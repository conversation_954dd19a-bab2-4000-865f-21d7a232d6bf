plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5' // use 3.2.5 for compatibility
    id 'io.spring.dependency-management' version '1.1.7'
}
group = 'com.keyanna'
version = '0.0.1-SNAPSHOT'
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}
repositories {
    mavenCentral()
}
dependencyManagement {
    imports {
        // Compatible Spring Cloud BOM for Spring Boot 3.2.5
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2023.0.1"
    }
}
dependencies {
    // Spring Boot starters
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    // Eureka Client
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'

    // Hibernate
    implementation 'org.hibernate.orm:hibernate-core:6.5.3.Final'
    implementation 'org.hibernate.orm:hibernate-spatial:6.5.3.Final'
    implementation 'org.liquibase:liquibase-core'

    // Swagger UI with Springdoc for Spring Boot 3+
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'
    implementation 'org.locationtech.jts:jts-core:1.19.0'
    // GeoTools (add version if needed)
    // implementation 'org.geotools:gt-geojson:26.0'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation "jakarta.persistence:jakarta.persistence-api"
    annotationProcessor "jakarta.persistence:jakarta.persistence-api"
    // Hibernate
    // Kafka dependencies (same as Common Gateway)
//    implementation 'org.apache.kafka:kafka-clients:2.8.0'
//    implementation 'org.springframework.kafka:spring-kafka:2.8.6'
//    testImplementation 'org.springframework.kafka:spring-kafka-test'
    implementation 'jakarta.persistence:jakarta.persistence-api:3.1.0'
    implementation 'org.javers:javers-spring-boot-starter-sql:6.14.0'
    implementation 'org.springframework.kafka:spring-kafka'
    //  implementation 'com.google.code.gson:gson'
    implementation 'com.google.code.gson:gson:2.10.1'

    // QueryDSL
    implementation 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
    annotationProcessor 'com.querydsl:querydsl-apt:5.0.0:jakarta'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    // PostgreSQL JDBC Driver
    runtimeOnly 'org.postgresql:postgresql'
    // DevTools (optional for hot reload in dev)
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    // Apache POI for Excel file handling
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'

    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

}
tasks.named('test') {
    useJUnitPlatform()
}