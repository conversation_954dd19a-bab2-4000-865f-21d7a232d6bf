[Tu<PERSON> May  6 12:21:27 2025].612000 msLoadMap(): General error message. Invalid DEBUG level, must be between 0 and 5 (line 3)
[<PERSON><PERSON> May  6 12:21:27 2025].612000 msFreeMap(): freeing map at 00D3BF48.
[Tu<PERSON> May  6 12:21:35 2025].366000 CGI Request 1 on process 21096
[Tu<PERSON> May  6 12:21:35 2025].366000 msWMSLoadGetMapParams(): enabling non-square pixels.
[Tu<PERSON> May  6 12:21:35 2025].368000 msDrawMap(): kicking into non-square pixel preserving mode.
[Tue May  6 12:21:35 2025].368000 msDrawMap(): rendering using outputformat named png (AGG/PNG).
[Tue May  6 12:21:35 2025].368000 msDrawMap(): WMS/WFS set-up and query, 0.000s
[Tu<PERSON> May  6 12:21:35 2025].429000 msDrawMap(): Layer 0 (Keyna-country-border), 0.061s
[Tu<PERSON> May  6 12:21:35 2025].429000 msDrawMap(): Drawing Label Cache, 0.000s
[<PERSON><PERSON> May  6 12:21:35 2025].429000 msDrawMap() total time: 0.062s
[Tue May  6 12:21:35 2025].447000 msSaveImage(stdout) total time: 0.018s
[Tue May  6 12:21:35 2025].447000 mapserv request processing time (msLoadMap not incl.): 0.081s
[Tue May  6 12:21:35 2025].447000 msFreeMap(): freeing map at 0173DAA0.
[Tue May  6 12:22:10 2025].465000 CGI Request 1 on process 18344
[Tue May  6 12:22:10 2025].465000 msWMSLoadGetMapParams(): enabling non-square pixels.
[Tue May  6 12:22:10 2025].466000 msDrawMap(): kicking into non-square pixel preserving mode.
[Tue May  6 12:22:10 2025].466000 msDrawMap(): rendering using outputformat named png (AGG/PNG).
[Tue May  6 12:22:10 2025].466000 msDrawMap(): WMS/WFS set-up and query, 0.000s
[Tue May  6 12:22:10 2025].466000 Database connection failed (missing "=" after "[PG_CONN_STRING]" in connection info string
) with connect string '[PG_CONN_STRING]'
Is the database running? Is it allowing connections? Does the specified user exist? Is the password valid? Is the database on the standard port? in msPostGISLayerOpen()[Tue May  6 12:22:10 2025].466000 msPostGISLayerOpen(): Query error. Database connection failed. Check server logs for more details.Is the database running? Is it allowing connections? Does the specified user exist? Is the password valid? Is the database on the standard port?
[Tue May  6 12:22:10 2025].466000 msDrawMap(): Image handling error. Failed to draw layer named 'Keyna-country-border'.
[Tue May  6 12:22:10 2025].467000 mapserv request processing time (msLoadMap not incl.): 0.002s
[Tue May  6 12:22:10 2025].467000 msFreeMap(): freeing map at 0071E1F0.
