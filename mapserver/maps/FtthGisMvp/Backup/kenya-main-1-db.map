MAP
#  CONFIG "MS_ERRORFILE" "../../logs/mapserver.log"
#   DEBUG 4

    IMAGETYPE      PNG
    IMAGECOLOR     255 255 255
    FONTSET        "../../fonts/fonts.txt"

    PROJECTION
      "init=epsg:4326"               # Coordinate system (WGS 84 - degrees)
    END
    
    WEB
      METADATA
        "wms_title" "My WMS Map"
        "wms_enable_request" "*"
        "wms_srs" "EPSG:4326 EPSG:3857"
        "wms_version" "1.3.0"
      END
    END
  
    # --- Layer 1 : Keyna country border ---
    LAYER
      NAME         "Keyna-country-border"
      STATUS       ON
      TYPE         POLYGON

      INCLUDE "../../config/dbpgconn.conf"
      DATA "geom FROM administrative_00 USING UNIQUE id USING SRID=4326"

      PROJECTION
        "init=epsg:4326"
      END

      METADATA
        "wms_title" "My Layer"
        "wms_enable_request" "*"
      END

      CLASS
        STYLE
            COLOR 72 233 23
            OUTLINECOLOR 32 32 32
        END
      END
    END # Layer 1 layer ends here

    # --- Layer 2 : Keyna counties ---
    LAYER
        NAME         "Keyna-counties"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
         DATA "geom FROM administrative_01 USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASSITEM "adm1_name"
        LABELITEM "adm1_name"

        CLASS
            NAME "[adm1_name]"  # Province names
            
            STYLE
              COLOR 204 229 255

              OUTLINECOLOR 0 0 0
            END
            
            LABEL
                COLOR 248 17 28
                SIZE SMALL
                OUTLINECOLOR 255 255 255
                POSITION CC
                PARTIALS FALSE
                FONT arial-bold
            END
        END
    END # Layer 2 layer ends here
    # End of LAYER DEFINITIONS -------------------------------
  
  END
