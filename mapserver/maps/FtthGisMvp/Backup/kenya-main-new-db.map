MAP
#  CONFIG "MS_ERRORFILE" "../../logs/mapserver.log"
#   DEBUG 4

    IMAGETYPE      PNG
    IMAGECOLOR     255 255 255
    SYMBOLSET      "../../symbols/symbols.txt"
#    FONTSET        "../../fonts/fonts.txt"

    PROJECTION
      "init=epsg:4326"               # Coordinate system (WGS 84 - degrees)
    END
    
    WEB
      METADATA
        "wms_title" "My WMS Map"
        "wms_enable_request" "*"
        "wms_srs" "EPSG:4326 EPSG:3857"
        "wms_version" "1.3.0"
      END
    END
  
    # --- Layer 1 : Keyna country border ---
    LAYER
      NAME         "Keyna-country-border"
      STATUS       ON
      TYPE         POLYGON

      INCLUDE "../../config/dbpgconn.conf"
      DATA "geom FROM vw_map_administrative_00 USING UNIQUE id USING SRID=4326"

      PROJECTION
        "init=epsg:4326"
      END

      METADATA
        "wms_title" "My Layer"
        "wms_enable_request" "*"
      END

      CLASS
        STYLE
            COLOR 72 233 23
            OUTLINECOLOR 32 32 32
        END
      END
    END # Layer 1 layer ends here

    # --- Layer 2 : Keyna counties ---
    LAYER
        NAME         "Keyna-counties"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_administrative_01 USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASS
            STYLE
              COLOR 204 229 255
              OUTLINECOLOR 0 0 0
            END            
        END
    END # Layer 2 layer ends here

    # --- Layer 3 : Keyna districts ---
    LAYER
        NAME         "Keyna-districts"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_administrative_02 USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASS
            STYLE
              COLOR 13 120 235
              OUTLINECOLOR 0 0 0
            END            
        END
    END # Layer 3 layer ends here

    # --- Layer 4 : Building ---
    LAYER
        NAME         "building"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_building USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASS
            STYLE
              COLOR 13 120 235
              OUTLINECOLOR 0 0 0
            END            
        END
    END # Layer 4 layer ends here

    # --- Layer 5 : cables lines ---
    LAYER
        NAME         "cables-lines"
        STATUS       ON
        TYPE         LINE
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_cables USING UNIQUE id USING SRID=4326"
  
        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASSITEM "cable_type"
  
        CLASS
          NAME "Road Lines"
          EXPRESSION 'Feeder'
          STYLE
            COLOR 0 0 255
            WIDTH 4
          END
        END
  
        CLASS
          NAME "Road Lines 1"
          EXPRESSION 'Drop'
          STYLE
            COLOR 255 0 255
            WIDTH 4
          END
        END
  
        CLASS
          NAME "Road Lines 2"
          EXPRESSION 'Distribution'
          STYLE
            COLOR 0 255 255
            WIDTH 4
          END
        END
    END # Layer 5 layer ends here

    # --- Layer 6 : customer points ---
    LAYER
        NAME "customer-points"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_customer USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 236 4 217
              SIZE 9
            END
        END
    END # Layer 6 layer ends here

    # --- Layer 7 : fat points ---
    LAYER
        NAME "fat"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_fat USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 250 68 255
              SIZE 9
            END
        END
    END # Layer 7 layer ends here

    # --- Layer 8 : fdp points ---
    LAYER
        NAME "fdp"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_fdp USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 250 68 255
              SIZE 9
            END
        END
    END # Layer 8 layer ends here

    # --- Layer 9 : pop points ---
    LAYER
        NAME "pop"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_pop USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 250 68 255
              SIZE 9
            END
        END
    END # Layer 9 layer ends here

    # --- Layer 10 : splitter points ---
    LAYER
        NAME "splitter"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_splitter USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 250 68 255
              SIZE 9
            END
        END
    END # Layer 10 layer ends here
    # End of LAYER DEFINITIONS -------------------------------
  
  END
