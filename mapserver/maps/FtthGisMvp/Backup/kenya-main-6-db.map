MAP
#  CONFIG "MS_ERRORFILE" "../../logs/mapserver.log"
#   DEBUG 4

#EXTENT 
#  BOX(77.6001 12.9711,77.6405 12.9755)
    IMAGETYPE      PNG
    IMAGECOLOR     255 255 255
    SYMBOLSET      "../../symbols/symbols.txt"

    PROJECTION
      "init=epsg:4326"               # Coordinate system (WGS 84 - degrees)
    END
    
    WEB
      METADATA
        "wms_title" "My WMS Map"
        "wms_enable_request" "*"
        "wms_srs" "EPSG:4326 EPSG:3857"
        "wms_version" "1.3.0"
      END
    END
  
    # --- Layer 1 : customer points ---
    LAYER
        NAME "customer-points"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_customer USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS            
            STYLE
               SYMBOL "circle"
              COLOR 47 13 236
              SIZE 9
            END
        END
    END # Layer 1 layer ends here
    # End of LAYER DEFINITIONS -------------------------------
  
  END
