MAP
#    CONFIG "MS_ERRORFILE" "../../logs/mapserver.log"
#    DEBUG 5

    IMAGETYPE      PNG
    IMAGECOLOR     255 255 255
    SYMBOLSET      "../../symbols/symbols.txt"
    FONTSET        "../../fonts/fonts.txt"

    PROJECTION
      "init=epsg:4326"               # Coordinate system (WGS 84 - degrees)
    END
    
    WEB
      METADATA
        "wms_title" "My WMS Map"
        "wms_enable_request" "*"
        "wms_srs" "EPSG:4326 EPSG:3857"
        "wms_version" "1.3.0"
      END
    END
  
    # --- Layer 1 : Keyna country border ---
    <PERSON>YER
      NAME         "Keyna-country-border"
      STATUS       ON
      TYPE         POLYGON

      INCLUDE "../../config/dbpgconn.conf"
      DATA "geom FROM vw_map_administrative_00 USING UNIQUE id USING SRID=4326"

      PROJECTION
        "init=epsg:4326"
      END

      METADATA
        "wms_title" "My Layer"
        "wms_enable_request" "*"
      END

      CLASS
        STYLE
            COLOR 72 233 23
            OUTLINECOLOR 32 32 32
        END
      END
    END # Layer 1 layer ends here

    # --- Layer 2 : Keyna counties ---
    LAYER
        NAME         "Keyna-counties"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_administrative_01 USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
        
        CLASS
            STYLE
                COLOR 12 230 66
                OUTLINECOLOR 32 32 32
            END
            
            LABEL
                TEXT "[adm1_name]"
                FONT arial
                TYPE truetype
                SIZE 10
                COLOR 235 9 28
                OUTLINECOLOR 255 255 255
                POSITION CC
                PARTIALS FALSE
                BUFFER 2
            END
        END # Class
    END # Layer 2 layer ends here

    # --- Layer 3 : Keyna districts ---
    LAYER
        NAME         "Keyna-districts"
        STATUS       ON
        TYPE         POLYGON
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_administrative_02 USING UNIQUE id USING SRID=4326"

        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASS
            STYLE
              COLOR 13 120 235
              OUTLINECOLOR 0 0 0
            END            
        END
    END # Layer 3 layer ends here

    # --- Layer 4 : Building ---
    LAYER
        NAME "building"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_building USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'buildingicon.png')
            STYLE
                SYMBOL "building"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 4 layer ends here

    # --- Layer 5 : cables ---
    LAYER
        NAME         "cables"
        STATUS       ON
        TYPE         LINE
  
        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_cable USING UNIQUE id USING SRID=4326"
  
        PROJECTION
          "init=epsg:4326"
        END
  
        METADATA
          "wms_title" "My Layer"
          "wms_enable_request" "*"
        END
  
        CLASSITEM "cable_type"
  
        CLASS
          NAME "Road Lines"
          EXPRESSION 'Feeder'
          STYLE
            COLOR 0 0 255
            WIDTH 4
          END
        END
  
        CLASS
          NAME "Road Lines 1"
          EXPRESSION 'Drop'
          STYLE
            COLOR 255 0 255
            WIDTH 4
          END
        END
  
        CLASS
          NAME "Road Lines 2"
          EXPRESSION 'Distribution'
          STYLE
            COLOR 0 255 255
            WIDTH 4
          END
        END
    END # Layer 5 layer ends here

    # --- Layer 6 : customer points ---
    LAYER
        NAME "customer"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_customer USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'customericon.png')
            STYLE
                SYMBOL "customer"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 6 layer ends here

    # --- Layer 7 : fat points ---
    LAYER
        NAME "fat"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_fat USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
#            STYLE
#                COLOR 12 230 66
#                OUTLINECOLOR 32 32 32
#            END
        
            CLASS
                EXPRESSION ('[icon_path]' eq 'faticon.png')
                STYLE
                    SYMBOL "fat"
                    #COLOR 255 7 89
                    SIZE 16
                END
              
#            LABEL
#                TEXT "[name]"
#                FONT arial-bold
#                TYPE truetype
#                SIZE 9
#                COLOR 235 9 28
#                OUTLINECOLOR 255 255 255
#                POSITION AUTO
#                PARTIALS TRUE
#                FORCE TRUE
#                BUFFER 2
#            END
        END
    END # Layer 7 layer ends here

    # --- Layer 8 : fdc points ---
    LAYER
        NAME "fdc"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_fdc USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'fdcicon.png')
            STYLE
               SYMBOL "fdc"
              COLOR 250 68 255
              SIZE 16
            END
        END

        CLASS
            EXPRESSION ('[icon_path]' eq 'star.png')
            STYLE
               SYMBOL "star"
              COLOR 250 68 255
              SIZE 16
            END
        END
    END # Layer 8 layer ends here

    # --- Layer 9 : fdp points ---
    LAYER
        NAME "fdp"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_fdp USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'fdpicon.png')
            STYLE
               SYMBOL "fdp"
              #COLOR 255 7 89
              SIZE 16
            END
        END

        CLASS
            EXPRESSION ('[icon_path]' eq 'star.png')
            STYLE
               SYMBOL "star"
              COLOR 250 68 255
              SIZE 16
            END
        END
    END # Layer 9 layer ends here

    # --- Layer 10 : pop points ---
    LAYER
        NAME "pop"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_pop USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'popicon.png')
            STYLE
                SYMBOL "pop"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 10 layer ends here

    # --- Layer 11 : splitter points ---
    LAYER
        NAME "splitter"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_splitter USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'splittericon.png')
            STYLE
                SYMBOL "splitter"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 11 layer ends here

# --- Layer 12 : pole points ---
    LAYER
        NAME "pole"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_pole USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        

           CLASS
            EXPRESSION ('[icon_path]' eq 'pole.png')
            STYLE
                SYMBOL "pole"
                #COLOR 255 7 89
                SIZE 16
            END
        END
       
    END # Layer 12 layer ends here


# --- Layer 13 : joint closure points ---
    LAYER
        NAME "jointclosure"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_joint_closure USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'jointclosure.png')
            STYLE
                SYMBOL "jointclosure"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 13 layer ends here

 # --- Layer 14 : trench lines ---
    LAYER
        NAME "trench"
        STATUS ON
        TYPE LINE

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_trench USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "My Layer"
            "wms_enable_request" "*"
        END
        
          CLASS
            EXPRESSION ('[icon_path]' eq 'trench.png')
          STYLE
            COLOR 255 0 255
            WIDTH 4
          END
        END
     END

# --- Layer 15 : duct lines ---
    LAYER
        NAME "duct"
        STATUS ON
        TYPE LINE

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_duct USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "My Layer"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'duct.png')
            STYLE
                 COLOR 255 0 255
            WIDTH 4
            END
        END
    END # Layer 15 layer ends here

# --- Layer 16 : handhole points ---
    LAYER
        NAME "handhole"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_handhole USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'handhold.png')
            STYLE
                SYMBOL "handhole"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 16 layer ends here

# --- Layer 17 : manhole points ---
    LAYER
        NAME "manhole"
        STATUS ON
        TYPE POINT

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_ne_manhole USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "points"
            "wms_enable_request" "*"
        END
        
        CLASS
            EXPRESSION ('[icon_path]' eq 'manhole.png')
            STYLE
                SYMBOL "manhole"
                #COLOR 255 7 89
                SIZE 16
            END
        END
    END # Layer 17 layer ends here

     # --- Layer 18 : Survey area polygon ---
     LAYER
        NAME "surveyarea"
        STATUS ON
        TYPE POLYGON

        INCLUDE "../../config/dbpgconn.conf"
        DATA "geom FROM vw_map_survey_area USING UNIQUE id USING SRID=4326"
    
        PROJECTION
            "init=epsg:4326"
        END

        METADATA
            "wms_title" "My Layer"
            "wms_enable_request" "*"
        END
        
        CLASS
            STYLE
                COLOR 42 7 240
                OUTLINECOLOR 0 0 0
                WIDTH 2
            END
        END
     END # Layer 18 layer ends here
    # End of LAYER DEFINITIONS -------------------------------
  
  END
