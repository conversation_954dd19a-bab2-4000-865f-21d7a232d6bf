package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.CableSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.CableSpecificationResponseDTO;
import com.keyanna.gis.core.model.CableSpecification;

import java.util.List;

public interface CableSpecificationService {

    void createCableSpecification(CableSpecificationRequestDTO cableSpecificationRequestDTO);

    CableSpecification updateCableSpecification(Integer id, CableSpecificationRequestDTO updatedCableSpecificationRequestDTO);

    List<CableSpecificationResponseDTO> getAllCableSpecification();

    void deleteCableSpecificationById(Integer id);

}
