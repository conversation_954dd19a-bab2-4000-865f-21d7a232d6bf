package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.DuctDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Duct;
import com.keyanna.gis.core.repository.DuctRepository;
import com.keyanna.gis.core.service.DuctService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class DuctServiceImpl implements DuctService {

    private static final Logger logger = LoggerFactory.getLogger(DuctServiceImpl.class);
    private final DuctRepository ductRepository;
    private final SpatialValidator spatialValidator;

    public DuctServiceImpl(DuctRepository ductRepository, SpatialValidator spatialValidator) {
        this.ductRepository = ductRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Duct create(DuctDTO dto) throws ParseException {

        Duct duct = new Duct();
        duct.setPublicId(UUID.randomUUID());
        duct.setName(dto.getName());
        duct.setMaterial(dto.getMaterial());
        duct.setDiameterMm(dto.getDiameterMm());
        duct.setMeasuredLengthM(dto.getMeasuredLengthM());
        duct.setStatus(dto.getStatus());
        duct.setInstallDate(dto.getInstallDate());
        duct.setOwner(dto.getOwner());
        duct.setStartNodeId(dto.getStartNodeId());
        duct.setEndNodeId(dto.getEndNodeId());
        duct.setNumSubducts(dto.getNumSubducts());
        duct.setSurveyAreaId(dto.getSurveyAreaId());
        duct.setUsedSubducts(dto.getUsedSubducts());
        duct.setParentNeId(dto.getParentNeId());
        duct.setParentNeType(dto.getParentNeType());

        duct.setNetworkType(dto.getNetworkType());
        duct.setRemarks(dto.getRemarks());

        if (dto.getGeom() != null) {
            duct.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(dto.getGeom()));
        }

        if (!spatialValidator.isValidGeometry(duct.getGeom().toText())) {
            throw new RuntimeException("Invalid geometry data");
        }

        duct.setMvnoId(dto.getMvnoId());
        duct.setCreatedBy(dto.getUserId());
        duct.setCreatedOn(LocalDateTime.now());

        ductRepository.save(duct);

        return duct;
    }

    @Override
    public List<Duct> getAllDuct(Integer mvnoId) {

        try {

            return ductRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return Collections.emptyList(); // or throw a custom runtime exception
        }

    }

    @Override
    public void updateDuct(UUID publicId, DuctDTO ductdto) {

        try {
            Duct duct = ductRepository.findByPublicId(publicId)
                    .orElseThrow(() -> new RuntimeException("Duct not found"));

            duct.setName(ductdto.getName());
            duct.setMaterial(ductdto.getMaterial());
            duct.setDiameterMm(ductdto.getDiameterMm());
            duct.setMeasuredLengthM(ductdto.getMeasuredLengthM());
            duct.setStatus(ductdto.getStatus());
            duct.setInstallDate(ductdto.getInstallDate());
            duct.setOwner(ductdto.getOwner());
            duct.setSurveyAreaId(ductdto.getSurveyAreaId());

            duct.setStartNodeId(ductdto.getStartNodeId());
            duct.setEndNodeId(ductdto.getEndNodeId());
            duct.setNumSubducts(ductdto.getNumSubducts());
            duct.setUsedSubducts(ductdto.getUsedSubducts());

            duct.setNetworkType(ductdto.getNetworkType());
            duct.setRemarks(ductdto.getRemarks());

            duct.setMvnoId(ductdto.getMvnoId());
            duct.setModifiedBy(ductdto.getUserId());
            duct.setModifiedOn(LocalDateTime.now());

            if (ductdto.getGeom() != null) {
                duct.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(ductdto.getGeom()));
            }

            if (!spatialValidator.isValidGeometry(duct.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            ductRepository.save(duct);
        } catch (Throwable e) {
            throw new RuntimeException("Duct not found"); // or throw a custom runtime exception
        }
    }

    public Duct getDuctByPublicId(UUID publicId, Integer mvnoId) {
        if (!ductRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Duct with public Id not found : " + publicId);
        }

        try {
            Optional<Duct> entity = ductRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting duct data method getDuctByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get duct data method getDuctByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        if (!ductRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Duct with public Id not found : " + publicId);
        }

        try {
            ductRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            //    logger.error("Error deleting duct with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete duct: " + ex.getMessage(), ex);
        }

    }
}
