package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.PopRequestDTO;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.model.Pop;

import java.util.List;
import java.util.UUID;

public interface PopService {

    void createPop(PopRequestDTO popRequestDTO);

    void updatePop(UUID publicId, PopRequestDTO updatedPopRequestDTO);

    void deletePopByPublicId(UUID publicId,Integer mvnoId);

    List<PopResponseDTO> getAllPops(boolean withGeometry, String userTimeZone,Integer mvnoId);

    Pop getNePopById(UUID publicId,Integer mvnoId);

//    List<PopResponseDTO> getAllPops0();

}
