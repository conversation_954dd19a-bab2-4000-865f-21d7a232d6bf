package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.RoleACLEntryDTO;
import com.keyanna.gis.core.dto.RoleDTO;
import com.keyanna.gis.core.model.Role;
import com.keyanna.gis.core.model.RoleACLEntry;
import com.keyanna.gis.core.repository.RoleRepository;
import com.keyanna.gis.core.service.RoleService;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleRepository roleRepository;

    @Transactional
    @Override
    public void saveFromKafka(RoleDTO dto) {
        if (dto == null) {
            log.error("Received null RoleDTO from Kafka");
            return;
        }

        log.info("Received RoleDTO from Kafka: {}", dto);

        Role role = (dto.getId() != null && roleRepository.existsById(dto.getId()))
                ? roleRepository.findById(dto.getId()).orElseGet(Role::new)
                : new Role();

        role.setId(dto.getId());
        role.setRolename(dto.getRolename());
        role.setStatus(dto.getStatus());
        role.setSysRole(dto.getSysRole());
        role.setMvnoId(dto.getMvnoId() != null ? dto.getMvnoId() : 0);
        role.setLcoId(dto.getLcoId() != null ? dto.getLcoId() : 0);
        role.setProduct(dto.getProduct());
        role.setIsDelete(Boolean.TRUE.equals(dto.getIsDelete()));
        role.setCreatedByName(dto.getCreatedByName());
        role.setLastModifiedByName(dto.getLastModifiedByName());
        role.setCreatedById(dto.getCreatedById());
        role.setLastModifiedById(dto.getLastModifiedById());

        // Handle ACL entries
        if (role.getRoleAclEntry() != null) {
            role.getRoleAclEntry().clear();
        } else {
            role.setRoleAclEntry(new ArrayList<>());
        }

        if (dto.getAclMenu() != null && !dto.getAclMenu().isEmpty()) {
            for (RoleACLEntryDTO aclDto : dto.getAclMenu()) {
                RoleACLEntry aclEntry = new RoleACLEntry();
                aclEntry.setMenuid(aclDto.getMenuid());
                aclEntry.setCode(aclDto.getCode());
                aclEntry.setProduct(aclDto.getProduct());
                aclEntry.setRole(role);
                role.getRoleAclEntry().add(aclEntry);
            }
        }


        roleRepository.save(role);
        log.info("Role saved/updated successfully with ID [{}]", role.getRolename(), role.getId());
    }

}
