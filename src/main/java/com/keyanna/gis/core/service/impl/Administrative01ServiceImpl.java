package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.Administrator01DTO;
import com.keyanna.gis.core.model.Administrative01;
import com.keyanna.gis.core.repository.Administrative01Repository;
import com.keyanna.gis.core.utility.Administrator01DtoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class Administrative01ServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(Administrative01ServiceImpl.class);

    private final Administrative01Repository administrative01Repository;

    public Administrative01ServiceImpl(Administrative01Repository administrative01Repository) {
        this.administrative01Repository = administrative01Repository;
    }

    public Optional<Administrative01> getUserById(Long id) {
        try {
            return administrative01Repository.findById(id);
        } catch (Exception ex) {
            logger.error("Error in getUserById: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to fetch user by ID");
        }
    }


    public Administrator01DTO getSingleByAdm0Code(String adm0Code) {
        try {
        List<Object[]> rows = administrative01Repository.findSelectedFieldsByAdm0Code(adm0Code);
        if (rows.isEmpty()) {
            return null;
        }
        return Administrator01DtoMapper.map(rows.get(0));
    } catch(Exception ex)
        {
        logger.error("Error in getSingleByAdm0Code: " + ex.getMessage(), ex);
        throw new RuntimeException("Failed to fetch single record by Adm0Code");
    }
}

    public List<Administrator01DTO> getAllByAdm0Code(String adm0Code) {
        try {
            List<Object[]> rows = administrative01Repository.findSelectedFieldsByAdm0Code(adm0Code);
            return Administrator01DtoMapper.mapList(rows);
        } catch (Exception ex) {
            logger.error("Error in getAllByAdm0Code: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to fetch list by Adm0Code");
        }
    }
}

