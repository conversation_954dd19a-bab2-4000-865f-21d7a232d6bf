package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.FatDTO;
import com.keyanna.gis.core.dto.response.FatWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.repository.FatRepository;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.service.FatService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_FAT;

@Service
public class FatServiceImpl implements FatService {

    private static final Logger logger = LoggerFactory.getLogger(FatServiceImpl.class);
    private final FatRepository fatRepository;
    private final CommonUtilComponent commonUtilComponent;
    private final SpatialValidator spatialValidator;

    private final ImageServiceImpl imageServiceImpl;
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;

    public FatServiceImpl(FatRepository fatRepository, ImageServiceImpl imageServiceImpl, LayerImageMappingRepository layerImageMappingRepository, LayerInventoryMappingRepository layerInventoryMappingRepository, CommonUtilComponent commonUtilComponent, SpatialValidator spatialValidator) {
        this.fatRepository = fatRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.commonUtilComponent = commonUtilComponent;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Fat create(FatDTO dto) throws ParseException {
        try {
            // Validation call Point is Within SurveyArea or not
            spatialValidator.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Fat fat = new Fat();
            fat.setPublicId(UUID.randomUUID()); // Generate UUID here
            fat.setName(dto.getName());
            fat.setCapacity(dto.getCapacity());
            fat.setAddress(dto.getAddress());
            fat.setGeom(point);
            fat.setStatus(dto.getStatus());

            fat.setParentNeId(dto.getParentNeId());
            fat.setParentNeType(dto.getParentNeType());
            fat.setMvnoId(dto.getMvnoId());
            fat.setSurveyAreaId(dto.getSurveyAreaId());
            //     fatLayer.setPhoto(dto.photo);
            fat.setRemarks(dto.getRemarks());
            fat.setPowerLevels(dto.getPowerLevels());
            fat.setCreatedBy(dto.getUserId());
            fat.setCreatedOn(LocalDateTime.now());

            Fat savedFat = fatRepository.save(fat);

            // Create Layer Inventory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedFat.getId(), ApiConstants.LAYER_CODE_FAT,dto.getStatus());
            }

            return savedFat;
        } catch (Exception ex) {
            logger.error("Error creating fat {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create fat: " + ex.getMessage(), ex);
        }
    }


    public Fat updateFat(UUID publicId, FatDTO updatedFatRequestDTO) {
        Fat fat = fatRepository.findByPublicId(publicId).orElseThrow(() -> new RuntimeException("Fat not found"));

        spatialValidator.validatePointWithinSurveyArea(
                updatedFatRequestDTO.getSurveyAreaId(),
                updatedFatRequestDTO.getLongitude(),
                updatedFatRequestDTO.getLatitude()
        );

        try {
            // Remove old mappings
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(fat.getId(), LABEL_FAT);

            fat.setName(updatedFatRequestDTO.getName());
            fat.setCapacity(updatedFatRequestDTO.getCapacity());
            fat.setAddress(updatedFatRequestDTO.getAddress());
            fat.setStatus(updatedFatRequestDTO.getStatus());
            fat.setModifiedBy(updatedFatRequestDTO.getUserId());
            fat.setModifiedOn(LocalDateTime.now());

            fat.setParentNeId(updatedFatRequestDTO.getParentNeId());
            fat.setParentNeType(updatedFatRequestDTO.getParentNeType());
            fat.setMvnoId(updatedFatRequestDTO.getMvnoId());
            fat.setSurveyAreaId(updatedFatRequestDTO.getSurveyAreaId());
            //     fatDTO.setPhoto(dto.photo);
            fat.setRemarks(updatedFatRequestDTO.getRemarks());
            fat.setPowerLevels(updatedFatRequestDTO.getPowerLevels());

            Point location = GeoJsonConverterUtil.createPointGeom(updatedFatRequestDTO.getLongitude(), updatedFatRequestDTO.getLatitude());
            location.setSRID(4326);
            fat.setGeom(location);

            Fat savedFat = fatRepository.save(fat);

            // Create Layer Inventory Mapping mappings
            if (updatedFatRequestDTO.getInventoryList() != null && !updatedFatRequestDTO.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(updatedFatRequestDTO.getInventoryList(), savedFat.getId(), ApiConstants.LAYER_CODE_FAT,updatedFatRequestDTO.getStatus());
            }

        } catch (Exception ex) {
            logger.error("Error updating fat : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update fat");
        }
        return fat;
    }

    public List<Fat> getAllFat(Integer mvnoId) {
        try {
            return fatRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    public FatWithImageDTO findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId) {
        if (!fatRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Fat with public Id not found : " + publicId);
        }

        try {
            Optional<Fat> entity = fatRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Fat fat = entity.get();
            List<LayerImageMapping> layerImageMappingData = layerImageMappingRepository.findAllByLayerIdAndLayerCode(fat.getId(), LABEL_FAT);
            List<LayerInventoryMapping> accessoriesList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(fat.getId(), LABEL_FAT);
            return new FatWithImageDTO(fat, layerImageMappingData, accessoriesList);
        } catch (Exception ex) {
            logger.error("Error getting fat data method getFatByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get fat data method getFatByPublicId: " + ex.getMessage(), ex);
        }
    }

//    public FatWithImageDTO getFatByPublicId(UUID publicId, Integer mvnoId) {
//        try {
//            // Find by both publicId and mvnoId
//            Optional<Fat> entity = fatRepository.findByPublicId(publicId, mvnoId);
//
//            if (entity.isEmpty()) {
//                throw new EntityNotFoundException("Fat not found with publicId: " + publicId + " and mvnoId: " + mvnoId);
//            }
//
//            Fat fat = entity.get();
//
//            List<LayerImageMapping> layerImageMappingData =
//                    layerImageMappingRepository.findAllByLayerIdAndLayerCode(fat.getId(), LABEL_FAT);
//
//            List<LayerInventoryMapping> accessoriesList =
//                    layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(fat.getId(), LABEL_FAT);
//
//            return new FatWithImageDTO(fat, layerImageMappingData, accessoriesList);
//
//        } catch (Exception ex) {
//            logger.error("Error in getFatByPublicIdAndMvnoId: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to get fat data: " + ex.getMessage(), ex);
//        }
//    }


    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Fat> optionalFat = fatRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Fat fat = optionalFat.orElseThrow(() ->
                new EntityNotFoundException("Fat with public Id not found")
        );

        Integer fatId = fat.getId(); // Primary key in ne_fat

        try {
            // Step 1: Delete mappings from layer_inventory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(fatId, LABEL_FAT);

            // Step 2: Remove images from mapping.
            commonUtilComponent.deleteAllAssociatedImageWithNetworkElement(fatId, LABEL_FAT);

            // Step 3: Delete the fat itself
            fatRepository.delete(fat);

        } catch (Exception ex) {
            logger.error("Error deleting fat, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete fat and related data: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Fat createWithImage(FatDTO dto, List<MultipartFile> imageFiles) throws ParseException {
        try {
            // Validation call Point is Within SurveyArea or not
            spatialValidator.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Fat fat = new Fat();
            fat.setPublicId(UUID.randomUUID()); // Generate UUID here
            fat.setName(dto.getName());
            fat.setCapacity(dto.getCapacity());
            fat.setAddress(dto.getAddress());
            fat.setGeom(point);
            fat.setStatus(dto.getStatus());

            fat.setParentNeId(dto.getParentNeId());
            fat.setParentNeType(dto.getParentNeType());
            fat.setMvnoId(dto.getMvnoId());
            fat.setSurveyAreaId(dto.getSurveyAreaId());
            //     fatLayer.setPhoto(dto.photo);
            fat.setRemarks(dto.getRemarks());
            fat.setPowerLevels(dto.getPowerLevels());
            fat.setCreatedBy(dto.getUserId());
            fat.setCreatedOn(LocalDateTime.now());

            Fat savedFat = fatRepository.save(fat);

            commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedFat.getId(), ApiConstants.LAYER_CODE_FAT,dto.getStatus());

            // Create Layer Inventory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedFat.getId(), ApiConstants.LAYER_CODE_FAT,dto.getStatus());
            }

            //Save data in layer image mapping.
            if (imageFiles != null && !imageFiles.isEmpty()) {
                commonUtilComponent.saveLayerImageMappingData(imageFiles, ApiConstants.LAYER_CODE_FAT, savedFat.getId());
            }
            return savedFat;
        } catch (Exception ex) {
            logger.error("Error creating fat {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create fat: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Map<String, Object> previewImageByName(String fileName) {
        try {
            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
            if (!optionalLayerImageMapping.isPresent()) {
                throw new RuntimeException("No images found for fileName=" + fileName);
            }

            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());

            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            Map<String, Object> map = new HashMap<>();
            map.put("resources", resource); // resources removed and same contentTypes also
            map.put("contentTypes", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
