package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.CableRequestDTO;
import com.keyanna.gis.core.dto.response.CableResponseDTO;
import com.keyanna.gis.core.model.Cable;

import java.util.List;
import java.util.UUID;

public interface CableService {

    void createCable(CableRequestDTO cableRequestDTO);

    void updateCable(UUID publicId, CableRequestDTO updatedCableRequestDTO);

    List<CableResponseDTO> getAllCables(boolean withGeometry, String userTimeZone,Integer mvnoId);

//    List<CableResponseDTO> getAllCables();

    void deleteCableByPublicId(UUID publicId,Integer mvnoId);

    Cable getCableByPublicId(UUID publicId,Integer mvnoId);

}
