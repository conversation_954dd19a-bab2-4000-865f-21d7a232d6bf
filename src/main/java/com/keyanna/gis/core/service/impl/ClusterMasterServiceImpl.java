package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.ClusterMasterRequestDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.ClusterMaster;
import com.keyanna.gis.core.repository.ClusterMasterRepository;
import com.keyanna.gis.core.service.ClusterMasterService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Service
public class ClusterMasterServiceImpl implements ClusterMasterService {

    private static final Logger logger = LoggerFactory.getLogger(ClusterMasterServiceImpl.class);
    private final ClusterMasterRepository clusterMasterRepository;

    public ClusterMasterServiceImpl(ClusterMasterRepository clusterMasterRepository) {
        this.clusterMasterRepository = clusterMasterRepository;
    }

    @Transactional
    public void createClusterMaster(ClusterMasterRequestDTO clusterMasterRequestDTO) {
        try {
            ClusterMaster clusterMaster = new ClusterMaster();
            clusterMaster.setPublicId(UUID.randomUUID()); // Generate UUID here
            clusterMaster.setName(clusterMasterRequestDTO.getName());
            clusterMaster.setDescription(clusterMasterRequestDTO.getDescription());
            clusterMaster.setStatus(clusterMasterRequestDTO.getStatus());

            clusterMaster.setGeom(GeoJsonConverterUtil.convertGeometryToPolygon(clusterMasterRequestDTO.getGeom()));
            clusterMaster.setIsActive(clusterMasterRequestDTO.getIsActive());
            clusterMaster.setMvnoId(clusterMasterRequestDTO.getMvnoId());

            clusterMaster.setCreatedOn(Instant.now());
            clusterMaster.setCreatedBy(clusterMasterRequestDTO.getUserId());

            clusterMasterRepository.save(clusterMaster);    //  Save to DB

        } catch (DataIntegrityViolationException ex) {
            logger.error("Cluster Master with the same name already exists {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Cluster Master with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating clusterMaster {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create clusterMaster: " + ex.getMessage(), ex);
        }
    }

    public void updateClusterMaster(UUID publicId, ClusterMasterRequestDTO updatedClusterMasterRequestDTO,Integer mvnoId) {
        ClusterMaster clusterMaster = clusterMasterRepository.findByPublicIdAndMvnoId(publicId,mvnoId)
                .orElseThrow(() -> new EntityNotFoundException("ClusterMaster with ID " + publicId + " not found."));

        try {
            clusterMaster.setName(updatedClusterMasterRequestDTO.getName());
            clusterMaster.setName(updatedClusterMasterRequestDTO.getName());
            clusterMaster.setDescription(updatedClusterMasterRequestDTO.getDescription());
            clusterMaster.setStatus(updatedClusterMasterRequestDTO.getStatus());

            clusterMaster.setGeom(GeoJsonConverterUtil.convertGeometryToPolygon(updatedClusterMasterRequestDTO.getGeom()));
            clusterMaster.setIsActive(updatedClusterMasterRequestDTO.getIsActive());
            clusterMaster.setMvnoId(updatedClusterMasterRequestDTO.getMvnoId());

            clusterMaster.setModifiedBy(updatedClusterMasterRequestDTO.getUserId());
            clusterMaster.setModifiedOn(Instant.now());    //  modified_on

            clusterMasterRepository.save(clusterMaster); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating clusterMaster {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update clusterMaster");
        }
    }

    @Transactional
    public void deleteClusterMasterByPublicId(UUID publicId,Integer mvnoId) {
        if (!clusterMasterRepository.existsByPublicIdAndMvnoId(publicId,mvnoId)) {
            throw new EntityNotFoundException("ClusterMaster with public Id not found : " + publicId);
        }

        try {
            clusterMasterRepository.deleteByPublicIdAndMvnoId(publicId,mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting clusterMaster with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete clusterMaster: " + ex.getMessage(), ex);
        }
    }

    public ClusterMaster getClusterMasterByPublicId(UUID publicId,Integer mvnoId) {
        if (!clusterMasterRepository.existsByPublicIdAndMvnoId(publicId,mvnoId)) {
            throw new EntityNotFoundException("ClusterMaster with public Id not found : " + publicId);
        }

        try {
            Optional<ClusterMaster> entity = clusterMasterRepository.findByPublicIdAndMvnoId(publicId,mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting data method getClusterMasterDataById {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data method getClusterMasterById: " + ex.getMessage(), ex);
        }
    }
}