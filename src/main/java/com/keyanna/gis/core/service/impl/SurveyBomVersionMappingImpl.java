package com.keyanna.gis.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.model.*;
import com.keyanna.gis.core.repository.*;
import com.keyanna.gis.core.service.SurveyBomVersionMappingService;
import com.keyanna.gis.core.utility.component.BomExcelGenerate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static com.keyanna.gis.core.constants.ApiConstants.*;
import static com.keyanna.gis.core.constants.ApiConstants.LAYER_CODE_TRENCH;
import static org.springframework.data.jpa.domain.AbstractAuditable_.createdBy;
@Service
@Slf4j
public class SurveyBomVersionMappingImpl implements SurveyBomVersionMappingService
{

    private final CommonRepository commonRepository;
    //    private final ObjectMapper objectMapper;
//    private final RepositoryRegistry repositoryRegistry;
//    private final LayerMasterRepository layerMasterRepository;
//    private final SurveyAreaRepository surveyAreaRepository;
//        private final BomExcelGenerate bomExcelGenerate;
    private final CableRepository cableRepository;
    private final CduRepository cduRepository;
    private final CustomerRepository customerRepository;
    private final DuctRepository ductRepository;
    private final FatRepository fatRepository;
    private final FdpRepository fdpRepository;
    private final FdtRepository fdtRepository;
    private final HandholeRepository handholeRepository;
    private final JointClosureRepository jointClosureRepository;
    private final ManholeRepository manholeRepository;
    private final MduRepository mduRepository;
    private final OdfRepository odfRepository;
    private final OltRepository oltRepository;
    private final PoleRepository poleRepository;
    private final PopRepository popRepository;
    private final SduRepository sduRepository;
    private final StreetRepository streetRepository ;
    private final SplitterRepository splitterRepository;
    private final TrenchLayerRepository trenchRepository;

    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final SurveyBomVersionMappingRepository surveyBomVersionMappingRepository;




    public SurveyBomVersionMappingImpl(CommonRepository commonRepository, CableRepository cableRepository, CduRepository cduRepository, CustomerRepository customerRepository, DuctRepository ductRepository, FatRepository fatRepository, FdpRepository fdpRepository, FdtRepository fdtRepository, HandholeRepository handholeRepository, JointClosureRepository jointClosureRepository, LayerInventoryMappingRepository layerInventoryMappingRepository, ManholeRepository manholeRepository, MduRepository mduRepository, OdfRepository odfRepository, OltRepository oltRepository, PoleRepository poleRepository, PopRepository popRepository, SduRepository sduRepository, SplitterRepository splitterRepository,  SurveyBomVersionMappingRepository surveyBomVersionMappingRepository, TrenchLayerRepository trenchRepository, StreetRepository streetRepository) {
//    SurveyAreaRepository surveyAreaRepository, LayerMasterRepository layerMasterRepository

        this.commonRepository = commonRepository;
        this.cableRepository = cableRepository;
        this.cduRepository = cduRepository;
        this.customerRepository = customerRepository;
        this.ductRepository = ductRepository;
        this.fatRepository = fatRepository;
        this.fdpRepository = fdpRepository;
        this.fdtRepository = fdtRepository;
        this.handholeRepository = handholeRepository;
        this.jointClosureRepository = jointClosureRepository;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
//        this.layerMasterRepository = layerMasterRepository;
        this.manholeRepository = manholeRepository;
        this.mduRepository = mduRepository;
//        this.objectMapper = objectMapper;
        this.odfRepository = odfRepository;
        this.oltRepository = oltRepository;
        this.poleRepository = poleRepository;
        this.popRepository = popRepository;
        this.sduRepository = sduRepository;
        this.splitterRepository = splitterRepository;
//        this.surveyAreaRepository = surveyAreaRepository;
        this.surveyBomVersionMappingRepository = surveyBomVersionMappingRepository;
        this.trenchRepository = trenchRepository;
        this.streetRepository = streetRepository;
    }


    public void createSurveyBomMapping(Integer surveyAreaId,
                                       Integer bomVersionId,
                                       Integer layerInventoryMappingId,
                                       String createdBy,
                                       Integer neId,
                                       String neType,
                                       String status) {
        try {
            SurveyBomVersionMapping mapping = new SurveyBomVersionMapping();
            mapping.setSurveyId(surveyAreaId);
            mapping.setBomVersionId(bomVersionId);
            mapping.setLayerInventoryMappingId(layerInventoryMappingId);
            mapping.setCreatedBy(createdBy);
            mapping.setCreatedAt(LocalDateTime.now());
            mapping.setUpdatedBy(createdBy);
            mapping.setUpdatedAt(LocalDateTime.now());

            switch (neType) {
                case LABEL_CABLE -> mapping.setNeCableId(neId);
                case LABEL_CDU -> mapping.setNeCduId(neId);
                case LABEL_CUSTOMER -> mapping.setNeCustomerId(neId);
                case LABEL_DUCT -> mapping.setNeDuctId(neId);
                case LABEL_FAT -> mapping.setNeFatId(neId);
                case LABEL_FDP -> mapping.setNeFdpId(neId);
                case LABEL_FDT -> mapping.setNeFdtId(neId);
                case LABEL_HANDHOLE -> mapping.setNeHandholeId(neId);
                case LABEL_JOINT_CLOSURE -> mapping.setNeJointClosureId(neId);
                case LABEL_MANHOLE -> mapping.setNeManholeId(neId);
                case LABEL_MDU -> mapping.setNeMduId(neId);
                case LABEL_POLE -> mapping.setNePoleId(neId);
                case LABEL_POP -> mapping.setNePopId(neId);
                case LABEL_SDU -> mapping.setNeSduId(neId);
                case LABEL_SPLITTER -> mapping.setNeSplitterId(neId);
                case LABEL_STREET -> mapping.setNeStreetId(neId);
                case LABEL_TRENCH -> mapping.setNeTrenchId(neId);
                case LABEL_OLT -> mapping.setNeOltId(neId);
                case LABEL_ODF -> mapping.setNeOdfId(neId);
                default -> throw new IllegalArgumentException("Unsupported NE type: " + neType);
            }

            mapping.setStatus(status);

            surveyBomVersionMappingRepository.save(mapping);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid NE type provided: {}", neType, e);
            throw e; // or return/handle accordingly
        } catch (Exception e) {
            log.error("Error while creating SurveyBomVersionMapping for NE ID: {} and type: {}", neId, neType, e);
            throw new RuntimeException("Failed to create SurveyBomVersionMapping", e);
        }
    }


    @Transactional
    public void insertToSurveyBomVersionMapping(Integer surveyAreaId,
                                                Integer bomVersionId,
                                                String createdBy,
                                                Integer mvnoid)
    {
        try {

            // Cable
            if (cableRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Cable> cables = cableRepository.existsForSurveyArea(surveyAreaId, mvnoid);

                for (Cable c : cables) {
                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_CABLE, c.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, c.getId(), LABEL_CABLE, c.getStatus()
                        );
                    }
                }
            }

            // CDU
            if (cduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Cdu> cdus = cduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Cdu c : cdus) {

                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_CDU, c.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, c.getId(), LABEL_CDU, c.getStatus()
                        );
                    }
                }
            }

            // Customer
            if (customerRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Customer> customers = customerRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Customer c : customers) {

                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_CUSTOMER, c.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, c.getId(), LABEL_CUSTOMER, c.getStatus()
                        );
                    }
                }
            }

            // Duct
            if (ductRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Duct> ducts = ductRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Duct d : ducts) {

                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_DUCT, d.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, d.getId(), LABEL_DUCT, d.getStatus()
                        );
                    }
                }
            }

            // FAT
            if (fatRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Fat> fats = fatRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Fat f : fats) {
                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_FAT, f.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, f.getId(), LABEL_FAT, f.getStatus()
                        );
                    }
                }
            }

            // FDP
            if (fdpRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                List<Fdp> fdps = fdpRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Fdp f : fdps) {
                    List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_FDP, f.getId());
                    for (Integer mappingId : mappingIds) {
                        createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, f.getId(), LABEL_FDP, f.getStatus()
                        );
                    }
                }
            }
                // FDT
                if (fdtRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Fdt> fdts = fdtRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Fdt f : fdts) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_FDT, f.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, f.getId(), LABEL_FDT, f.getStatus());
                        }
                    }
                }

                // Handhole
                if (handholeRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Handhole> handholes = handholeRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Handhole h : handholes) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_HANDHOLE, h.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, h.getId(), LABEL_HANDHOLE, h.getStatus());
                        }

                    }
                }

                // Joint Closure
                if (jointClosureRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<JointClosure> joints = jointClosureRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (JointClosure j : joints) {
                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_JOINT_CLOSURE, j.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, j.getId(), LABEL_JOINT_CLOSURE, j.getStatus());

                        }
                    }

                }

                // Manhole
                if (manholeRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Manhole> manholes = manholeRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Manhole m : manholes) {
                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_MANHOLE, m.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, m.getId(), LABEL_MANHOLE, m.getStatus());
                        }

                    }
                }

                // MDU
                if (mduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Mdu> mdus = mduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Mdu m : mdus) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_MDU, m.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, m.getId(), LABEL_MDU, m.getStatus());
                        }
                    }
                }

                // ODF
                if (odfRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Odf> odfs = odfRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Odf o : odfs) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_ODF, o.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, o.getId(), LABEL_ODF, o.getStatus());
                        }
                    }
                }

                // OLT
                if (oltRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Olt> olts = oltRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Olt o : olts) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_OLT, o.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, o.getId(), LABEL_OLT, o.getStatus());
                        }

                    }
                }

                // Pole
                if (poleRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Pole> poles = poleRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Pole p : poles) {
                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_POLE, p.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, p.getId(), LABEL_POLE, p.getStatus()
                            );
                        }
//                }
                    }

                    // Pop
                    if (popRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                        List<Pop> pops = popRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                        for (Pop p : pops) {

                            List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_POP, p.getId());
                            for (Integer mappingId : mappingIds) {
                                createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, p.getId(), LABEL_POP, p.getStatus());
                            }
                        }
                    }
                }

                // SDU
                if (sduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Sdu> sdus = sduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Sdu s : sdus) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_SDU, s.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, s.getId(), LABEL_SDU, s.getStatus());
                        }
                    }
                }

                // Splitter
                if (splitterRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Splitter> splitters = splitterRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Splitter s : splitters) {
                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_SPLITTER, s.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, s.getId(), LABEL_SPLITTER, s.getStatus());
                        }
                    }
                }

                // Trench
                if (trenchRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<TrenchLayer> trenches = trenchRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (TrenchLayer t : trenches) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_TRENCH, t.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, t.getId(), LABEL_TRENCH, t.getStatus());
                        }
                    }
                }

                //Street
                if (streetRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                    List<Street> streets = streetRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                    for (Street s : streets) {

                        List<Integer> mappingIds = layerInventoryMappingRepository.getIdByLayerCodeAndLayerId(LAYER_CODE_STREET, s.getId());
                        for (Integer mappingId : mappingIds) {
                            createSurveyBomMapping(surveyAreaId, bomVersionId, mappingId, createdBy, s.getId(), LABEL_STREET, s.getStatus());
                        }
                    }
                }

            } catch(Throwable e){
                throw new RuntimeException("Failed to insert survey BOM version mappings: " + e.getMessage(), e);
            }
    }
}





