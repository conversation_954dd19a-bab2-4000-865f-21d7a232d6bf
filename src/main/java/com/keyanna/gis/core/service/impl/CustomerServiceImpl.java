package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.CustomerDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.AdministrativeUnit;
import com.keyanna.gis.core.model.Customer;
import com.keyanna.gis.core.repository.AdministrativeUnitRepository;
import com.keyanna.gis.core.repository.CustomerRepository;
import com.keyanna.gis.core.service.CustomerService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class CustomerServiceImpl implements CustomerService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerServiceImpl.class);
    private final CustomerRepository customerRepository;
    private final SpatialValidator spatialValidatorpoint;

    public CustomerServiceImpl(CustomerRepository customerRepository, SpatialValidator spatialValidator) {
        this.customerRepository = customerRepository;
        this.spatialValidatorpoint = spatialValidator;
    }

    @Autowired
    private AdministrativeUnitRepository reposi;

    public List<Customer> getAllCustomers(Integer mvnoId) {
        try {

            return customerRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    @Override
    public Customer create(CustomerDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidatorpoint.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidatorpoint.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Customer customer = new Customer();
            customer.setName(dto.getName());
            customer.setAddress(dto.getAddress());
            customer.setPublicId(UUID.randomUUID()); // Generate UUID here
            customer.setCustomer_type(dto.getCustomerType());
            customer.setStatus(dto.getStatus());
            customer.setPort(dto.getPort());
            customer.setActivation_date(dto.getActivationDate());
            customer.setParent_ne_id(dto.getParentId());
            customer.setParent_ne_type(dto.getParentType());
            customer.setSurveyAreaId(dto.getSurveyAreaId());
            customer.setGeom(point);

            customer.setMvnoId(dto.getMvnoId());
            customer.setCreatedBy(dto.getUserId());
            customer.setCreatedOn(LocalDateTime.now());

            return customerRepository.save(customer);
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return null; // or throw a custom runtime exception
        }
    }


    public Customer getLocationById(Integer id, Integer mvnoId) {
        try {
            Optional<Customer> custOpt = customerRepository.findByIdAndMvnoId(id, mvnoId);
            if (custOpt.isPresent()) {
                return custOpt.get();
            } else {
                throw new RuntimeException("Data not found with id :" + id);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }


    @Override
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        if (!customerRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Customer with public Id not found : " + publicId);
        }

        try {
            customerRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            //    logger.error("Error deleting customer with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete customer: " + ex.getMessage(), ex);
        }
    }

    public List<AdministrativeUnit> getChildren(Long adm1Code) {
        try {

            return reposi.findByParent_Adm2Code(adm1Code);
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return null; // or throw a custom runtime exception
        }
    }

    public List<AdministrativeUnit> getAllUnits() {
        try {

            return reposi.findAll();
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return null; // or throw a custom runtime exception
        }
    }

    @Override
    public void updateCustomer(UUID publicId, CustomerDTO customerDTO) {
        // Validation call Point is Within SurveyArea or not
        spatialValidatorpoint.validatePointWithinSurveyArea(
                customerDTO.getSurveyAreaId(),
                customerDTO.getLongitude(),
                customerDTO.getLatitude()
        );

        Customer customer = customerRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Customer not found"));

        try {

            customer.setName(customerDTO.getName());
            customer.setAddress(customerDTO.getAddress());
            customer.setCustomer_type(customerDTO.getCustomerType());
            customer.setStatus(customerDTO.getStatus());
            customer.setActivation_date(customerDTO.getActivationDate());
            customer.setPort(customerDTO.getPort());
            customer.setParent_ne_id(customerDTO.getParentId());
            customer.setParent_ne_type(customerDTO.getParentType());
            customer.setSurveyAreaId(customerDTO.getSurveyAreaId());

            Point point = GeoJsonConverterUtil.createPointGeom(customerDTO.getLongitude(), customerDTO.getLatitude());
            point.setSRID(4326);
            customer.setGeom(point);

            if (!spatialValidatorpoint.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }
            customer.setMvnoId(customerDTO.getMvnoId());

            customer.setModifiedBy(customerDTO.getUserId());
            customer.setModifiedOn(LocalDateTime.now());

            customerRepository.save(customer);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Customer getCustomerByPublicId(UUID publicId, Integer mvnoId) {
        if (!customerRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Customer with public Id not found : " + publicId);
        }

        try {
            Optional<Customer> entity = customerRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting customer data method getCustomerByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get customer data method getCustomerByPublicId: " + ex.getMessage(), ex);
        }
    }
}

