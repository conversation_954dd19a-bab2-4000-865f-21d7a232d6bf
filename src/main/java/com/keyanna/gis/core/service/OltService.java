package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.OltDTO;
import com.keyanna.gis.core.model.Olt;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface OltService {
    Olt create(OltDTO dto) throws ParseException;

    List<Olt> getAllOlt(Integer mvnoId);

    public void updateOlt(UUID publicId, OltDTO oltDTO);

    Olt getOltByPublicId(UUID publicId, Integer mvnoId);

    void deleteByPublicId(UUID publicId, Integer mvnoId);

}
