package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.OdfDTO;
import com.keyanna.gis.core.model.Odf;

import java.util.List;
import java.util.UUID;

public interface OdfService {

    List<Odf> getAllOdfs(Integer mvnoId);

    Odf getOdfByPublicId(UUID publicId,Integer mvnoId);

    Odf createOdf(OdfDTO dto);


    void updateOdf(UUID publicId, OdfDTO dto);

    void deleteByPublicId(UUID publicId,Integer mvnoId);

    boolean existsByPublicId(UUID publicId);
}
