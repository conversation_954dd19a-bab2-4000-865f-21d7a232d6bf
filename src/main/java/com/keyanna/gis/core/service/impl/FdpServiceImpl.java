package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.FdpRequestDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fdp;
import com.keyanna.gis.core.model.LookupFdpTypes;
import com.keyanna.gis.core.repository.FdpRepository;
import com.keyanna.gis.core.repository.LookupFdpTypesRepository;
import com.keyanna.gis.core.repository.PopRepository;
import com.keyanna.gis.core.service.FdpService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Service
public class FdpServiceImpl implements FdpService {

    private static final Logger logger = LoggerFactory.getLogger(FdpServiceImpl.class);
    private final FdpRepository fdpRepository;
    private final LookupFdpTypesRepository lookupFdpTypesRepository;
    private final PopRepository popRepository;
    private final SpatialValidator spatialValidator;

    public FdpServiceImpl(FdpRepository fdpRepository, LookupFdpTypesRepository lookupFdpTypesRepository, PopRepository popRepository, SpatialValidator spatialValidator) {
        this.fdpRepository = fdpRepository;
        this.lookupFdpTypesRepository = lookupFdpTypesRepository;
        this.popRepository = popRepository;
        this.spatialValidator = spatialValidator;
    }

    @Transactional
    public void createFdp(FdpRequestDTO fdpRequestDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                fdpRequestDTO.getSurveyAreaId(),
                fdpRequestDTO.getLongitude(),
                fdpRequestDTO.getLatitude()
        );

        popRepository.findById(fdpRequestDTO.getPopId()).orElseThrow(() -> new EntityNotFoundException("Pop not found"));
        LookupFdpTypes lookupCableType = lookupFdpTypesRepository.findById(fdpRequestDTO.getFdpTypeId())
                .orElseThrow(() -> new EntityNotFoundException("Lookup Fdp Type not found"));

        try {

            Point point = GeoJsonConverterUtil.createPointGeom(fdpRequestDTO.getLongitude(), fdpRequestDTO.getLatitude());
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Fdp fdp = new Fdp();
            fdp.setPublicId(UUID.randomUUID()); // Generate UUID here
            fdp.setName(fdpRequestDTO.getName());
            fdp.setPort(fdpRequestDTO.getPort());

            fdp.setLookupFdpType(lookupCableType);

            fdp.setPopId(fdpRequestDTO.getPopId());

            fdp.setGeom(point);

            fdp.setStatus(fdpRequestDTO.getStatus());

            fdp.setCreatedBy(fdpRequestDTO.getUserId());
            fdp.setCreatedOn(Instant.now());    //  TODO change if required
            fdp.setMvnoId(fdpRequestDTO.getMvnoId());
            fdp.setSurveyAreaId(fdpRequestDTO.getSurveyAreaId());

            fdpRepository.save(fdp);    //  Save to DB
        } catch (Exception ex) {
            logger.error("Error creating fdp {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create fdp: " + ex.getMessage(), ex);
        }
    }

    public void updateFdp(UUID publicId, FdpRequestDTO updatedFdpRequestDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                updatedFdpRequestDTO.getSurveyAreaId(),
                updatedFdpRequestDTO.getLongitude(),
                updatedFdpRequestDTO.getLatitude()
        );

        Fdp fdp = fdpRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("Fdp with ID " + publicId + " not found."));

        try {

            fdp.setName(updatedFdpRequestDTO.getName());
            fdp.setPort(updatedFdpRequestDTO.getPort());

            LookupFdpTypes lookupCableType = lookupFdpTypesRepository.findById(updatedFdpRequestDTO.getFdpTypeId())
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Fdp Type not found"));
            fdp.setLookupFdpType(lookupCableType);

            fdp.setPopId(updatedFdpRequestDTO.getPopId());

            fdp.setGeom(GeoJsonConverterUtil.createPointGeom(updatedFdpRequestDTO.getLongitude(), updatedFdpRequestDTO.getLatitude()));
            if (!spatialValidator.isValidGeometry(fdp.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            fdp.setStatus(updatedFdpRequestDTO.getStatus());

            fdp.setModifiedBy(updatedFdpRequestDTO.getUserId());
            fdp.setModifiedOn(Instant.now());    //  modified_on
            fdp.setMvnoId(updatedFdpRequestDTO.getMvnoId());
            fdp.setSurveyAreaId(updatedFdpRequestDTO.getSurveyAreaId());

            fdpRepository.save(fdp); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating fdp {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update fdp");
        }
    }

//    public List<FdpResponseDTO> getAllFdps(boolean withGeometry, String userTimeZone) {
//        try {
//            List<Object[]> fdpList = new ArrayList<>();
//            if (!withGeometry) {
//                fdpList = fdpRepository.getAllFdpWithoutGeom();
//            } else {
//                fdpList = fdpRepository.getAllFdps();
//            }
//
//            if (fdpList == null) {
//                throw new IllegalArgumentException("Fdp list is null.");
//            } else if (fdpList.isEmpty()) {
//                throw new EntityNotFoundException("No fdp found for the provided criteria.");
//            }
//            List<FdpResponseDTO> responseDtoList = new ArrayList<>();
//
//            for (Object[] fdpObj : fdpList) {
//                FdpResponseDTO responseDto = new FdpResponseDTO();
//                responseDto.setName((String) fdpObj[0]);    //  name
//                responseDto.setAddress((String) fdpObj[1]); //  address
//                responseDto.setCategory((String) fdpObj[2]);  //  category
//                responseDto.setStatus((String) fdpObj[3]);  //  status
//
//                responseDto.setUserId((Long) fdpObj[4]); //  created_by
//
//                responseDto.setCreatedOn(CommonUtil.convertToUserTimeZone0((Instant) fdpObj[5], userTimeZone));    //  created_on
//
//                // Assuming geom is stored as JSON string or a compatible object
//                if (withGeometry) {
//                    responseDto.setGeom(GeoJsonConverterUtil.convertToPointJson1((String) fdpObj[6], objectMapper));  //  geom
//                }
//                responseDtoList.add(responseDto);
//            }
//            return responseDtoList;
//        } catch (Exception ex) {
//            logger.error("Error while getting fdps {}: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to get fdps: ");
//        }
//    }

    public Fdp getFdpByPublicId(UUID publicId, Integer mvnoId) {
        if (!fdpRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Fdp with public Id not found : " + publicId);
        }

        try {
            Optional<Fdp> entity = fdpRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting fdp data method getFdpByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get fdp data method getFdpByPublicId: " + ex.getMessage(), ex);
        }
    }


    @Transactional
    public void deleteFdpByPublicIdAndMvnoId(UUID publicId, Integer mvnoId) {
        if (!fdpRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Fdp with public Id not found : " + publicId);
        }

        try {
            fdpRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting fdp with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete fdp: " + ex.getMessage(), ex);
        }
    }
}