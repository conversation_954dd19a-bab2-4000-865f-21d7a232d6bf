package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.CustomerDTO;
import com.keyanna.gis.core.model.Customer;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface CustomerService {

    public void updateCustomer(UUID publicId, CustomerDTO customerDTO);
    List<Customer> getAllCustomers(Integer mvnoId);
    Customer create(CustomerDTO dto) throws ParseException;
    Customer getLocationById(Integer id,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);

    Customer getCustomerByPublicId(UUID publicId,Integer mvnoId);

}
