package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.HandholeRequestDTO;
import com.keyanna.gis.core.dto.response.HandholeResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Handhole;
import com.keyanna.gis.core.repository.DuctRepository;
import com.keyanna.gis.core.repository.HandholeRepository;
import com.keyanna.gis.core.service.HandholeService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class HandholeServiceImpl implements HandholeService {

    private static final Logger logger = LoggerFactory.getLogger(HandholeServiceImpl.class);
    private final HandholeRepository handholeRepository;
    private final SpatialValidator spatialValidator;

    public HandholeServiceImpl(HandholeRepository handholeRepository, SpatialValidator spatialValidator) {
        this.handholeRepository = handholeRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public HandholeResponseDTO create(HandholeRequestDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            Handhole handhole = new Handhole();
            handhole.setName(dto.getName());
            handhole.setHoleSize(dto.getHoleSize());
            handhole.setAccessType(dto.getAccessType());
            handhole.setMaterial(dto.getMaterial());
            handhole.setPublicId(UUID.randomUUID());
            handhole.setStatus(dto.getStatus());
            handhole.setCreatedBy(dto.getUserId());
            handhole.setGeom(GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude()));
            handhole.setMvnoId(dto.getMvnoId());
            handhole.setSurveyAreaId(dto.getSurveyAreaId());

            if (!spatialValidator.isValidGeometry(handhole.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Handhole savedHandhole = handholeRepository.save(handhole);
            return convertToResponseDTO(savedHandhole);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to create Handhole: " + e.getMessage());
        }
    }

    @Override
    public HandholeResponseDTO updateHandholes(UUID publicId, @Valid HandholeResponseDTO dto) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            Handhole handhole = handholeRepository.findByPublicId(publicId)
                    .orElseThrow(() -> new RuntimeException("Handhole not found"));

            handhole.setName(dto.getName());
            handhole.setHoleSize(dto.getHoleSize());
            handhole.setAccessType(dto.getAccessType());
            handhole.setMaterial(dto.getMaterial());
            handhole.setStatus(dto.getStatus());
            handhole.setModifiedBy(dto.getUserId());
            handhole.setSurveyAreaId(dto.getSurveyAreaId());

            Point location = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            location.setSRID(4326);
            handhole.setGeom(location);

            if (!spatialValidator.isValidGeometry(handhole.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            handhole.setMvnoId(dto.getMvnoId());

            Handhole updatedHandhole = handholeRepository.save(handhole);
            return convertToResponseDTO(updatedHandhole);
        } catch (Throwable e) {
            throw new RuntimeException("Failed to update Handhole: " + e.getMessage());
        }
    }

    public Handhole getHandholeByPublicId(UUID publicId, Integer mvnoId) {
        if (!handholeRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Handhole with public Id not found : " + publicId);
        }

        try {
            Optional<Handhole> entity = handholeRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting handhole data method getHandholeByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get handhole data method getHandholeByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    @Transactional
    public void deleteHandholeByPublicId(UUID publicId, Integer mvnoId) {
        try {
            if (!handholeRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
                throw new EntityNotFoundException("Handhole not found for publicId: " + publicId + " and mvnoId: " + mvnoId);
            }

            handholeRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);

        } catch (EntityNotFoundException enfe) {
            // Specific error if not found
            throw enfe;

        } catch (DataAccessException dae) {
            // Spring-specific DB exception
            logger.error("Database error while deleting Handhole with publicId: {}, mvnoId: {}", publicId, mvnoId, dae);
            throw new RuntimeException("Database error occurred while deleting handhole.");

        } catch (Exception ex) {
            // Generic fallback
            logger.error("Unexpected error while deleting Handhole with publicId: {}, mvnoId: {}", publicId, mvnoId, ex);
            throw new RuntimeException("Unexpected error occurred while deleting handhole.");
        }
    }


    @Override
    public List<HandholeResponseDTO> getAllHandholes(Integer mvnoId) {
        try {
            List<Handhole> handholes = handholeRepository.findByMvnoId(mvnoId);
            return handholes.stream()
                    .map(this::convertToResponseDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    private HandholeResponseDTO convertToResponseDTO(Handhole handhole) {
        HandholeResponseDTO dto = new HandholeResponseDTO();
        dto.setId(handhole.getId());
        dto.setPublicId(handhole.getPublicId());
        dto.setCustomId(handhole.getCustomId());
        dto.setName(handhole.getName());
        dto.setHoleSize(handhole.getHoleSize());
        dto.setAccessType(handhole.getAccessType());
        dto.setMaterial(handhole.getMaterial());
        dto.setAdm1Id(handhole.getAdm1Id());
        dto.setAdm2Id(handhole.getAdm2Id());
        dto.setGeom(handhole.getGeom().toString());
        dto.setStatus(handhole.getStatus());
        dto.setCreatedBy(handhole.getCreatedBy());
        dto.setCreatedOn(handhole.getCreatedOn());
        dto.setModifiedOn(handhole.getModifiedOn());
        dto.setModifiedBy(handhole.getModifiedBy());
        return dto;
    }
}