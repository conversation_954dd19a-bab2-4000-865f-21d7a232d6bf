package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.HandholeRequestDTO;
import com.keyanna.gis.core.dto.response.HandholeResponseDTO;
import com.keyanna.gis.core.model.Handhole;
import jakarta.validation.Valid;

import java.text.ParseException;
import java.util.List;
import java.util.UUID;

public interface HandholeService {
    HandholeResponseDTO create(HandholeRequestDTO dto) throws ParseException;
    HandholeResponseDTO updateHandholes(UUID publicId, @Valid HandholeResponseDTO dto) ;
    Handhole getHandholeByPublicId(UUID publicId,Integer mvnoId);
    List<HandholeResponseDTO> getAllHandholes(Integer mvnoId);
    void deleteHandholeByPublicId(UUID uuid,Integer mvnoId);

}
