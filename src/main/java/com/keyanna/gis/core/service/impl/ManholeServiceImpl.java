package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.ManholeDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Manhole;
import com.keyanna.gis.core.repository.ManholeRepository;
import com.keyanna.gis.core.service.ManholeService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class ManholeServiceImpl implements ManholeService {

    private static final Logger logger = LoggerFactory.getLogger(ManholeServiceImpl.class);
    private final ManholeRepository manholeRepository;
    private final SpatialValidator spatialValidator;

    public ManholeServiceImpl(ManholeRepository ManholeRepository, SpatialValidator spatialValidator) {
        this.manholeRepository = ManholeRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Manhole create(ManholeDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Manhole manhole = new Manhole();
            manhole.setName(dto.getName());
            manhole.setManholeSize(dto.getManholeSize());
            manhole.setDepthCm(dto.getDepthCm());
            manhole.setCoverType(dto.getCoverType());
            manhole.setPublicId(UUID.randomUUID());
            manhole.setStatus(dto.getStatus());
            manhole.setSurveyAreaId(dto.getSurveyAreaId());

            manhole.setGeom(point);
            manhole.setMvnoId(dto.getMvnoId());

            manhole.setCreatedBy(dto.getUserId());
            manhole.setCreatedOn(LocalDateTime.now());

            return manholeRepository.save(manhole);
        } catch (Exception ex) {
            logger.error("Error creating manhole" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create manhole: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void updateManhole(UUID publicId, ManholeDTO dto) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            Manhole manhole = manholeRepository.findByPublicId(publicId).orElseThrow(() -> new RuntimeException("Manhole not found"));

            manhole.setName(dto.getName());
            manhole.setManholeSize(dto.getManholeSize());
            manhole.setDepthCm(dto.getDepthCm());
            manhole.setCoverType(dto.getCoverType());
            manhole.setStatus(dto.getStatus());
            manhole.setSurveyAreaId(dto.getSurveyAreaId());

            Point point = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            point.setSRID(4326);
            manhole.setGeom(point);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            manhole.setMvnoId(dto.getMvnoId());

            manhole.setModifiedBy(dto.getUserId());
            manhole.setModifiedOn(LocalDateTime.now());

            manholeRepository.save(manhole);
        } catch (Throwable ex) {
            logger.error("Error updating manhole : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update manhole: " + ex.getMessage(), ex);
        }
    }

    public Manhole getManholeById(UUID publicId, Integer mvnoId) {
        if (!manholeRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Manhole with public Id not found : " + publicId);
        }

        try {
            Optional<Manhole> entity = manholeRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            // logger.error("Error getting cable data method getCableByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Manhole data method existsByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void deleteMandholeByPublicId(UUID publicId, Integer mvnoId) {
        try {
            // Check if the manhole exists for the given publicId and mvnoId
            if (!manholeRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
                throw new EntityNotFoundException("Manhole not found for publicId: " + publicId + " and mvnoId: " + mvnoId);
            }

            // Proceed with deletion
            manholeRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);

        } catch (EntityNotFoundException enfe) {
            // Entity-specific exception
            throw enfe;

        } catch (DataAccessException dae) {
            // Spring's data access layer exception
            logger.error("Database error while deleting Manhole with publicId: {}, mvnoId: {}", publicId, mvnoId, dae);
            throw new RuntimeException("Database error occurred while deleting the manhole.");

        } catch (Exception ex) {
            // Catch-all for unexpected issues
            logger.error("Unexpected error while deleting Manhole with publicId: {}, mvnoId: {}", publicId, mvnoId, ex);
            throw new RuntimeException("Unexpected error occurred while deleting the manhole.");
        }
    }


    @Override
    public List<Manhole> getAllManholes(Integer mvnoId) {
        try {
            return manholeRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }
}