package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.LayerInventoryMappingRequestDTO;

import java.util.List;

public interface LayerInventoryMappingService {

    void createMultipleLayerInventoryMappings(List<LayerInventoryMappingRequestDTO> requestDTOList);

    void deleteByLayerId(Integer layerId);

    void removeMappingsUsingLayerIdAndLayerName(Integer layerId, String layerName);

    void deleteByLayerIdAndLayerName(Integer layerId, String layerName);
}
