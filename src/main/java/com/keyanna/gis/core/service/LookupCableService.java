package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.LookupCableDTO;
import com.keyanna.gis.core.model.LookupCable;
import org.locationtech.jts.io.ParseException;

import java.util.List;

public interface LookupCableService {

    public void updateLookupCable(Integer id, LookupCableDTO lookupCableDTO);
    List<LookupCable> getAllActive();
    LookupCable create(LookupCableDTO dto) throws ParseException;
    LookupCable getLookupCableById(Integer id);
    void deleteLookupCableById(Integer id);
}
