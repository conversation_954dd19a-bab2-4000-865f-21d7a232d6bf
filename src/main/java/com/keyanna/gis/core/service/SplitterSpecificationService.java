package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.SplitterSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.SplitterSpecificationResponseDTO;
import com.keyanna.gis.core.model.SplitterSpecification;

import java.util.List;

public interface SplitterSpecificationService {

    void createSplitterSpecification(SplitterSpecificationRequestDTO splitterSpecificationRequestDTO);

    SplitterSpecification updateSplitterSpecification(Integer id, SplitterSpecificationRequestDTO updatedSplitterSpecificationRequestDTO);

    List<SplitterSpecificationResponseDTO> getAllSplitterSpecifications();

    void deleteSplitterSpecificationById(Integer id);

}
