package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.MduDTO;
import com.keyanna.gis.core.dto.response.MduWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Mdu;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.repository.MduRepository;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.service.MduService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_MDU;
import static com.keyanna.gis.core.constants.ApiConstants.LAYER_CODE_MDU;

@Service
public class MduServiceImpl implements MduService {

    private final MduRepository mduRepository;
    private static final Logger logger = LoggerFactory.getLogger(MduServiceImpl.class);
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final CommonUtilComponent commonUtilComponent;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final SpatialValidator spatialValidatorpoint;

    public MduServiceImpl(MduRepository mduRepository, LayerImageMappingRepository layerImageMappingRepository, ImageServiceImpl imageServiceImpl, CommonUtilComponent commonUtilComponent, LayerInventoryMappingRepository layerInventoryMappingRepository, SpatialValidator spatialValidatorpoint) {
        this.mduRepository = mduRepository;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.commonUtilComponent = commonUtilComponent;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.spatialValidatorpoint = spatialValidatorpoint;

    }

    @Override
    public Mdu create(MduDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidatorpoint.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidatorpoint.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Mdu mdu = new Mdu();
            mdu.setName(dto.name);
            mdu.setAddress(dto.address);
            mdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            mdu.setStatus(dto.status);
            mdu.setCategory(dto.category);
            mdu.setFloors(dto.floors);
            mdu.setTowers(dto.towers);
            mdu.setHome_passes(dto.homePasses);
            mdu.setTenancy(dto.tenancy);
            mdu.setSurveyAreaId(dto.surveyAreaId);

            mdu.setGeom(point);
            mdu.setMvnoId(dto.mvnoId);

            mdu.setOpticalLevel(dto.opticalLevel);
            mdu.setRemarks(dto.remarks);
            mdu.setFatNo(dto.fatNo);
            mdu.setFdtNo(dto.fdtNo);
            mdu.setStreetName(dto.getStreetName());

            mdu.setRiser(dto.getRiser());
            mdu.setOltName(dto.getOltName());

            mdu.setCreatedBy(dto.getUserId());
            mdu.setCreatedOn(LocalDateTime.now());

            Mdu savedMdu = mduRepository.save(mdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedMdu.getId(), ApiConstants.LAYER_CODE_MDU,dto.getStatus());
//            }

            return savedMdu;

        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void updateMdu(UUID publicId, MduDTO mduDTO) {
        Mdu mdu = mduRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Mdu not found"));

        spatialValidatorpoint.validatePointWithinSurveyArea(
                mduDTO.getSurveyAreaId(),
                mduDTO.getLongitude(),
                mduDTO.getLatitude()
        );

        try {
            // Remove old mappings
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(mdu.getId(), LABEL_MDU);

            mdu.setName(mduDTO.getName());
            mdu.setAddress(mduDTO.getAddress());
            mdu.setStatus(mduDTO.getStatus());

            mdu.setCategory(mduDTO.getCategory());
            mdu.setFloors(mduDTO.getFloors());
            mdu.setTowers(mduDTO.getTowers());
            mdu.setHome_passes(mduDTO.getHomePasses());
            mdu.setTenancy(mduDTO.getTenancy());
            mdu.setMvnoId(mduDTO.getMvnoId());
            mdu.setSurveyAreaId(mduDTO.getSurveyAreaId());

            mdu.setFdtNo(mduDTO.getFdtNo());
            mdu.setFatNo(mduDTO.getFatNo());
            mdu.setRemarks(mduDTO.getRemarks());
            mdu.setOpticalLevel(mduDTO.getOpticalLevel());
            mdu.setStreetName(mduDTO.getStreetName());

            mdu.setRiser(mduDTO.getRiser());
            mdu.setOltName(mduDTO.getOltName());

            Point location = GeoJsonConverterUtil.createPointGeom(mduDTO.getLongitude(), mduDTO.getLatitude());
            location.setSRID(4326);
            mdu.setGeom(location);

            mdu.setModifiedBy(mduDTO.getUserId());
            mdu.setModifiedOn(LocalDateTime.now());

            Mdu savedMdu = mduRepository.save(mdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (mduDTO.getInventoryList() != null && !mduDTO.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(mduDTO.getInventoryList(), savedMdu.getId(), ApiConstants.LAYER_CODE_MDU);
//            }

        } catch (Exception ex) {
            logger.error("Error updating mdu : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update mdu");
        }
    }

    @Override
    public List<Mdu> getAllMdu(Integer mvnoId) {
        try {

            return mduRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    public MduWithImageDTO getMduByPublicIdAndMvnoId(UUID publicId, Integer mvnoId) {
        if (!mduRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Mdu with public Id not found : " + publicId);
        }

        try {
            Optional<Mdu> entity = mduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Mdu mdu = entity.get();

            List<LayerImageMapping> mappingData = layerImageMappingRepository.findAllByLayerIdAndLayerCode(mdu.getId(), LAYER_CODE_MDU);
            List<LayerInventoryMapping> accessoriesList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(mdu.getId(), LAYER_CODE_MDU);
            return new MduWithImageDTO(mdu, mappingData, accessoriesList);
        } catch (Exception ex) {
            logger.error("Error getting mdu data method getMduByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get mdu data method getMduByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Mdu createWithImage(MduDTO dto, List<MultipartFile> imageFiles) throws ParseException {
        try {

            spatialValidatorpoint.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidatorpoint.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Mdu mdu = new Mdu();
            mdu.setName(dto.name);
            mdu.setAddress(dto.address);
            mdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            mdu.setStatus(dto.status);
            mdu.setCategory(dto.category);
            mdu.setFloors(dto.floors);
            mdu.setTowers(dto.towers);
            mdu.setHome_passes(dto.homePasses);
            mdu.setTenancy(dto.tenancy);
            mdu.setSurveyAreaId(dto.surveyAreaId);

            mdu.setGeom(point);
            mdu.setMvnoId(dto.mvnoId);

            mdu.setCreatedBy(dto.userId);
            mdu.setCreatedOn(LocalDateTime.now());
            mdu.setOpticalLevel(dto.opticalLevel);
            mdu.setRemarks(dto.remarks);

            mdu.setRiser(dto.getRiser());
            mdu.setOltName(dto.getOltName());

            mdu.setFdtNo(dto.getFdtNo());
            mdu.setFatNo(dto.getFatNo());
            mdu.setStreetName(dto.getStreetName());

            Mdu savedMdu = mduRepository.save(mdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedMdu.getId(), ApiConstants.LAYER_CODE_MDU);
//            }

            //Save data in layer image mapping.
            if (imageFiles != null && !imageFiles.isEmpty()) {
                commonUtilComponent.saveLayerImageMappingData(imageFiles, ApiConstants.LAYER_CODE_MDU, savedMdu.getId());
            }

            return savedMdu;
        } catch (Throwable e) {
            logger.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Map<String, Object> previewImageByName(String fileName) {
        try {
            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
            if (!optionalLayerImageMapping.isPresent()) {
                throw new RuntimeException("No images found for fileName=" + fileName);
            }

            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());

            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            Map<String, Object> map = new HashMap<>();
            map.put("resources", resource); // resources removed and same contentTypes also
            map.put("contentTypes", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Mdu> optionalMdu = mduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Mdu mdu = optionalMdu.orElseThrow(() ->
                new EntityNotFoundException("Mdu with public Id not found")
        );

        Integer mduId = mdu.getId(); // Primary key in ne_mdu

        try {
            // Step 1: Delete mappings from layer_accessory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(mduId, LAYER_CODE_MDU);

            // Step 3: Remove images from mapping.
            commonUtilComponent.deleteAllAssociatedImageWithNetworkElement(mduId, LAYER_CODE_MDU);

            // Step 3: Delete the mdu itself
            mduRepository.delete(mdu);

        } catch (Exception ex) {
            logger.error("Error deleting mdu, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete mdu and related data: " + ex.getMessage(), ex);
        }
    }
}
