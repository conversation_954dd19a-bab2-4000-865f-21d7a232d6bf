package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.model.LayerMaster;
import com.keyanna.gis.core.repository.LayerMasterRepository;
import com.keyanna.gis.core.service.LayerMasterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LayerMasterServiceImpl implements LayerMasterService {

    @Autowired
    private LayerMasterRepository repository;

    @Override
    public List<LayerMaster> getAllLayers(Integer mvnoId) {
        try
        {
            return repository.findByMvnoId(mvnoId);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
