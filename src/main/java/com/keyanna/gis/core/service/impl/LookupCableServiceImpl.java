package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.LookupCableDTO;
import com.keyanna.gis.core.model.LookupCable;
import com.keyanna.gis.core.repository.LookupCableRepository;
import com.keyanna.gis.core.service.LookupCableService;
import com.keyanna.gis.core.utility.ApiResponse;
import org.locationtech.jts.io.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class LookupCableServiceImpl implements LookupCableService {

    @Autowired
    private LookupCableRepository repository;

    @Override
    public void updateLookupCable(Integer Id, LookupCableDTO lookupCableDTO) {
        try {

            LookupCable customer = repository.findById(Id)
                    .orElseThrow(() -> new RuntimeException("Lookup cable not found"));

            customer.setName(lookupCableDTO.getName());
            customer.setDescription(lookupCableDTO.getDescription());
            customer.setCreatedOn(lookupCableDTO.getCreated_on());
            customer.setModifiedOn(lookupCableDTO.getModified_on());

            repository.save(customer);
        }
        catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }

    }

    @Override
    public List<LookupCable> getAllActive() {
        try {

            return repository.findByIsActiveTrue();
        }
             catch (Exception e) {
                // Log the exception or handle it as needed
                e.printStackTrace(); // Replace with logger if available
                return null; // or throw a custom runtime exception
            }
    }

    @Override
    public LookupCable create(LookupCableDTO dto) throws ParseException {

        try {

            LookupCable locations = new LookupCable();
            locations.setName(dto.name);
            locations.setDescription(dto.description);
            locations.setActive(dto.isActive);
            locations.setCreatedOn(dto.created_on);
            locations.setModifiedOn(dto.modified_on);


            return repository.save(locations);
        }
         catch (Exception e) {
                // Log the exception or handle it as needed
                e.printStackTrace(); // Replace with logger if available
             return null; // or throw a custom runtime exception
            }

    }

    @Override
    public LookupCable getLookupCableById(Integer id) {
        try {
            Optional<LookupCable> custOpt = repository.findById(id);
            if(custOpt.isPresent())
            {
                return custOpt.get();
            }
            else
            {
                throw new RuntimeException("Data not found with id :" +id);
            }
        }
        catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }    }

    @Override
    public void deleteLookupCableById(Integer id) {
        try {
            if (!repository.existsById(id)) {
                ResponseEntity.ok(ApiResponse.success("Cable not found with ID: " + id, null));
            } else {
                throw new RuntimeException("Data not found with id :" + id);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
