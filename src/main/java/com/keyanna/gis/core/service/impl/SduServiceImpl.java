package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.SduDTO;
import com.keyanna.gis.core.dto.response.SduWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.Sdu;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.SduRepository;
import com.keyanna.gis.core.service.SduService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_SDU;
import static com.keyanna.gis.core.constants.ApiConstants.LAYER_CODE_SDU;

@Service
public class SduServiceImpl implements SduService {

    private final SduRepository sduRepository;
    private static final Logger logger = LoggerFactory.getLogger(SduServiceImpl.class);
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final CommonUtilComponent commonUtilComponent;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final SpatialValidator spatialValidator;

    public SduServiceImpl(SduRepository sduRepository, LayerImageMappingRepository layerImageMappingRepository, ImageServiceImpl imageServiceImpl, CommonUtilComponent commonUtilComponent, LayerInventoryMappingRepository layerInventoryMappingRepository, SpatialValidator spatialValidator) {
        this.sduRepository = sduRepository;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.commonUtilComponent = commonUtilComponent;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Sdu create(SduDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Sdu sdu = new Sdu();
            sdu.setName(dto.name);
            sdu.setAddress(dto.address);
            sdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            sdu.setStatus(dto.status);
            sdu.setCategory(dto.category);
            sdu.setFloors(dto.floors);
            sdu.setTowers(dto.towers);
            sdu.setHome_passes(dto.homePasses);
            sdu.setTenancy(dto.tenancy);
            sdu.setSurveyAreaId(dto.surveyAreaId);

            sdu.setGeom(point);
            sdu.setMvnoId(dto.mvnoId);

            sdu.setOpticalLevel(dto.opticalLevel);
            sdu.setRemarks(dto.remarks);
            sdu.setFatNo(dto.fatNo);
            sdu.setFdtNo(dto.fdtNo);
            sdu.setStreetName(dto.getStreetName());

            sdu.setRiser(dto.getRiser());
            sdu.setOltName(dto.getOltName());

            sdu.setCreatedBy(dto.getUserId());
            sdu.setCreatedOn(LocalDateTime.now());

            Sdu savedSdu = sduRepository.save(sdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedSdu.getId(), ApiConstants.LAYER_CODE_SDU,dto.getStatus());
//            }

            return savedSdu;

        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void updateSdu(UUID publicId, SduDTO sduDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                sduDTO.getSurveyAreaId(),
                sduDTO.getLongitude(),
                sduDTO.getLatitude()
        );

        Sdu sdu = sduRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Sdu not found"));

        spatialValidator.validatePointWithinSurveyArea(
                sduDTO.getSurveyAreaId(),
                sduDTO.getLongitude(),
                sduDTO.getLatitude()
        );

        try {
            // Remove old mappings
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(sdu.getId(), LABEL_SDU);

            sdu.setName(sduDTO.getName());
            sdu.setAddress(sduDTO.getAddress());
            sdu.setStatus(sduDTO.getStatus());

            sdu.setCategory(sduDTO.getCategory());
            sdu.setFloors(sduDTO.getFloors());
            sdu.setTowers(sduDTO.getTowers());
            sdu.setHome_passes(sduDTO.getHomePasses());
            sdu.setTenancy(sduDTO.getTenancy());
            sdu.setMvnoId(sduDTO.getMvnoId());
            sdu.setSurveyAreaId(sduDTO.getSurveyAreaId());

            sdu.setFdtNo(sduDTO.getFdtNo());
            sdu.setFatNo(sduDTO.getFatNo());
            sdu.setRemarks(sduDTO.getRemarks());
            sdu.setOpticalLevel(sduDTO.getOpticalLevel());
            sdu.setStreetName(sduDTO.getStreetName());

            sdu.setRiser(sduDTO.getRiser());
            sdu.setOltName(sduDTO.getOltName());

            Point point = GeoJsonConverterUtil.createPointGeom(sduDTO.getLongitude(), sduDTO.getLatitude());
            point.setSRID(4326);
            sdu.setGeom(point);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            sdu.setModifiedBy(sduDTO.getUserId());
            sdu.setModifiedOn(LocalDateTime.now());

            Sdu savedSdu = sduRepository.save(sdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (sduDTO.getInventoryList() != null && !sduDTO.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(sduDTO.getInventoryList(), savedSdu.getId(), LABEL_SDU);
//            }

        } catch (Exception ex) {
            logger.error("Error updating sdu : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update sdu");
        }
    }

    @Override
    public List<Sdu> getAllSdu(Integer mvnoId) {
        try {

            return sduRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    public SduWithImageDTO getSduByPublicId(UUID publicId, Integer mvnoId) {
        if (!sduRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Sdu with public Id not found : " + publicId);
        }

        try {
            Optional<Sdu> entity = sduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Sdu sdu = entity.get();

            List<LayerImageMapping> mappingData = layerImageMappingRepository.findAllByLayerIdAndLayerCode(sdu.getId(), LAYER_CODE_SDU);
            List<LayerInventoryMapping> accessoriesList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(sdu.getId(), LAYER_CODE_SDU);
            return new SduWithImageDTO(sdu, mappingData, accessoriesList);
        } catch (Exception ex) {
            logger.error("Error getting sdu data method getSduByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get sdu data method getSduByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Sdu createWithImage(SduDTO dto, List<MultipartFile> imageFiles) throws ParseException {
        try {

            spatialValidator.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            Sdu sdu = new Sdu();
            sdu.setName(dto.name);
            sdu.setAddress(dto.address);
            sdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            sdu.setStatus(dto.status);
            sdu.setCategory(dto.category);
            sdu.setFloors(dto.floors);
            sdu.setTowers(dto.towers);
            sdu.setHome_passes(dto.homePasses);
            sdu.setTenancy(dto.tenancy);
            sdu.setSurveyAreaId(dto.surveyAreaId);

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }
            sdu.setGeom(point);

            sdu.setMvnoId(dto.mvnoId);

            sdu.setCreatedBy(dto.userId);
            sdu.setCreatedOn(LocalDateTime.now());
            sdu.setOpticalLevel(dto.opticalLevel);
            sdu.setRemarks(dto.remarks);

            sdu.setRiser(dto.getRiser());
            sdu.setOltName(dto.getOltName());
            sdu.setStreetName(dto.getStreetName());
            sdu.setFdtNo(dto.getFdtNo());
            sdu.setFatNo(dto.getFatNo());

            Sdu savedSdu = sduRepository.save(sdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedSdu.getId(), ApiConstants.LAYER_CODE_SDU);
//            }

            //Save data in layer image mapping.
            if (imageFiles != null && !imageFiles.isEmpty()) {
                commonUtilComponent.saveLayerImageMappingData(imageFiles, ApiConstants.LAYER_CODE_SDU, savedSdu.getId());
            }

            return savedSdu;
        } catch (Throwable e) {
            logger.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Map<String, Object> previewImageByName(String fileName) {
        try {
            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
            if (!optionalLayerImageMapping.isPresent()) {
                throw new RuntimeException("No images found for fileName=" + fileName);
            }

            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());

            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            Map<String, Object> map = new HashMap<>();
            map.put("resources", resource); // resources removed and same contentTypes also
            map.put("contentTypes", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Sdu> optionalSdu = sduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Sdu sdu = optionalSdu.orElseThrow(() ->
                new EntityNotFoundException("Sdu with public Id not found")
        );

        Integer sduId = sdu.getId(); // Primary key in ne_sdu

        try {
            // Step 1: Delete mappings from layer_accessory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(sduId, LABEL_SDU);

            // Step 3: Remove images from mapping.
            commonUtilComponent.deleteAllAssociatedImageWithNetworkElement(sduId, LABEL_SDU);

            // Step 3: Delete the sdu itself
            sduRepository.delete(sdu);
        } catch (Exception ex) {
            logger.error("Error deleting sdu, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete sdu and related data: " + ex.getMessage(), ex);
        }
    }
}
