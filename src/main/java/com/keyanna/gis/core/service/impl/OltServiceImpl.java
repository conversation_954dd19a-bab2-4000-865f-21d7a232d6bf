package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.OltDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.OltRepository;
import com.keyanna.gis.core.service.OltService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_OLT;

@Service
public class OltServiceImpl implements OltService {

    private static final Logger logger = LoggerFactory.getLogger(OltServiceImpl.class);
    private final OltRepository oltRepository;
    private final CommonUtilComponent commonUtilComponent;
    private final SpatialValidator spatialValidator;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;

    public OltServiceImpl(OltRepository oltRepository, @Lazy CommonUtilComponent commonUtilComponent, SpatialValidator spatialValidator, LayerInventoryMappingRepository layerInventoryMappingRepository) {
        this.oltRepository = oltRepository;
        this.commonUtilComponent = commonUtilComponent;
        this.spatialValidator = spatialValidator;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
    }

    @Override
    public Olt create(OltDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Olt olt = new Olt();
            olt.setName(dto.getName());
            olt.setOpticalLevel(dto.getOpticalLevel());
            olt.setUpLinkProtection(dto.getUpLinkProtection());
            olt.setPowerBackup(dto.getPowerBackup());
            olt.setVendor(dto.getVendor());
            olt.setPublicId(UUID.randomUUID()); // Generate UUID here
            olt.setModel(dto.getModel());
            olt.setSlots(dto.getSlots());
            olt.setActivePorts(dto.getActivePorts());
            olt.setSurveyAreaId(dto.getSurveyAreaId());
            olt.setGeom(point);
            olt.setMvnoId(dto.getMvnoId());
            olt.setStatus(dto.getStatus());



            olt.setCreatedBy(dto.getUserId());
            olt.setCreatedOn(LocalDateTime.now());

            Olt savedOlt = oltRepository.save(olt);

            // Create Layer Accessory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedOlt.getId(), ApiConstants.LAYER_CODE_OLT,dto.getStatus());
            }

            return savedOlt;

        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<Olt> getAllOlt(Integer mvnoId) {
        try {
            return oltRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    @Override
    public void updateOlt(UUID publicId, OltDTO oltDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                oltDTO.getSurveyAreaId(),
                oltDTO.getLongitude(),
                oltDTO.getLatitude()
        );

        Olt olt = oltRepository.findByPublicIdAndMvnoId(publicId, oltDTO.getMvnoId())
                .orElseThrow(() -> new RuntimeException("olt not found"));

        try {
            // Remove old mappings

            olt.setName(oltDTO.getName());
            olt.setOpticalLevel(oltDTO.getOpticalLevel());
            olt.setUpLinkProtection(oltDTO.getUpLinkProtection());
            olt.setPowerBackup(oltDTO.getPowerBackup());
            olt.setVendor(oltDTO.getVendor());
            olt.setModel(oltDTO.getModel());
            olt.setSlots(oltDTO.getSlots());
            olt.setActivePorts(oltDTO.getActivePorts());
            olt.setMvnoId(oltDTO.getMvnoId());
            olt.setSurveyAreaId(oltDTO.getSurveyAreaId());
            olt.setStatus(oltDTO.getStatus());

            Point point = GeoJsonConverterUtil.createPointGeom(oltDTO.getLongitude(), oltDTO.getLatitude());
            point.setSRID(4326);
            olt.setGeom(point);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            olt.setModifiedBy(oltDTO.getUserId());
            olt.setModifiedOn(LocalDateTime.now());

            Olt savedOlt = oltRepository.save(olt);

            /**
             * TODO : Delete old Inventory before adding while update olt api, handhle olt_tray, network_port data which is in use
             */
            // Create Layer Accessory Mapping mappings
            if (oltDTO.getInventoryList() != null && !oltDTO.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(oltDTO.getInventoryList(), savedOlt.getId(), LABEL_OLT, oltDTO.getStatus());
            }

        } catch (Exception ex) {
            logger.error("Error updating olt : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update olt");
        }
    }

    @Override
    public Olt getOltByPublicId(UUID publicId, Integer mvnoId) {
        if (!oltRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("olt with public Id not found : " + publicId);
        }
        try {
            Optional<Olt> entity = oltRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Olt olt = entity.get();

            layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(olt.getId(), ApiConstants.LAYER_CODE_ODF);
            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting olt data method getOltByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get olt data method getOltByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Olt> optionalOlt = oltRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Olt olt = optionalOlt.orElseThrow(() ->
                new EntityNotFoundException("olt with public Id not found")
        );

        Integer oltId = olt.getId(); // Primary key in ne_olt

        try {
            // Step 1: Delete mappings from layer_accessory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(oltId, ApiConstants.LAYER_CODE_ODF);

            oltRepository.delete(olt);


        } catch (Exception ex) {
            logger.error("Error deleting olt, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete olt and related data: " + ex.getMessage(), ex);
        }
    }

}
