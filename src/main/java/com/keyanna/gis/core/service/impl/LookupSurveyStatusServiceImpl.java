package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.LookupSurveyStatusRequestDTO;
import com.keyanna.gis.core.dto.response.LookupSurveyStatusResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LookupSurveyStatus;
import com.keyanna.gis.core.repository.LookupSurveyStatusRepository;
import com.keyanna.gis.core.service.LookupSurveyStatusService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LookupSurveyStatusServiceImpl implements LookupSurveyStatusService {

    private static final Logger logger = LoggerFactory.getLogger(LookupSurveyStatusServiceImpl.class);
    private final LookupSurveyStatusRepository lookupSurveyStatusRepository;

    public LookupSurveyStatusServiceImpl(LookupSurveyStatusRepository lookupSurveyStatusRepository) {
        this.lookupSurveyStatusRepository = lookupSurveyStatusRepository;
    }

    @Transactional
    @Override
    public void createLookupSurveyStatus(LookupSurveyStatusRequestDTO requestDTO) {
        try {
            LookupSurveyStatus surveyStatus = new LookupSurveyStatus();
            surveyStatus.setName(requestDTO.getName());
            surveyStatus.setDescription(requestDTO.getDescription());
            surveyStatus.setIsActive(requestDTO.getIsActive());

            lookupSurveyStatusRepository.save(surveyStatus);
        } catch (DataIntegrityViolationException ex) {
            logger.error("Survey Status with the same name already exists: {}", ex.getMessage(), ex);
            throw new RuntimeException("Survey Status with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating Survey Status: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create Survey Status.");
        }
    }

    public List<LookupSurveyStatusResponseDTO> getAllLookupSurveyStatus(Integer mvnoId) {
        try {
            List<LookupSurveyStatus> lookupSurveyStatusList = lookupSurveyStatusRepository.findByIsActiveTrueAndMvnoId(mvnoId);
            if (lookupSurveyStatusList == null) {
                throw new IllegalArgumentException("No Lookup Survey Status found");
            } else if (lookupSurveyStatusList.isEmpty()) {
                throw new EntityNotFoundException("No Lookup Survey Status found for the provided criteria.");
            }
            List<LookupSurveyStatusResponseDTO> responseDtoList = new ArrayList<>();
            for (LookupSurveyStatus lookupSurveyStatus : lookupSurveyStatusList) {
                LookupSurveyStatusResponseDTO responseDto = new LookupSurveyStatusResponseDTO();
                responseDto.setId(lookupSurveyStatus.getId());
                responseDto.setName(lookupSurveyStatus.getName());
                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting Survey Statues : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Survey Statues: ");
        }
    }

    @Transactional
    @Override
    public LookupSurveyStatusResponseDTO getLookupSurveyStatusById(Integer id,Integer mvnoId) {
        LookupSurveyStatus surveyStatus = lookupSurveyStatusRepository.findByIdAndMvnoId(id,mvnoId)
                .orElseThrow(() -> new EntityNotFoundException("Survey Status with ID " + mvnoId + " not found."));

        LookupSurveyStatusResponseDTO dto = new LookupSurveyStatusResponseDTO();
        dto.setId(surveyStatus.getId());
        dto.setName(surveyStatus.getName());
        return dto;
    }

    @Transactional
    @Override
    public void updateLookupSurveyStatus(Integer id, LookupSurveyStatusRequestDTO updatedDTO) {
        LookupSurveyStatus existing = lookupSurveyStatusRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Survey Status with ID " + id + " not found."));

        try {
            existing.setName(updatedDTO.getName());
            existing.setDescription(updatedDTO.getDescription());
            existing.setIsActive(updatedDTO.getIsActive());

            lookupSurveyStatusRepository.save(existing);
        } catch (Exception ex) {
            logger.error("Error updating Survey Status: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update Survey Status.");
        }
    }

    @Transactional
    @Override
    public void deleteLookupSurveyStatusById(Integer id,Integer mvnoId) {
        LookupSurveyStatus surveyStatus = (LookupSurveyStatus) lookupSurveyStatusRepository.findByIdAndMvnoId(id,mvnoId)
                .orElseThrow(() -> new EntityNotFoundException("Survey Status with ID " + mvnoId + " not found."));

        try {
            lookupSurveyStatusRepository.deleteByIdAndMvnoId(id,mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting Survey Status with id {}: {}", mvnoId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete Survey Status: " + ex.getMessage(), ex);
        }
    }
}
