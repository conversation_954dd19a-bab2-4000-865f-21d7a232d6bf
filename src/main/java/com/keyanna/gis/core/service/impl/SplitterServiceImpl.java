package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.SplitterRequestDTO;
import com.keyanna.gis.core.dto.response.NetworkPortResponseDTO;
import com.keyanna.gis.core.dto.response.SduResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.NetworkPort;
import com.keyanna.gis.core.model.Splitter;
import com.keyanna.gis.core.model.SplitterSpecification;
import com.keyanna.gis.core.repository.NetworkPortRepository;
import com.keyanna.gis.core.repository.SplitterRepository;
import com.keyanna.gis.core.repository.SplitterSpecificationRepository;
import com.keyanna.gis.core.service.SplitterService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class SplitterServiceImpl implements SplitterService {

    private static final Logger logger = LoggerFactory.getLogger(SplitterServiceImpl.class);
    private final SplitterRepository splitterRepository;
    private final SplitterSpecificationRepository splitterSpecificationRepository;
    private final SpatialValidator spatialValidator;
    private final NetworkPortRepository networkPortRepository;


    public SplitterServiceImpl(SplitterRepository splitterRepository, SplitterSpecificationRepository splitterSpecificationRepository, SpatialValidator spatialValidator,NetworkPortRepository networkPortRepository) {
        this.splitterRepository = splitterRepository;
        this.splitterSpecificationRepository = splitterSpecificationRepository;
        this.spatialValidator = spatialValidator;
        this.networkPortRepository = networkPortRepository;

    }

    @Value("${splitter.nearby.sdus.radius.length.meters}")
    private Double splitterNearbySdusRadiusLengthMeters;


    @Transactional
    public Splitter createSplitter(SplitterRequestDTO splitterRequestDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                splitterRequestDTO.getSurveyAreaId(),
                splitterRequestDTO.getLongitude(),
                splitterRequestDTO.getLatitude()
        );

        try {
            Splitter splitter = new Splitter();
            splitter.setPublicId(UUID.randomUUID()); // Generate UUID here
            splitter.setName(splitterRequestDTO.getName());

            SplitterSpecification splitterSpec = splitterSpecificationRepository.findById(splitterRequestDTO.getSpecificationId())
                    .orElseThrow(() -> new EntityNotFoundException("Splitter Specification not found"));
            splitter.setSplitterSpecification(splitterSpec);

            splitter.setGeom(GeoJsonConverterUtil.createPointGeom(splitterRequestDTO.getLongitude(), splitterRequestDTO.getLatitude()));

            splitter.setStatus(splitterRequestDTO.getStatus());
            splitter.setParentNeId(splitterRequestDTO.getParentNeId());
            splitter.setParentNeType(splitterRequestDTO.getParentNeType());

            splitter.setCreatedBy(splitterRequestDTO.getUserId());
            splitter.setCreatedOn(Instant.now());
            splitter.setMvnoId(splitterRequestDTO.getMvnoId());
            splitter.setSurveyAreaId(splitterRequestDTO.getSurveyAreaId());

            if (!spatialValidator.isValidGeometry(splitter.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Splitter savedSplitter = splitterRepository.save(splitter);

            String[] ratio = splitterSpec.getPortRatio().split(":");
            int inputPorts = Integer.parseInt(ratio[0]);
            int outputPorts = Integer.parseInt(ratio[1]);

            // 4. Create Input Ports
            for (int i = 1; i <= inputPorts; i++) {
                createPort(splitter.getId(), ApiConstants.LAYER_TYPE_SPLITTER, i, ApiConstants.PORT_INPUT);
            }

            // 5. Create Output Ports
            for (int i = 1; i <= outputPorts; i++) {
                createPort(splitter.getId(), ApiConstants.LAYER_TYPE_SPLITTER, i, ApiConstants.PORT_OUTPUT);
            }//  Save to DB
            return savedSplitter;
        } catch (Exception ex) {
            logger.error("Error creating splitter {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create splitter: " + ex.getMessage(), ex);
        }
    }

    private void createPort(Integer layerId, String layerType, int portNumber, String portType) {
        NetworkPort port = new NetworkPort();
        port.setLayerId(layerId);
        port.setLayerType(layerType);
        port.setPortNumber(portNumber);
        port.setPortType(portType); // Input / Output
        port.setPortStatus(ApiConstants.STATUS_AVAILABLE);
        port.setCreatedOn(OffsetDateTime.now().toLocalDateTime());
        networkPortRepository.save(port);
    }

    public void updateSplitter(UUID publicId, SplitterRequestDTO updatedSplitterRequestDTO) {
        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                updatedSplitterRequestDTO.getSurveyAreaId(),
                updatedSplitterRequestDTO.getLongitude(),
                updatedSplitterRequestDTO.getLatitude()
        );

        Splitter splitter = splitterRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("Splitter with ID " + publicId + " not found."));

        try {
            splitter.setName(updatedSplitterRequestDTO.getName());

            SplitterSpecification splitterSpec = splitterSpecificationRepository.findById(updatedSplitterRequestDTO.getSpecificationId())
                    .orElseThrow(() -> new EntityNotFoundException("Splitter Specification not found"));
            splitter.setSplitterSpecification(splitterSpec);

            splitter.setGeom(GeoJsonConverterUtil.createPointGeom(updatedSplitterRequestDTO.getLongitude(), updatedSplitterRequestDTO.getLatitude()));

            if (!spatialValidator.isValidGeometry(splitter.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            splitter.setStatus(updatedSplitterRequestDTO.getStatus());
            splitter.setParentNeId(updatedSplitterRequestDTO.getParentNeId());
            splitter.setParentNeType(updatedSplitterRequestDTO.getParentNeType());

            splitter.setModifiedBy(updatedSplitterRequestDTO.getUserId());
            splitter.setModifiedOn(Instant.now());    //  modified_on
            splitter.setMvnoId(updatedSplitterRequestDTO.getMvnoId());
            splitter.setSurveyAreaId(updatedSplitterRequestDTO.getSurveyAreaId());

            splitterRepository.save(splitter); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating splitter {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update splitter");
        }
    }

    public Splitter getNeSplitterById(UUID publicId, Integer mvnoId) {
        if (!splitterRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Splitter with public Id not found : " + publicId);
        }

        try {
            Optional<Splitter> entity = splitterRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting Splitter data method getNeSplitterById {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Splitter data method getNePopById: " + ex.getMessage(), ex);
        }
    }

//    public List<SplitterResponseDTO> getAllSplitters(boolean withGeometry, String userTimeZone) {
//        try {
//            List<Object[]> splitterList = new ArrayList<>();
//            if (!withGeometry) {
//                splitterList = splitterRepository.getAllSplitterWithoutGeom();
//            } else {
//                splitterList = splitterRepository.getAllSplitters();
//            }
//
//            if (splitterList == null) {
//                throw new IllegalArgumentException("Splitter list is null.");
//            } else if (splitterList.isEmpty()) {
//                throw new EntityNotFoundException("No splitter found for the provided criteria.");
//            }
//            List<SplitterResponseDTO> responseDtoList = new ArrayList<>();
//
//            for (Object[] splitterObj : splitterList) {
//                SplitterResponseDTO responseDto = new SplitterResponseDTO();
//                responseDto.setName((String) splitterObj[0]);    //  name
//                responseDto.setAddress((String) splitterObj[1]); //  address
//                responseDto.setCategory((String) splitterObj[2]);  //  category
//                responseDto.setStatus((String) splitterObj[3]);  //  status
//
//                responseDto.setUserId((Long) splitterObj[4]); //  created_by
//
//                responseDto.setCreatedOn(CommonUtil.convertToUserTimeZone0((Instant) splitterObj[5], userTimeZone));    //  created_on
//
//                // Assuming geom is stored as JSON string or a compatible object
//                if (withGeometry) {
//                    responseDto.setGeom(GeoJsonConverterUtil.convertToPointJson1((String) splitterObj[6], objectMapper));  //  geom
//                }
//                responseDtoList.add(responseDto);
//            }
//            return responseDtoList;
//        } catch (Exception ex) {
//            logger.error("Error while getting splitters {}: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to get splitters: ");
//        }
//    }

    @Transactional
    public void deleteSplitterByPublicId(UUID publicId, Integer mvnoId) {
        if (!splitterRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Splitter with public Id not found : " + publicId);
        }

        try {
            splitterRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting splitter with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete splitter: " + ex.getMessage(), ex);
        }
    }

    @Override
    public List<NetworkPortResponseDTO> getAvailablePortsForSplitter(Integer splitterId, Integer mvnoId) {
        try {

            List<NetworkPort> ports = splitterRepository.findAvailablePortsBySplitterIdAndMvnoId(splitterId, mvnoId, ApiConstants.LAYER_TYPE_SPLITTER, ApiConstants.STATUS_AVAILABLE);

            return ports.stream().map(this::convertToNetworkPortDTO).toList();
        } catch (Exception ex) {
            logger.error("Error getting Available Ports method getAvailablePortsForSplitter with splitterId : " + splitterId + ", mvnoId {}: {}", mvnoId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Available Ports data method getAvailablePortsForSplitter: " + ex.getMessage(), ex);
        }
    }

    private NetworkPortResponseDTO convertToNetworkPortDTO(NetworkPort port) {
        NetworkPortResponseDTO dto = new NetworkPortResponseDTO();
        dto.setId(port.getId());
        dto.setLayerId(port.getLayerId());
        dto.setLayerType(port.getLayerType());
        dto.setPortType(port.getPortType());
        dto.setPortNumber(port.getPortNumber());
        dto.setPortStatus(port.getPortStatus());
        dto.setDescription(port.getDescription());
//        dto.setCreatedOn(port.getCreatedOn());
//        dto.setModifiedOn(port.getModifiedOn());
        return dto;
    }

    public List<SduResponseDTO> getNearbySDUsForSplitter(Integer splitterId, Integer mvnoId) {
        try {
            List<Object[]> result = splitterRepository.getNearbySDUsForSplitter(splitterId, mvnoId, splitterNearbySdusRadiusLengthMeters);

            if (result.isEmpty()) {
                throw new RuntimeException("No nearby SDUs found or invalid splitter/mvno.");
            }

            List<SduResponseDTO> dtos = result.stream()
                    .map(obj -> {
                        SduResponseDTO dto = new SduResponseDTO();
                        dto.setId((Integer) obj[0]);
                        dto.setName((String) obj[1]);
                        return dto;
                    })
                    .toList();

            return dtos;
        } catch (Exception ex) {
            logger.error("Error getting Near by SDUs For Splitter method getNearbySDUsForSplitter with splitterId : " + splitterId + ", mvnoId {}: {}", mvnoId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Near by SDUs For Splitter data method getNearbySDUsForSplitter: " + ex.getMessage(), ex);
        }
    }

}