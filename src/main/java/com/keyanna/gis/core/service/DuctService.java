package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.DuctDTO;
import com.keyanna.gis.core.model.Duct;

import java.text.ParseException;
import java.util.List;
import java.util.UUID;

public interface DuctService {

    Duct create(DuctDTO dto) throws ParseException;
    List<Duct> getAllDuct(Integer mvnoId);
    void updateDuct(UUID publicId, DuctDTO ductdto);
    Duct getDuctByPublicId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
}
