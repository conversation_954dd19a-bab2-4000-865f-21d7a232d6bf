package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.LookupFdpTypesDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LookupCable;
import com.keyanna.gis.core.model.LookupFdpTypes;
import com.keyanna.gis.core.repository.LookupFdpTypesRepository;
import com.keyanna.gis.core.service.LookupFdpTypesService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LookupFdpTypesServiceImpl implements LookupFdpTypesService {

    private static final Logger logger = LoggerFactory.getLogger(LookupFdpTypesServiceImpl.class);
    @Autowired
    private  LookupFdpTypesRepository lookupFdpTypesRepository;



    @Override
    public List<LookupFdpTypesDTO> getallLookupFdpTypes() {

        List<LookupFdpTypes> LookupFdpTypesList = lookupFdpTypesRepository.findByIsActiveTrue();
        List<LookupFdpTypesDTO> responseList = new ArrayList<>();
        try {


            if (LookupFdpTypesList == null) {
                throw new IllegalArgumentException("Lookup Fdp Types list is null.");
            } else if (LookupFdpTypesList.isEmpty()) {
                throw new EntityNotFoundException("No Lookup Fdp Types  found for the provided criteria.");
            }


            for (LookupFdpTypes LookupFdpTypes : LookupFdpTypesList) {
                LookupFdpTypesDTO responseDto = new LookupFdpTypesDTO();
                responseDto.setId(LookupFdpTypes.getId());
                responseDto.setName(LookupFdpTypes.getName());
                responseDto.setActive(LookupFdpTypes.isActive());
                responseDto.setDescription(LookupFdpTypes.getDescription());
                responseDto.setName(LookupFdpTypes.getName());

                responseList.add(responseDto);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseList;
    }



    public LookupFdpTypes getbyId(Integer id)
    {
        try {

            return lookupFdpTypesRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Lookup Fdp Type Id not found"));
        } catch (Throwable e) {

                throw new RuntimeException(e.getMessage()); // or throw a custom runtime exception
            }
    }

    @Transactional
    public void createLookupFdpType(LookupFdpTypesDTO requestDTO) {
        try {
            LookupFdpTypes fdpType = new LookupFdpTypes();
            fdpType.setName(requestDTO.getName());
            fdpType.setDescription(requestDTO.getDescription());
            fdpType.setActive(requestDTO.isActive());

            lookupFdpTypesRepository.save(fdpType);
        } catch (DataIntegrityViolationException ex) {
            logger.error("FDP Type with the same name already exists: {}", ex.getMessage(), ex);
            throw new RuntimeException("FDP Type with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating FDP Type: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create FDP Type.");
        }
    }

    @Transactional
    public void updateLookupFdpType(Integer id, LookupFdpTypesDTO updatedDTO) {
        LookupFdpTypes existing = lookupFdpTypesRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("FDP Type with ID " + id + " not found."));

        try {
            existing.setName(updatedDTO.getName());
            existing.setDescription(updatedDTO.getDescription());
            existing.setActive(updatedDTO.isActive());

            lookupFdpTypesRepository.save(existing);
        } catch (Exception ex) {
            logger.error("Error updating FDP Type: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update FDP Type.");
        }
    }

    @Transactional
    public void deleteLookupFdpTypeById(Integer id) {
        LookupFdpTypes fdpType = lookupFdpTypesRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("FDP Type with ID " + id + " not found."));

        try {
            lookupFdpTypesRepository.deleteById(id);
        } catch (Exception ex) {
            logger.error("Error deleting FDP Type with id {}: {}", id, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete FDP Type: " + ex.getMessage(), ex);
        }

    }
}



