package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.enumfolder.CoreColor;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.CableCore;
import com.keyanna.gis.core.model.CableSpecification;
import com.keyanna.gis.core.repository.CableCoreRepository;
import com.keyanna.gis.core.repository.CableRepository;
import com.keyanna.gis.core.service.CableCoreService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CableCoreServiceImpl implements CableCoreService {

    private static final Logger logger = LoggerFactory.getLogger(CableCoreServiceImpl.class);

    private final CableRepository cableRepository;
    private final CableCoreRepository cableCoreRepository;

    public CableCoreServiceImpl(CableRepository cableRepository, CableCoreRepository cableCoreRepository) {
        this.cableRepository = cableRepository;
        this.cableCoreRepository = cableCoreRepository;
    }

    @Override
    @Transactional
    public void createCableCoreEntry(Integer cableId) {
        try {
            // Step 1: Get cable by ID
            Cable cable = cableRepository.findById(cableId)
                    .orElseThrow(() -> new EntityNotFoundException("Cable not found for ID: " + cableId));

            CableSpecification specification = cable.getCableSpecification();
            if (specification == null
                    || specification.getNumberOfCores() == null
                    || specification.getNumberOfTubes() == null
                    || specification.getNumberOfCores() <= 0
                    || specification.getNumberOfTubes() <= 0) {
                throw new IllegalArgumentException("Invalid or missing cable specification for cable ID: " + cableId);
            }

            int totalCores = specification.getNumberOfCores();
            int totalTubes = specification.getNumberOfTubes();
            int coresPerTube = totalCores / totalTubes;
            int remainingCores = totalCores % totalTubes;

            List<CableCore> coreList = new ArrayList<>();
            int coreCounter = 1;

            for (int tube = 1; tube <= totalTubes; tube++) {
                int coresInThisTube = coresPerTube + ((tube == totalTubes) ? remainingCores : 0);

                for (int i = 0; i < coresInThisTube; i++) {
                    CableCore core = new CableCore();
                    core.setCableId(cableId);
                    core.setCoreNumber(coreCounter);
                    core.setCoreColor(CoreColor.getByIndex(coreCounter - 1).getDisplayName()); // assuming color enum has enough colors
                    core.setCoreStatus(ApiConstants.STATUS_AVAILABLE);
                    core.setTubeNumber(tube);
                    core.setSpliceType(null);
                    core.setSpliceStatus(null);
                    coreList.add(core);
                    coreCounter++;
                }
            }

            cableCoreRepository.saveAll(coreList);
        } catch (Exception ex) {
            logger.error("Error creating cable core for cable ID {}: {}", cableId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to create cable core: " + ex.getMessage(), ex);
        }
    }

    @Transactional
    @Override
    public void markPortAsUsed(Integer id) {
        CableCore port = cableCoreRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Cable Core not found with id : " + id));

        port.setCoreStatus(ApiConstants.STATUS_USED);
        // JPA will detect the change and flush it on transaction commit
    }

}