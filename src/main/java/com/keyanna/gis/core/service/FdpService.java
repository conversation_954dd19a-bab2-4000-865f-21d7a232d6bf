package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.FdpRequestDTO;
import com.keyanna.gis.core.model.Fdp;

import java.util.UUID;

public interface FdpService {

    void createFdp(FdpRequestDTO fdpRequestDTO);

    void deleteFdpByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void updateFdp(UUID publicId, FdpRequestDTO updatedFdpRequestDTO);

//    List<FdpResponseDTO> getAllFdps(boolean withGeometry, String userTimeZone);

    Fdp getFdpByPublicId(UUID publicId,Integer mvnoId);

}
