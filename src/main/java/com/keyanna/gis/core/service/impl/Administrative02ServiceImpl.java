package com.keyanna.gis.core.service.impl;


import com.keyanna.gis.core.dto.response.Administrator02DTO;
import com.keyanna.gis.core.repository.Administrative02Repository;
import com.keyanna.gis.core.utility.Administrator02DtoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class Administrative02ServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(Administrative02ServiceImpl.class);

    private final Administrative02Repository repository;

    public Administrative02ServiceImpl(Administrative02Repository repository) {
        this.repository = repository;
    }

    public Administrator02DTO getSingleByAdm0Code(String adm0Code) {
        try {
        List<Object[]> rows = repository.findSelectedFieldsByAdm0Code(adm0Code);
        if (rows.isEmpty()) {
            return null;
        }
        return Administrator02DtoMapper.map(rows.get(0));
    } catch(Exception ex)
    {
        logger.error("Error in getSingleByAdm0Code: " + ex.getMessage(), ex);
        throw new RuntimeException("Failed to fetch single record by Adm0Code");
    }
}

    public List<Administrator02DTO> getAllByAdm0Code(String adm0Code) {
        try {
            List<Object[]> rows = repository.findSelectedFieldsByAdm0Code(adm0Code);
            return Administrator02DtoMapper.mapList(rows);
        } catch (Exception ex) {
            logger.error("Error in getAllByAdm0Code: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to fetch list by Adm0Code");
        }
    }
}
