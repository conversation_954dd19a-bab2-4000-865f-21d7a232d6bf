package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.Kafka.KafkaProducerService;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.SurveyAreaRequestDTO;
import com.keyanna.gis.core.dto.response.SurveyAreaResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LookupSurveyStage;
import com.keyanna.gis.core.model.LookupSurveyStatus;
import com.keyanna.gis.core.model.SurveyArea;
import com.keyanna.gis.core.repository.LookupSurveyStageRepository;
import com.keyanna.gis.core.repository.LookupSurveyStatusRepository;
import com.keyanna.gis.core.repository.SurveyAreaRepository;
import com.keyanna.gis.core.service.CommonService;
import com.keyanna.gis.core.service.SurveyAreaService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

@Service
public class SurveyAreaServiceImpl implements SurveyAreaService {

    private static final Logger logger = LoggerFactory.getLogger(SurveyAreaServiceImpl.class);
    private final SurveyAreaRepository surveyAreaRepository;
    private final LookupSurveyStatusRepository lookupSurveyStatusRepository;
    private final LookupSurveyStageRepository lookupSurveyStageRepository;
    private final CommonService commonService;
    private final KafkaProducerService kafkaProducerService;
    public SurveyAreaServiceImpl(SurveyAreaRepository surveyAreaRepository, LookupSurveyStatusRepository lookupSurveyStatusRepository, LookupSurveyStageRepository lookupSurveyStageRepository, CommonService commonService, KafkaProducerService kafkaProducerService) {
        this.surveyAreaRepository = surveyAreaRepository;
        this.lookupSurveyStatusRepository = lookupSurveyStatusRepository;
        this.lookupSurveyStageRepository = lookupSurveyStageRepository;
        this.commonService = commonService;
        this.kafkaProducerService = kafkaProducerService;
    }

    @Transactional
    public void createSurveyArea(SurveyAreaRequestDTO surveyAreaRequestDTO) {
        try {
            SurveyArea surveyArea = new SurveyArea();
            surveyArea.setPublicId(UUID.randomUUID()); // Generate UUID here
            surveyArea.setName(surveyAreaRequestDTO.getName());
            surveyArea.setDescription(surveyAreaRequestDTO.getDescription());

            LookupSurveyStatus lookupSurveyStatus = lookupSurveyStatusRepository.findById(surveyAreaRequestDTO.getSurveyStatusId())
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Survey Status not found"));
            surveyArea.setLookupSurveyStatus(lookupSurveyStatus);

            //  Set default Survey stage name "Survey"
            LookupSurveyStage lookupSurveyStage = lookupSurveyStageRepository.findByNameIgnoreCase(ApiConstants.LABEL_SURVEY);
            if (lookupSurveyStage == null) {
                throw new EntityNotFoundException("Lookup Survey Stage not found");
            }
            surveyArea.setLookupSurveyStage(lookupSurveyStage);

            surveyArea.setGeom(GeoJsonConverterUtil.convertGeometryToPolygon(surveyAreaRequestDTO.getGeom()));
            surveyArea.setIsActive(surveyAreaRequestDTO.getIsActive());

            if(surveyAreaRequestDTO.getSurveyStartDate()!=null){
                if (surveyAreaRequestDTO.getSurveyStartDate().isBefore(LocalDate.now())) {
                    throw new RuntimeException("Survey start date cannot be in the past.");
                } else {
                    surveyArea.setSurveyStartDate(surveyAreaRequestDTO.getSurveyStartDate());
                }
            }
            if (surveyAreaRequestDTO.getSurveyEndDate() != null) {

                if (surveyAreaRequestDTO.getSurveyEndDate().isBefore(surveyAreaRequestDTO.getSurveyStartDate())) {
                    throw new RuntimeException("Survey end date cannot be before survey start date.");
                } else {
                    surveyArea.setSurveyEndDate(surveyAreaRequestDTO.getSurveyEndDate());
                }
            }
            surveyArea.setCreatedOn(Instant.now());
            surveyArea.setCreatedBy(surveyAreaRequestDTO.getUserId());

            surveyArea.setMvnoId(surveyAreaRequestDTO.getMvnoId());

            surveyAreaRepository.save(surveyArea);    //  Save to DB

        } catch (DataIntegrityViolationException ex) {
            logger.error("Survey Area with the same name already exists : " + ex.getMessage(), ex);
            throw new RuntimeException("Survey Area with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating surveyArea : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create surveyArea: " + ex.getMessage(), ex);
        }
    }

    public void updateSurveyArea(UUID publicId, SurveyAreaRequestDTO updatedSurveyAreaRequestDTO) {
        SurveyArea surveyArea = surveyAreaRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("SurveyArea with ID " + publicId + " not found."));

        try {
            surveyArea.setName(updatedSurveyAreaRequestDTO.getName());
            surveyArea.setDescription(updatedSurveyAreaRequestDTO.getDescription());

            LookupSurveyStatus lookupSurveyStatus = (LookupSurveyStatus) lookupSurveyStatusRepository.findById(updatedSurveyAreaRequestDTO.getSurveyStatusId())
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Survey Status not found"));
            surveyArea.setLookupSurveyStatus(lookupSurveyStatus);

            /**
             *  For create api default Survey stage name "Survey" added
             *  TODO : add here if required in future
             */

            surveyArea.setGeom(GeoJsonConverterUtil.convertGeometryToPolygon(updatedSurveyAreaRequestDTO.getGeom()));
            surveyArea.setIsActive(updatedSurveyAreaRequestDTO.getIsActive());


            surveyArea.setSurveyStartDate(updatedSurveyAreaRequestDTO.getSurveyStartDate());
            surveyArea.setSurveyEndDate(updatedSurveyAreaRequestDTO.getSurveyEndDate());

            surveyArea.setModifiedBy(updatedSurveyAreaRequestDTO.getUserId());
            surveyArea.setModifiedOn(Instant.now());    //  modified_on

            surveyArea.setMvnoId(updatedSurveyAreaRequestDTO.getMvnoId());

            surveyAreaRepository.save(surveyArea); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating surveyArea : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update surveyArea");
        }
    }

    @Transactional
    public void updateSurveyAreaStatus(UUID publicId, String surveyStatusName, Long userId,Integer mvnoId) {
        SurveyArea surveyArea = surveyAreaRepository.findByPublicIdAndMvnoId(publicId,mvnoId)
                .orElseThrow(() -> new EntityNotFoundException("Survey area not found with publicId"));

        try {
            // Fetch and set LookupSurveyStatus entity
            LookupSurveyStatus lookupSurveyStatus = lookupSurveyStatusRepository.findByNameIgnoreCase(surveyStatusName)
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Survey Status not found"));

            /*Note : Below if condition is used for demo purpose. When survey status is completed then we have to change the stage of survey
            from Survey to Design. Will remove this condition once workflow service is used in GIS.*/
            /*Start*/
            if(lookupSurveyStatus.getName().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_COMPLETED))
            {
                LookupSurveyStage surveyStage = lookupSurveyStageRepository.findByNameIgnoreCase(ApiConstants.SURVEY_STAGE_DESIGN);
                if(surveyStage != null)
                {
                    surveyArea.setLookupSurveyStage(surveyStage);
                }
            }
            if(lookupSurveyStatus.getName().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_APPROVED))
            {
                LookupSurveyStage surveyStage = lookupSurveyStageRepository.findByNameIgnoreCase(ApiConstants.SURVEY_STAGE_DIGITALIZATION);
                if(surveyStage != null)
                {
                    surveyArea.setLookupSurveyStage(surveyStage);
                }
            }
            if(lookupSurveyStatus.getName().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE))
            {
                LookupSurveyStage surveyStage = lookupSurveyStageRepository.findByNameIgnoreCase(ApiConstants.SURVEY_STAGE_BOM);
                if(surveyStage != null)
                {
                    surveyArea.setLookupSurveyStage(surveyStage);
                }
            }
            /*Stop*/
            surveyArea.setLookupSurveyStatus(lookupSurveyStatus);
            surveyArea.setModifiedBy(userId);
            surveyArea.setModifiedOn(Instant.now());
            surveyAreaRepository.save(surveyArea);
            if (surveyArea.getLookupSurveyStage() != null &&
                    surveyArea.getLookupSurveyStage().getName().equalsIgnoreCase(ApiConstants.SURVEY_STAGE_BOM)) {
                kafkaProducerService.sendSurveyDesignData(surveyArea, "BOM_STAGE_REACHED");
            }
            commonService.updateLatestStatus(surveyArea.getId(),lookupSurveyStatus.getName(),mvnoId);
        } catch (Exception ex) {
            logger.error("Error updating surveyArea status with publicId: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to updating surveyArea: " + ex.getMessage(), ex);
        }
    }


    @Transactional
    public void deleteSurveyAreaByPublicId(UUID publicId,Integer mvnoId) {
        if (!surveyAreaRepository.existsByPublicIdAndMvnoId(publicId,mvnoId)) {
            throw new EntityNotFoundException("SurveyArea with public Id not found : " + publicId);
        }

        try {
            surveyAreaRepository.deleteByPublicIdAndMvnoId(publicId,mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting surveyArea with publicId : " + publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete surveyArea: " + ex.getMessage(), ex);
        }
    }


    public SurveyArea getSurveyAreaByPublicId(UUID publicId, Integer mvnoId) {
        if (!surveyAreaRepository.existsByPublicIdAndMvnoId(publicId,mvnoId)) {
            throw new EntityNotFoundException("SurveyArea with public Id not found : " + publicId);
        }

        try {
            Optional<SurveyArea> entity = surveyAreaRepository.findByPublicIdAndMvnoId(publicId,mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting data method getSurveyAreaDataById : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data method getSurveyAreaById: " + ex.getMessage(), ex);
        }
    }

    public List<SurveyAreaResponseDTO> getSurveyAreaByUserId(Integer userId,Integer mvnoId, Boolean isAdmin) {
        try {
            List<Object[]> surveyAreaList = new ArrayList<>();
            if(isAdmin)
            {
                surveyAreaList = surveyAreaRepository.getInitiatedSurveyAreaByUserIdAndMvnoId(userId,mvnoId ,ApiConstants.LABEL_ROLE_ADMIN,ApiConstants.SURVEY_STATUS_INITIATED);
            }
            else
            {
                surveyAreaList = surveyAreaRepository.getSurveyAreaByUserIdAndMvnoId(userId,mvnoId ,ApiConstants.LABEL_ROLE_ADMIN);
            }

            if (surveyAreaList == null) {
                throw new IllegalArgumentException("No Survey Area found");
            } else if (surveyAreaList.isEmpty()) {
                throw new EntityNotFoundException("No Survey Area found.");
            }
            List<SurveyAreaResponseDTO> responseDtoList = new ArrayList<>();

            for (Object[] surveyAreaObj : surveyAreaList) {
                SurveyAreaResponseDTO responseDto = new SurveyAreaResponseDTO();
                responseDto.setId((Integer) surveyAreaObj[0]);
                responseDto.setPublicId((UUID) surveyAreaObj[1]);
                responseDto.setName((String) surveyAreaObj[2]);

                responseDto.setStatusId((Integer) surveyAreaObj[3]);
                responseDto.setSurveyStatusName((String) surveyAreaObj[4]);
                responseDto.setSurveyStageName((String) surveyAreaObj[5]);

                responseDtoList.add(responseDto);
            }

            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error getting survey area details :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get survey area details: " + ex.getMessage(), ex);
        }
    }

    public List<SurveyAreaResponseDTO> getSurveyAreaByUserIdAtLayer(Integer userId,Integer mvnoId) {
        try {
            List<Object[]> surveyAreaList = surveyAreaRepository.getSurveyAreaByUserIdAtLayerAndMvnoId(userId, ApiConstants.LABEL_ROLE_ADMIN,mvnoId);

            if (surveyAreaList == null) {
                throw new IllegalArgumentException("No Survey Area found");
            } else if (surveyAreaList.isEmpty()) {
                throw new EntityNotFoundException("No Survey Area found.");
            }
            List<SurveyAreaResponseDTO> responseDtoList = new ArrayList<>();

            for (Object[] surveyAreaObj : surveyAreaList) {
                SurveyAreaResponseDTO responseDto = new SurveyAreaResponseDTO();
                responseDto.setId((Integer) surveyAreaObj[0]);
                responseDto.setPublicId((UUID) surveyAreaObj[1]);
                responseDto.setName((String) surveyAreaObj[2]);

                responseDto.setStatusId((Integer) surveyAreaObj[3]);
                responseDto.setSurveyStatusName((String) surveyAreaObj[4]);

                responseDto.setSurveyStageName((String) surveyAreaObj[5]);

                responseDtoList.add(responseDto);
            }

            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error getting survey area details :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get survey area details: " + ex.getMessage(), ex);
        }
    }

    @Override
    public List<SurveyArea> getAllSurveys(Integer mvnoId) {
        try {
            return surveyAreaRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }    }


//    @Override
//    public List<SurveyArea> findByLookupSurveyStatus(String initiated) {
//        try {
//            return surveyAreaRepository.findByLookupSurveyStatus(lookupSurveyStatusRepository.findByNameIgnoreCase(initiated));
//        } catch (Throwable e) {
//            throw new RuntimeException(e.getMessage());
//        }
//    }

//
//    @Override
//    public SurveyArea getSurveyStatusId(Integer lookupSurveyStatus) {
//        List<SurveyArea> custOpt = new ArrayList<>();
//        try {
//            custOpt = surveyAreaRepository.findBySurveyStatusId(lookupSurveyStatus);
//        } catch (Exception e) {
//            // Log the error and rethrow if necessary
//            System.err.println("Error fetching SurveyArea by SurveyStatus: " + e.getMessage());
//            throw new RuntimeException("Failed to retrieve survey areas", e);
//        }
//        return null;
//    }
}