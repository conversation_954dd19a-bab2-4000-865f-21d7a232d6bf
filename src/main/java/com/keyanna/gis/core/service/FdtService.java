package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.FdtDTO;
import com.keyanna.gis.core.model.Fdt;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface FdtService {
    Fdt create(FdtDTO dto) throws ParseException;

    List<Fdt> getAllFdts(Integer mvnoId);

    public void updateFdt(UUID publicId, FdtDTO fdtDTO);
    Fdt getFdtByPublicId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
}
