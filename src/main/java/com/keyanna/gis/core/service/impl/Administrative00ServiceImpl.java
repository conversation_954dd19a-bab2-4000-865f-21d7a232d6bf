package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.Administrator00DTO;
import com.keyanna.gis.core.repository.Administartor00Repository;
import com.keyanna.gis.core.utility.AdministratorDtoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class Administrative00ServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(Administrative00ServiceImpl.class);

    private final Administartor00Repository repository;

    public Administrative00ServiceImpl(Administartor00Repository repository) {
        this.repository = repository;
    }

    public Administrator00DTO getSingleByAdm0Code(String adm0Code) {
        try {
            List<Object[]> rows = repository.findSelectedFieldsByAdm0Code(adm0Code);
            if (rows.isEmpty()) {
                return null;
            }
            return AdministratorDtoMapper.map(rows.get(0));
        } catch (Exception ex) {
            logger.error("Error fetching single Administrator00 by code: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to fetch single Administrator00");
        }

    }

    public List<Administrator00DTO> getAllByAdm0Code(String adm0Code) {
        try {
            List<Object[]> rows = repository.findSelectedFieldsByAdm0Code(adm0Code);
            return AdministratorDtoMapper.mapList(rows);
        } catch (Exception ex) {
            logger.error("Error fetching all Administrator00 by code: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to fetch all Administrator00");
        }


    }
}

