package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.CduDTO;
import com.keyanna.gis.core.dto.response.CduWithImageDTO;
import com.keyanna.gis.core.model.Cdu;
import org.locationtech.jts.io.ParseException;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface CduService {
    Cdu create(CduDTO dto) throws ParseException;
    public void updateCdu(UUID publicId, CduDTO cduDTO);
    List<Cdu> getAllCdu(Integer mvnoId);
    CduWithImageDTO getCduByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    Cdu createWithImage(CduDTO dto, List<MultipartFile> imageFiles) throws ParseException;

    Map<String,Object> previewUploadedImage(String directory, String fileName);
    Map<String,Object> previewImageByName(String fileName);
}



