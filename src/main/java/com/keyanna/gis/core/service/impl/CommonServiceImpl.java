package com.keyanna.gis.core.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.Kafka.KafkaProducerService;
import com.keyanna.gis.core.Kafka.kafkaDto.*;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.ExcelResponseDTO;
import com.keyanna.gis.core.dto.MduDTO;
import com.keyanna.gis.core.dto.request.CommonRequestDTO;
import com.keyanna.gis.core.dto.response.PolygonResponseDTO;
import com.keyanna.gis.core.model.*;
import com.keyanna.gis.core.repository.*;
import com.keyanna.gis.core.repository.SurveyBomVersionMappingRepository;
import com.keyanna.gis.core.service.CommonService;
import com.keyanna.gis.core.utility.CommonUtil;
import com.keyanna.gis.core.utility.component.BomExcelGenerate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import static com.keyanna.gis.core.constants.ApiConstants.*;

@Service
public class CommonServiceImpl implements CommonService {

    private static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);
    private final CommonRepository commonRepository;
    private final ObjectMapper objectMapper;
    private final RepositoryRegistry repositoryRegistry;
    private final LayerMasterRepository layerMasterRepository;
    private final SurveyAreaRepository surveyAreaRepository;
    private final BomExcelGenerate bomExcelGenerate;
    private final CableRepository cableRepository;
    private final CduRepository cduRepository;
    private final CustomerRepository customerRepository;
    private final DuctRepository ductRepository;
    private final FatRepository fatRepository;
    private final FdpRepository fdpRepository;
    private final FdtRepository fdtRepository;
    private HandholeRepository handholeRepository;
    private final JointClosureRepository jointClosureRepository;
    private final ManholeRepository manholeRepository;
    private final MduRepository mduRepository;
    private final OdfRepository odfRepository;
    private final OltRepository oltRepository;
    private final PoleRepository poleRepository;
    private final PopRepository popRepository;
    private final SduRepository sduRepository;
    private final SplitterRepository splitterRepository;
    private final TrenchLayerRepository trenchRepository;
    private final StreetRepository streetRepository;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final KafkaProducerService kafkaProducerService;

    @Value("${search.radius.meter}")
    private Double searchRadiusMeter;

    @Value("${radius.length.meters}")
    private Double radiusLengthMeters;

    public CommonServiceImpl(
            CommonRepository commonRepository,
            ObjectMapper objectMapper,
            RepositoryRegistry repositoryRegistry,
            LayerMasterRepository layerMasterRepository,
            SurveyAreaRepository surveyAreaRepository,
            BomExcelGenerate bomExcelGenerate, CableRepository cableRepository, CduRepository cduRepository, CustomerRepository customerRepository,
            DuctRepository ductRepository, FatRepository fatRepository, FdpRepository fdpRepository,
            FdtRepository fdtRepository, HandholeRepository handholeRepository, JointClosureRepository jointClosureRepository,
            ManholeRepository manholeRepository, MduRepository mduRepository, OdfRepository odfRepository,
            OltRepository oltRepository, PoleRepository poleRepository, PopRepository popRepository,
            SduRepository sduRepository, SplitterRepository splitterRepository, TrenchLayerRepository trenchRepository,StreetRepository streetRepository,
            ImageServiceImpl imageServiceImpl, LayerInventoryMappingRepository layerInventoryMappingRepository, KafkaProducerService kafkaProducerService) {
        this.commonRepository = commonRepository;
        this.objectMapper = objectMapper;
        this.repositoryRegistry = repositoryRegistry;
        this.layerMasterRepository = layerMasterRepository;
        this.surveyAreaRepository = surveyAreaRepository;
        this.bomExcelGenerate = bomExcelGenerate;
        this.cableRepository = cableRepository;
        this.cduRepository = cduRepository;
        this.customerRepository = customerRepository;
        this.ductRepository = ductRepository;
        this.fatRepository = fatRepository;
        this.fdpRepository = fdpRepository;
        this.fdtRepository = fdtRepository;
        this.handholeRepository = handholeRepository;
        this.jointClosureRepository = jointClosureRepository;
        this.manholeRepository = manholeRepository;
        this.mduRepository = mduRepository;
        this.odfRepository = odfRepository;
        this.oltRepository = oltRepository;
        this.poleRepository = poleRepository;
        this.popRepository = popRepository;
        this.sduRepository = sduRepository;
        this.splitterRepository = splitterRepository;
        this.trenchRepository = trenchRepository;
        this.streetRepository = streetRepository;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.kafkaProducerService = kafkaProducerService;
    }

    //  TODO DO not delete below method code, Currently not in use
//    public Map<String, Object> getGeometriesWithinPolygon(PolygonRequestDTO polygonRequestDTO) {
//        try {
//            String polygonGeomString = objectMapper.writeValueAsString(polygonRequestDTO);
//
//            // Fetch the JSONB response from the database
//            String jsonResponse = filterRepository.findGeometriesWithinPolygon(polygonGeomString);
//
//            // Parse the JSON response
//            JsonNode rootNode = objectMapper.readTree(jsonResponse);
//
//            return buildResponseMap(rootNode);  //  For all layers
//        } catch (Exception ex) {
//            logger.error("Error creating filter : " + ex.getMessage(), ex);
//            throw new RuntimeException("Failed to create filter: " + ex.getMessage(), ex);
//        }
//    }
//
//    /**
//     * Get table wise data, iterate and put into response DTO
//     *
//     * @param rootNode
//     * @return
//     * @throws IOException
//     */
//    private Map<String, Object> buildResponseMap(JsonNode rootNode) throws IOException {
//        Map<String, Object> responseMap = new HashMap<>();
//
//        //  cable
//        JsonNode cableArrayNode = rootNode.path(ApiConstants.LABEL_CABLE);
//        List<PolygonResponseDTO.Cable> cableResponseDtoList = CommonUtil.convertCableJsonToResponseDTO(cableArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_CABLE, cableResponseDtoList);
//
//        //  fat
//        JsonNode fatArrayNode = rootNode.path(ApiConstants.LABEL_FAT);
//        List<PolygonResponseDTO.Fat> fatResponseDtoList = CommonUtil.convertFatJsonToResponseDTO(fatArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_FAT, fatResponseDtoList);
//
//        //  pop
//        JsonNode popArrayNode = rootNode.path(ApiConstants.LABEL_POP);
//        List<PolygonResponseDTO.Pop> popResponseDtoList = CommonUtil.convertPopJsonToResponseDTO(popArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_POP, popResponseDtoList);
//
//        //  fdp
//        JsonNode fdpArrayNode = rootNode.path(ApiConstants.LABEL_FDP);
//        List<PolygonResponseDTO.Fdp> fdpResponseDtoList = CommonUtil.convertFdpJsonToResponseDTO(fdpArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_FDP, fdpResponseDtoList);
//
//        //  fdt
//        JsonNode fdtArrayNode = rootNode.path(ApiConstants.LABEL_FDT);
//        List<PolygonResponseDTO.Fdt> fdtResponseDtoList = CommonUtil.convertFdtJsonToResponseDTO(fdtArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_FDT, fdtResponseDtoList);
//
//        //  customer
//        JsonNode customerArrayNode = rootNode.path(ApiConstants.LABEL_CUSTOMER);
//        List<PolygonResponseDTO.Customer> customerResponseDtoList = CommonUtil.convertCustomerJsonToResponseDTO(customerArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_CUSTOMER, customerResponseDtoList);
//
//        //  splitter
//        JsonNode splitterArrayNode = rootNode.path(ApiConstants.LABEL_SPLITTER);
//        List<PolygonResponseDTO.Splitter> splitterResponseDtoList = CommonUtil.convertSplitterJsonToResponseDTO(splitterArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LABEL_SPLITTER, splitterResponseDtoList);
//
//        return responseMap;
//    }

    public Map<String, Object> getDataInSurveyArea(Integer surveyAreaId, Integer mvnoId) {
        try {
            // Fetch the JSONB response from the database

            String jsonResponse = commonRepository.getDataInSurveyArea(surveyAreaId, mvnoId);

            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            return buildResponseMapForDISA(rootNode);  //  For all layers
        } catch (Exception ex) {
            logger.error("Error getting data : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data : " + ex.getMessage(), ex);
        }
    }

    /**
     * For method : getDataInSurveyArea : DISA
     */
    private Map<String, Object> buildResponseMapForDISA(JsonNode rootNode) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();

        //  fdt
        JsonNode fdtArrayNode = rootNode.path(ApiConstants.LABEL_FDT);
        List<PolygonResponseDTO.PointDataInSurveyArea> fdtResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(fdtArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_FDT, fdtResponseDtoList);

        // SDU (Table : ne_sdu)
        JsonNode sduArrayNode = rootNode.path(ApiConstants.LABEL_SDU);
        List<PolygonResponseDTO.PointDataInSurveyArea> sduResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(sduArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_SDU, sduResponseDtoList);

        // MDU (Table : ne_mdu)
        JsonNode mduArrayNode = rootNode.path(ApiConstants.LABEL_MDU);
        List<PolygonResponseDTO.PointDataInSurveyArea> mduResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(mduArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_MDU, mduResponseDtoList);

        // CDU (Table : ne_cdu)
        JsonNode cduArrayNode = rootNode.path(ApiConstants.LABEL_CDU);
        List<PolygonResponseDTO.PointDataInSurveyArea> cduResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(cduArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_CDU, cduResponseDtoList);

        //  cable
        JsonNode cableArrayNode = rootNode.path(ApiConstants.LABEL_CABLE);
        List<PolygonResponseDTO.LineStringDataInSurveyArea> cableResponseDtoList = CommonUtil.convertLineStringJsonDataToResponseDtoDISA(cableArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_CABLE, cableResponseDtoList);

        //  customer
        JsonNode customerArrayNode = rootNode.path(ApiConstants.LABEL_CUSTOMER);
        List<PolygonResponseDTO.PointDataInSurveyArea> customerResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(customerArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_CUSTOMER, customerResponseDtoList);

        //  fat
        JsonNode fatArrayNode = rootNode.path(ApiConstants.LABEL_FAT);
        List<PolygonResponseDTO.PointDataInSurveyArea> fatResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(fatArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_FAT, fatResponseDtoList);

        //  fdp
        JsonNode fdpArrayNode = rootNode.path(ApiConstants.LABEL_FDP);
        List<PolygonResponseDTO.PointDataInSurveyArea> fdpResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(fdpArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_FDP, fdpResponseDtoList);

        //  pop
        JsonNode popArrayNode = rootNode.path(ApiConstants.LABEL_POP);
        List<PolygonResponseDTO.PointDataInSurveyArea> popResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(popArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_POP, popResponseDtoList);

        //  splitter
        JsonNode splitterArrayNode = rootNode.path(ApiConstants.LABEL_SPLITTER);
        List<PolygonResponseDTO.PointDataInSurveyArea> splitterResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(splitterArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_SPLITTER, splitterResponseDtoList);

        //  handhole
        JsonNode handholeArrayNode = rootNode.path(ApiConstants.LABEL_HANDHOLE);
        List<PolygonResponseDTO.PointDataInSurveyArea> handholeResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(handholeArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_HANDHOLE, handholeResponseDtoList);

        //  manhole
        JsonNode manholeArrayNode = rootNode.path(ApiConstants.LABEL_MANHOLE);
        List<PolygonResponseDTO.PointDataInSurveyArea> manholeResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(manholeArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_MANHOLE, manholeResponseDtoList);

        //  pole_size_type: 8m (Table : ne_pole)
        //  eightM
        JsonNode eightMArrayNode = rootNode.path(ApiConstants.LABEL_POLE_SIZE_TYPE_8M);
        List<PolygonResponseDTO.PointDataInSurveyArea> eightMResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(eightMArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_POLE_SIZE_TYPE_8M, eightMResponseDtoList);

        //  pole_size_type: 10m (Table : ne_pole)
        //  tenM
        JsonNode tenMArrayNode = rootNode.path(ApiConstants.LABEL_POLE_SIZE_TYPE_10M);
        List<PolygonResponseDTO.PointDataInSurveyArea> tenMResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(tenMArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_POLE_SIZE_TYPE_10M, tenMResponseDtoList);

        //  pole_size_type: 12m (Table : ne_pole)
        //  twelveM
        JsonNode twelveMArrayNode = rootNode.path(ApiConstants.LABEL_POLE_SIZE_TYPE_12M);
        List<PolygonResponseDTO.PointDataInSurveyArea> twelveMResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(twelveMArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_POLE_SIZE_TYPE_12M, twelveMResponseDtoList);

        //  trench
        JsonNode trenchArrayNode = rootNode.path(ApiConstants.LABEL_TRENCH);
        List<PolygonResponseDTO.LineStringDataInSurveyArea> trenchResponseDtoList = CommonUtil.convertLineStringJsonDataToResponseDtoDISA(trenchArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_TRENCH, trenchResponseDtoList);

        //  duct
        JsonNode ductArrayNode = rootNode.path(ApiConstants.LABEL_DUCT);
        List<PolygonResponseDTO.LineStringDataInSurveyArea> ductResponseDtoList = CommonUtil.convertLineStringJsonDataToResponseDtoDISA(ductArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_DUCT, ductResponseDtoList);

        //  jointClosure
        JsonNode jointClosureArrayNode = rootNode.path(ApiConstants.LABEL_JOINT_CLOSURE);
        List<PolygonResponseDTO.PointDataInSurveyArea> jointClosureResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(jointClosureArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_JOINT_CLOSURE, jointClosureResponseDtoList);

        //  olt
        JsonNode oltArrayNode = rootNode.path(ApiConstants.LABEL_OLT);
        List<PolygonResponseDTO.PointDataInSurveyArea> oltResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(oltArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_OLT, oltResponseDtoList);

        //  odf
        JsonNode odfArrayNode = rootNode.path(ApiConstants.LABEL_ODF);
        List<PolygonResponseDTO.PointDataInSurveyArea> odfResponseDtoList = CommonUtil.convertPointJsonDataToResponseDtoDISA(odfArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_ODF, odfResponseDtoList);

        //  surveyArea
        JsonNode surveyAreaArrayNode = rootNode.path(ApiConstants.LABEL_SURVEY_AREA);
        List<PolygonResponseDTO.PolygonDataInSurveyArea> surveyAreaResponseDtoList = CommonUtil.convertPolygonJsonDataToResponseDtoDISA(surveyAreaArrayNode, objectMapper);
        responseMap.put(ApiConstants.LABEL_SURVEY_AREA, surveyAreaResponseDtoList);

        return responseMap;
    }
    @Transactional
    public CommonService.ExcelExportResult getDataInSurveyAreaBom(Integer surveyAreaId) {
        try {
            // Check if Survey Area Data is available in given id
            SurveyArea surveyArea = surveyAreaRepository.findById(surveyAreaId)
                    .orElseThrow(() -> new RuntimeException("Survey area not found for id: " + surveyAreaId));

            if(surveyArea.getLookupSurveyStage() != null)
            {
                if(!surveyArea.getLookupSurveyStage().getName().equals(ApiConstants.SURVEY_STAGE_BOM))
                {
                    throw new RuntimeException("Survey is not in bom stage for id: " + surveyAreaId);
                }
            }
            else
            {
                throw new RuntimeException("Survey is not in bom stage for id: " + surveyAreaId);
            }
            // Fetch the JSONB response from the database
            String jsonResponse = commonRepository.getDataInSurveyAreaBom(surveyAreaId);

            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            logger.debug("printing: entry request: {}", rootNode);

            ExcelResponseDTO excelResponseDTO = objectMapper.treeToValue(rootNode, ExcelResponseDTO.class);

            return bomExcelGenerate.generateExcel(excelResponseDTO, surveyAreaId, 1L);  //  For all layers
        } catch (Exception ex) {
            logger.error("Error getting data : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data: " + ex.getMessage(), ex);
        }
    }

    /**
     * For method : getDataInSurveyAreaBom : DISAB
     * TODO : Note : This method is currently not in use
     */
    private Map<String, Object> buildResponseMapForDISAB(JsonNode rootNode) {
        Map<String, Object> responseMap = new HashMap<>();

        //  customer
        JsonNode customerArrayNode = rootNode.path(ApiConstants.LABEL_CUSTOMER);
        List<PolygonResponseDTO.CustomerDto> customerResponseList = CommonUtil.convertCustomerJsonToResponseDTO(customerArrayNode);
        responseMap.put(ApiConstants.LABEL_CUSTOMER, customerResponseList);

        //  fat
//        JsonNode fatArrayNode = rootNode.path(ApiConstants.LABEL_FAT);
//        List<PolygonResponseDTO.FatDto> fatResponseList = CommonUtil.convertFatJsonToResponseDTO(fatArrayNode);
//        responseMap.put(ApiConstants.LABEL_FAT, fatResponseList);

        //  fdt
//        JsonNode fdtArrayNode = rootNode.path(ApiConstants.LABEL_FDT);
//        List<PolygonResponseDTO.FdtDto> fdtResponseList = CommonUtil.convertFdtJsonToResponseDTO(fdtArrayNode);
//        responseMap.put(ApiConstants.LABEL_FDT, fdtResponseList);

        //  fdp
        JsonNode fdpArrayNode = rootNode.path(ApiConstants.LABEL_FDP);
        List<PolygonResponseDTO.FdpDto> fdpResponseList = CommonUtil.convertFdpJsonToResponseDTO(fdpArrayNode);
        responseMap.put(ApiConstants.LABEL_FDP, fdpResponseList);

        //  handhole
        JsonNode handholeArrayNode = rootNode.path(ApiConstants.LABEL_HANDHOLE);
        List<PolygonResponseDTO.HandholeDto> handholeResponseList = CommonUtil.convertHandholeJsonToResponseDTO(handholeArrayNode);
        responseMap.put(ApiConstants.LABEL_HANDHOLE, handholeResponseList);

        //  jointClosure
//        JsonNode jointClosureArrayNode = rootNode.path(ApiConstants.LABEL_JOINT_CLOSURE);
//        List<PolygonResponseDTO.JointClosureDto> jointClosureResponseList = CommonUtil.convertJointClosureJsonToResponseDTO(jointClosureArrayNode);
//        responseMap.put(ApiConstants.LABEL_JOINT_CLOSURE, jointClosureResponseList);

        //  manhole
        JsonNode manholeArrayNode = rootNode.path(ApiConstants.LABEL_MANHOLE);
        List<PolygonResponseDTO.ManholeDto> manholeResponseList = CommonUtil.convertManholeJsonToResponseDTO(manholeArrayNode);
        responseMap.put(ApiConstants.LABEL_MANHOLE, manholeResponseList);

        //  pole
        JsonNode poleArrayNode = rootNode.path(ApiConstants.LABEL_POLE);
        List<PolygonResponseDTO.PoleDto> poleResponseList = CommonUtil.convertPoleJsonToResponseDTO(poleArrayNode);
        responseMap.put(ApiConstants.LABEL_POLE, poleResponseList);

        //  pop
        JsonNode popArrayNode = rootNode.path(ApiConstants.LABEL_POP);
        List<PolygonResponseDTO.PopDto> popResponseList = CommonUtil.convertPopJsonToResponseDTO(popArrayNode);
        responseMap.put(ApiConstants.LABEL_POP, popResponseList);

        //  splitter
//        JsonNode splitterArrayNode = rootNode.path(ApiConstants.LABEL_SPLITTER);
//        List<PolygonResponseDTO.SplitterDto> splitterResponseList = CommonUtil.convertSplitterJsonToResponseDTO(splitterArrayNode);
//        responseMap.put(ApiConstants.LABEL_SPLITTER, splitterResponseList);

        //  cable
//        JsonNode cableArrayNode = rootNode.path(ApiConstants.LABEL_CABLE);
//        List<PolygonResponseDTO.CableDto> cableResponseList = CommonUtil.convertCableJsonToResponseDTO(cableArrayNode);
//        responseMap.put(ApiConstants.LABEL_CABLE, cableResponseList);

        //  duct
        JsonNode ductArrayNode = rootNode.path(ApiConstants.LABEL_DUCT);
        List<PolygonResponseDTO.DuctDto> ductResponseList = CommonUtil.convertDuctJsonToResponseDTO(ductArrayNode);
        responseMap.put(ApiConstants.LABEL_DUCT, ductResponseList);

        //  trench
        JsonNode trenchArrayNode = rootNode.path(ApiConstants.LABEL_TRENCH);
        List<PolygonResponseDTO.TrenchDto> trenchResponseList = CommonUtil.convertTrenchJsonToResponseDTO(trenchArrayNode);
        responseMap.put(ApiConstants.LABEL_TRENCH, trenchResponseList);

        return responseMap;
    }

    public Map<String, Object> findParentNearChildGeom(CommonRequestDTO request) {
        try {
            String geomString = "";

            if (request.getGeomType().equals(ApiConstants.LABEL_POINT)) {
                geomString = objectMapper.writeValueAsString(request.getGeomPoint());
            } else if (request.getGeomType().equals(ApiConstants.LABEL_LINESTRING)) {
                geomString = objectMapper.writeValueAsString(request.getGeomLineString());
            }

            // Fetch the JSONB response from the database
            String jsonResponse = commonRepository.findParentNearChildGeom(request.getChildLayerId(), geomString, searchRadiusMeter);

            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            return buildResponseMapForParentData(rootNode);
        } catch (Exception ex) {
            logger.error("Error creating filter findParentDataNearChild : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create filter findParentDataNearChild: " + ex.getMessage(), ex);
        }
    }

    private Map<String, Object> buildResponseMapForParentData(JsonNode rootNode) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();

        // Iterate over the fields in the root JSON object
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String layerNameVar = entry.getKey(); // e.g., "pole", "trench"
            JsonNode featuresArray = entry.getValue();

            List<Map<String, Object>> featureList = new ArrayList<>();
            if (featuresArray.isArray()) {
                for (JsonNode featureNode : featuresArray) {
                    /**
                     * If needed add into response dto in future
                     */
                    Map<String, Object> featureMap = new HashMap<>();
                    featureMap.put("publicId", featureNode.get("public_id").asText());
                    featureMap.put("name", featureNode.get("name").asText());
                    featureMap.put("status", featureNode.get("status").asText());

                    // Handle the geometry
                    String geomType = featureNode.get("geomType").asText();
                    String geoJsonString = featureNode.path("geom").asText();
                    if (geomType.equals("ST_Point")) {
                        featureMap.put("geom", objectMapper.readValue(geoJsonString, PolygonResponseDTO.GeometryPointDto.class));
                    } else if (geomType.equals("ST_LineString")) {
                        featureMap.put("geom", objectMapper.readValue(geoJsonString, PolygonResponseDTO.GeometryLineStringDto.class));
                    }

                    featureMap.put("layerName", featureNode.get("layer_name").asText());

                    featureList.add(featureMap);
                }
            }

            responseMap.put(layerNameVar, featureList);
        }
        return responseMap;
    }

    public Map<String, Object> findNearByGeom(CommonRequestDTO request) {
        try {
            String geoJsonInputString = objectMapper.writeValueAsString(request.getGeomPoint());

            //  Getting Ex. "fdp, splitter" from request
            String[] tableNamesArray = Optional.ofNullable(request.getLayerNames())
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(s -> Arrays.stream(s.split(","))
                            .map(String::trim)
                            .filter(name -> !name.isEmpty())
                            .map(name -> "ne_" + name)
                            .toArray(String[]::new))
                    .orElse(new String[0]);

            // Fetch the JSONB response from the database
            String jsonResponse = commonRepository.findNearByGeom(geoJsonInputString, radiusLengthMeters, tableNamesArray);

            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            return buildResponseMapForParentData(rootNode);
        } catch (Exception ex) {
            logger.error("Error creating filter findParentDataNearChild : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create filter findParentDataNearChild: " + ex.getMessage(), ex);
        }
    }

    public Object getNetworkElementDataById(UUID publicId, String layerName) {
        String tableName = layerMasterRepository.findByDisplayName(layerName)
                .map(LayerMaster::getTableName)
                .orElseThrow(() -> new RuntimeException("Layer not found for display name: " + layerName));

        try {
            PublicIdRepository<?> repository = repositoryRegistry.getRepository(tableName);
            if (repository == null) {
                throw new IllegalArgumentException("Unknown table name: " + layerName);
            }

            Optional<?> entity = repository.findByPublicId(publicId);
            return entity.orElse(null);
        } catch (Exception ex) {
            logger.error("Error getting data method getNetworkElementDataById : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data method getNetworkElementDataById: " + ex.getMessage(), ex);
        }
    }

    public List<Map<String, Object>> getStaffDetails(Integer mvnoId) {
        try {
            return commonRepository.getStaffDetailsExcludingRoleAndMvnoId(ApiConstants.LABEL_ROLE_ADMIN, mvnoId);
        } catch (Exception ex) {
            logger.error("Error getting staff : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to getting staff : " + ex.getMessage(), ex);
        }
    }

//  TODO NOT REMOVE:Use this code while working with method : previewImageByName
    //    private final LayerImageMappingRepository layerImageMappingRepository;
//    public CommonServiceImpl(LayerImageMappingRepository layerImageMappingRepository) {
//        this.layerImageMappingRepository = layerImageMappingRepository;
//    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateLatestStatus(Integer surveyAreaId, String status, Integer mvnoid) {
//       boolean result = false;
        try {

            if (cableRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                cableRepository.updateStatus(surveyAreaId, status, mvnoid);
              List<Cable> cable = cableRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Cable c : cable) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_CABLE, c.getId());

                }
            }
            if (cduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                cduRepository.updateStatus(surveyAreaId, status, mvnoid);
                List<Cdu> cdu = cduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                List<CduKafkaDTO> cduDtoList = new ArrayList<>();
                for (Cdu c : cdu) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_CDU, c.getId());
                    if (c.getStatus() != null && c.getStatus().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE)){

                        CduKafkaDTO dto = new CduKafkaDTO(c);
                        cduDtoList.add(dto);
                    }

                }
                if(!cduDtoList.isEmpty()) {
                    kafkaProducerService.sendNetworkElementData(cduDtoList, "CDU", "STATUS_UPDATED");
                }
            }
            if (customerRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                customerRepository.updateStatus(surveyAreaId, status, mvnoid);
                List<Customer> customer = customerRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Customer c : customer) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_CUSTOMER, c.getId());
                }
            }

            if (ductRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                ductRepository.updateStatus(surveyAreaId, status, mvnoid);
                List<Duct> duct = ductRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Duct d : duct) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_DUCT, d.getId());
                }
            }
            if (fatRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                fatRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Fat> fat = fatRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                List<FatKafkaDTO> fatDtoList = new ArrayList<>();
                for (Fat f : fat) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_FAT, f.getId());
                    if (f.getStatus() != null && f.getStatus().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE)) {
                        // Use constructor to create DTO - much cleaner!
                        FatKafkaDTO dto = new FatKafkaDTO(f);

//                    // Check if FAT has inventory list and set it
//                    List<LayerInventoryMapping> inventory = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(f.getId(), LAYER_CODE_FAT);
//                    if (inventory != null && !inventory.isEmpty()) {
//                        List<Inventory> inventoryList = new ArrayList<>();
//                        for (LayerInventoryMapping mapping : inventory) {
//                            Inventory inv = new Inventory();
//                            inv.setParentParamId(mapping.getParentParamId());
//                            inv.setParamId(mapping.getParamId());
//                            inv.setParamName(mapping.getParamName());
//                            inv.setParamValue(mapping.getParamValue());
//                            inv.setIsMandatory(mapping.getIsMandatory());
//                            inv.setIsAccessory(mapping.getIsAccessory());
//                            inv.setQuantity(mapping.getQuantity());
//                            inv.setIsConfiguration(mapping.getIsConfiguration());
//                            inventoryList.add(inv);
//                        }
//                        dto.setInventoryList(inventoryList);
//                    }

                        fatDtoList.add(dto);
                    }
                }
                if(!fatDtoList.isEmpty()) {
                    kafkaProducerService.sendNetworkElementData(fatDtoList, "FAT", "STATUS_UPDATED");
                }
            }

            if (fdpRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                fdpRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Fdp> fdp = fdpRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Fdp f : fdp) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_FDP, f.getId());
                }
            }
            if (fdtRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                fdtRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Fdt> fdt = fdtRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Fdt f : fdt) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_FDT, f.getId());
                }
            }
            if (handholeRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                handholeRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Handhole> handhole = handholeRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Handhole h : handhole) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_HANDHOLE, h.getId());
                }
            }
            if (jointClosureRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                jointClosureRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<JointClosure> jointClosure = jointClosureRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (JointClosure j : jointClosure) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_JOINT_CLOSURE, j.getId());
                }
            }
            if (manholeRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {

                manholeRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Manhole> manhole = manholeRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Manhole m : manhole) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_MANHOLE, m.getId());
                }
            }
            if (mduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                mduRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Mdu> mdu = mduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                List<MduKafkaDTO> mduDtoList = new ArrayList<>();
                for (Mdu m : mdu) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_MDU, m.getId());
                    if (m.getStatus() != null && m.getStatus().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE)) {

                        // Use constructor to create DTO - much cleaner!
                        MduKafkaDTO dto = new MduKafkaDTO(m);
                        mduDtoList.add(dto);
                    }
                }
                if(!mduDtoList.isEmpty()) {
                    kafkaProducerService.sendNetworkElementData(mduDtoList, "MDU", "STATUS_UPDATED");
                }
            }
//            if (mduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
//                mduRepository.updateStatus(surveyAreaId, status, mvnoid);
//                List<Mdu> mdus = mduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
//                for (Mdu m : mdus) {
//                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_MDU, m.getId());
//
//                    List<LayerInventoryMapping> inventory = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(m.getId(), LAYER_CODE_MDU);
//                    MduWithImageDTO mduWithInventory = new MduWithImageDTO(m, Collections.emptyList(), inventory);
//
//                    kafkaProducerService.sendNetworkElementData(mduWithInventory, "MDU", "STATUS_UPDATED");
//                }
//            }

            if (odfRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {

                odfRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Odf> odf = odfRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Odf o : odf) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_ODF, o.getId());
                }
            }
            if (oltRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                oltRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Olt> olt = oltRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Olt o : olt) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_OLT, o.getId());
                }
            }
            if (poleRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                poleRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Pole> pole = poleRepository.existsForSurveyArea(surveyAreaId,mvnoid);
                List<PoleKafkaDTO> poleDtoList = new ArrayList<>();
                for (Pole p : pole) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_POLE, p.getId());
                    if (p.getStatus() != null && p.getStatus().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE)) {
                        // Use constructor to create DTO - much cleaner!
                        PoleKafkaDTO dto = new PoleKafkaDTO(p);

//                    // Check if POLE has inventory list and set it
//                    List<LayerInventoryMapping> inventory = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(p.getId(), LAYER_CODE_POLE);
//                    if (inventory != null && !inventory.isEmpty()) {
//                        List<Inventory> inventoryList = new ArrayList<>();
//                        for (LayerInventoryMapping mapping : inventory) {
//                            Inventory inv = new Inventory();
//                            inv.setParentParamId(mapping.getParentParamId());
//                            inv.setParamId(mapping.getParamId());
//                            inv.setParamName(mapping.getParamName());
//                            inv.setParamValue(mapping.getParamValue());
//                            inv.setIsMandatory(mapping.getIsMandatory());
//                            inv.setIsAccessory(mapping.getIsAccessory());
//                            inv.setQuantity(mapping.getQuantity());
//                            inv.setIsConfiguration(mapping.getIsConfiguration());
//                            inventoryList.add(inv);
//                        }
//                        dto.setInventoryList(inventoryList);
//                    }

                        poleDtoList.add(dto);
                    }
                }
                if(!poleDtoList.isEmpty()) {
                    kafkaProducerService.sendNetworkElementData(poleDtoList, "POLE", "STATUS_UPDATED");
                }
            }
            if (popRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                popRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Pop> pop = popRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Pop p : pop) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_POP, p.getId());
                }
            }
            if (sduRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                sduRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Sdu> sdu = sduRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                List<SduKafkaDTO> sduDtoList = new ArrayList<>();
                for (Sdu s : sdu) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_SDU, s.getId());

                    if (s.getStatus() != null && s.getStatus().equalsIgnoreCase(ApiConstants.SURVEY_STATUS_DONE)) {
                        SduKafkaDTO dto = new SduKafkaDTO(s);
                        sduDtoList.add(dto);
                    }
                }
                if(!sduDtoList.isEmpty()) {
                    kafkaProducerService.sendNetworkElementData(sduDtoList, "SDU", "STATUS_UPDATED");
                }
            }
            if (splitterRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                splitterRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Splitter> splitter = splitterRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (Splitter s : splitter) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_SPLITTER, s.getId());
                }
            }
            if (trenchRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                trenchRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<TrenchLayer> trench = trenchRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for (TrenchLayer t : trench) {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_TRENCH, t.getId());
                }

            }

            if (streetRepository.existsBySurveyAreaIdAndMvnoId(surveyAreaId, mvnoid)) {
                streetRepository.updateStatus(surveyAreaId, status, mvnoid);

                List<Street> street = streetRepository.existsForSurveyArea(surveyAreaId, mvnoid);
                for ( Street t : street)  {
                    layerInventoryMappingRepository.updateStatus(status, LAYER_CODE_STREET, t.getId());
                }

            }

//            cableRepository.updateStatus(surveyAreaId, status);
//            cduRepository.updateStatus(surveyAreaId, status);
//            customerRepository.updateStatus(surveyAreaId, status);
//            ductRepository.updateStatus(surveyAreaId, status);
//            fatRepository.updateStatus(surveyAreaId, status);
//            fdpRepository.updateStatus(surveyAreaId, status);
//            fdtRepository.updateStatus(surveyAreaId, status);
//            handholeRepository.updateStatus(surveyAreaId, status);
//            jointClosureRepository.updateStatus(surveyAreaId, status);
//            manholeRepository.updateStatus(surveyAreaId, status);
//            mduRepository.updateStatus(surveyAreaId, status);
//            odfRepository.updateStatus(surveyAreaId, status);
//            oltRepository.updateStatus(surveyAreaId, status);
//            poleRepository.updateStatus(surveyAreaId, status);
//            popRepository.updateStatus(surveyAreaId, status);
//            sduRepository.updateStatus(surveyAreaId, status);
//            splitterRepository.updateStatus(surveyAreaId, status);
//            trenchRepository.updateStatus(surveyAreaId, status);


//            return result;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSurveyDataForDigitalization(Integer surveyAreaId, Integer mvnoId) {
        try {
            // Fetch the JSONB response from the database
            String jsonResponse = commonRepository.getSurveyDataForDigitalization(surveyAreaId, mvnoId);

            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            return CommonUtil.buildResponseMapForDigitalization(rootNode, objectMapper);  //  For all layers
        } catch (Exception ex) {
            logger.error("Error getting data : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get data : " + ex.getMessage(), ex);
        }
    }

//    public Map<String, Object> previewImageByName(String fileName) {
//        try {
//            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
//            if (!optionalLayerImageMapping.isPresent()) {
//                throw new RuntimeException("No images found for fileName=" + fileName);
//            }
//
//            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());
//
//            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
//            if (contentType == null) {
//                contentType = "application/octet-stream";
//            }
//
//            Map<String, Object> map = new HashMap<>();
//            map.put("resources", resource); // resources removed and same contentTypes also
//            map.put("contentTypes", contentType);
//            return map;
//        } catch (Throwable e) {
//            throw new RuntimeException(e.getMessage());
//        }
//    }
}