package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.FatDTO;
import com.keyanna.gis.core.dto.response.FatWithImageDTO;
import com.keyanna.gis.core.model.Fat;
import org.locationtech.jts.io.ParseException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface FatService {

    Fat updateFat(UUID publicId, FatDTO updatedFatRequestDTO);
    List<Fat> getAllFat(Integer mvnoId);
    Fat create(FatDTO dto) throws ParseException;
    FatWithImageDTO findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    Fat createWithImage(FatDTO dto, List<MultipartFile> imageFiles) throws ParseException;
    Map<String,Object> previewUploadedImage(String directory, String fileName);
    Map<String,Object> previewImageByName(String fileName);
}
