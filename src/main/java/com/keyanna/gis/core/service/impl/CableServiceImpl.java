package com.keyanna.gis.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.dto.request.CableRequestDTO;
import com.keyanna.gis.core.dto.response.CableResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.CableSpecification;
import com.keyanna.gis.core.model.LookupCable;
import com.keyanna.gis.core.repository.CableRepository;
import com.keyanna.gis.core.repository.CableSpecificationRepository;
import com.keyanna.gis.core.repository.LookupCableRepository;
import com.keyanna.gis.core.service.CableCoreService;
import com.keyanna.gis.core.service.CableService;
import com.keyanna.gis.core.service.NetworkElementConnectionService;
import com.keyanna.gis.core.utility.CommonUtil;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class CableServiceImpl implements CableService {

    private static final Logger logger = LoggerFactory.getLogger(CableServiceImpl.class);

    private final CableRepository cableRepository;
    private final LookupCableRepository lookupCableTypeRepository;
    private final CableSpecificationRepository cableSpecificationRepository;
    private final ObjectMapper objectMapper;
    private final SpatialValidator spatialValidator;
    private final CableCoreService cableCoreService;
    private final NetworkElementConnectionService networkElementConnectionService;

    public CableServiceImpl(CableRepository cableRepository, LookupCableRepository lookupCableTypeRepository, CableSpecificationRepository cableSpecificationRepository, ObjectMapper objectMapper, SpatialValidator spatialValidator, CableCoreService cableCoreService, NetworkElementConnectionService networkElementConnectionService) {
        this.cableRepository = cableRepository;
        this.lookupCableTypeRepository = lookupCableTypeRepository;
        this.cableSpecificationRepository = cableSpecificationRepository;
        this.objectMapper = objectMapper;
        this.spatialValidator = spatialValidator;
        this.cableCoreService = cableCoreService;
        this.networkElementConnectionService = networkElementConnectionService;
    }

    @Transactional
    public void createCable(CableRequestDTO cableRequestDTO) {
        try {
            Cable cable = new Cable();
            cable.setPublicId(UUID.randomUUID()); // Generate UUID here
            cable.setName(cableRequestDTO.getName());

            LookupCable lookupCableType = lookupCableTypeRepository.findById(cableRequestDTO.getCableTypeId())
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Cable Type not found"));
            cable.setLookupCableType(lookupCableType);

            cable.setMountingType(cableRequestDTO.getMountingType());
            cable.setStatus(cableRequestDTO.getStatus());

            CableSpecification cableSpec = cableSpecificationRepository.findById(cableRequestDTO.getSpecificationId())
                    .orElseThrow(() -> new EntityNotFoundException("Cable Specification not found"));
            cable.setCableSpecification(cableSpec);

            cable.setMeasuredLengthM(cableRequestDTO.getMeasuredLengthM());

            cable.setInstallationDate(cableRequestDTO.getInstallationDate());

            cable.setParentNeId(cableRequestDTO.getParentNeId());
            cable.setParentNeType(cableRequestDTO.getParentNeType());
            cable.setRemarks(cableRequestDTO.getRemarks());
            cable.setSurveyAreaId(cableRequestDTO.getSurveyAreaId());


            if (cableRequestDTO.getGeom() != null) {
                cable.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(cableRequestDTO.getGeom()));
            }

            if (!spatialValidator.isValidGeometry(cable.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            cable.setCreatedBy(cableRequestDTO.getUserId());
            cable.setCreatedOn(Instant.now());    //  created_on

            cable.setMvnoId(cableRequestDTO.getMvnoId());

            Cable savedCable = cableRepository.save(cable);    //  Save to DB

            cableCoreService.createCableCoreEntry(savedCable.getId());

            /**
             * Create Network Element Connection Entry (Fat Splitter To Cable)
             */
            networkElementConnectionService.connectFatSplitterToCable(cableRequestDTO.getSplitterId(), savedCable.getId(),
                    cableRequestDTO.getConnectionType(), cableRequestDTO.getSplitterPortId(),
                    cableRequestDTO.getSurveyAreaId(), cableRequestDTO.getMvnoId());

            /**
             * Create Network Element Connection Entry (Cable To Sdu)
             */
            networkElementConnectionService.connectCableToSdu(savedCable.getId(),
                    cableRequestDTO.getConnectionType(), cableRequestDTO.getSduId(),
                    cableRequestDTO.getSurveyAreaId(), cableRequestDTO.getMvnoId());

        } catch (Exception ex) {
            logger.error("Error creating cable {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create cable: " + ex.getMessage(), ex);
        }
    }

    public void updateCable(UUID publicId, CableRequestDTO updatedCableRequestDTO) {
        Cable cable = cableRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("Cable with ID " + publicId + " not found."));

        try {
            cable.setName(updatedCableRequestDTO.getName());

            LookupCable lookupCableType = lookupCableTypeRepository.findById(updatedCableRequestDTO.getCableTypeId())
                    .orElseThrow(() -> new EntityNotFoundException("Lookup Cable Type not found"));
            cable.setLookupCableType(lookupCableType);

            cable.setMountingType(updatedCableRequestDTO.getMountingType());
            cable.setStatus(updatedCableRequestDTO.getStatus());

            CableSpecification cableSpec = cableSpecificationRepository.findById(updatedCableRequestDTO.getSpecificationId())
                    .orElseThrow(() -> new EntityNotFoundException("Cable Specification not found"));
            cable.setCableSpecification(cableSpec);

            cable.setMeasuredLengthM(updatedCableRequestDTO.getMeasuredLengthM());

            cable.setInstallationDate(updatedCableRequestDTO.getInstallationDate());

            cable.setParentNeId(updatedCableRequestDTO.getParentNeId());
            cable.setParentNeType(updatedCableRequestDTO.getParentNeType());
            cable.setRemarks(updatedCableRequestDTO.getRemarks());
            cable.setSurveyAreaId(updatedCableRequestDTO.getSurveyAreaId());

            if (updatedCableRequestDTO.getGeom() != null) {
                cable.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(updatedCableRequestDTO.getGeom()));
            }

            cable.setModifiedBy(updatedCableRequestDTO.getModifiedBy());
            cable.setModifiedOn(Instant.now());    //  modified_on

            cable.setMvnoId(updatedCableRequestDTO.getMvnoId());

            cableRepository.save(cable); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating cable {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update cable");
        }
    }

    public List<CableResponseDTO> getAllCables(boolean withGeometry, String userTimeZone, Integer mvnoId) {
        try {
            List<Object[]> cableList = new ArrayList<>();
            if (!withGeometry) {
                cableList = cableRepository.getAllCables(mvnoId);
            } else {
                cableList = cableRepository.getAllCablesWithGeom(mvnoId);
            }

            if (cableList == null) {
                throw new IllegalArgumentException("No Cable found");
            } else if (cableList.isEmpty()) {
                throw new EntityNotFoundException("No cable found for the provided criteria.");
            }
            List<CableResponseDTO> responseDtoList = new ArrayList<>();

            for (Object[] cableObj : cableList) {
                CableResponseDTO responseDto = new CableResponseDTO();
                responseDto.setName((String) cableObj[0]);    //  name
                responseDto.setCableType((String) cableObj[1]); //  cable_type
                responseDto.setMountingType((String) cableObj[2]);  //  mounting_type
                responseDto.setStatus((String) cableObj[3]);  //  status
                responseDto.setSpecificationId((Integer) cableObj[4]);  //  specification_id
                responseDto.setMeasuredLengthM((BigDecimal) cableObj[5]);  //  measured_length_m
                responseDto.setGisLengthM((BigDecimal) cableObj[6]);  //  gis_length_m

                responseDto.setInstallationDate(CommonUtil.convertToUserTimeZone0((Instant) cableObj[7], userTimeZone));    //  ZonedDateTime

                responseDto.setTrenchId((String) cableObj[8]);  //  trench_id
                responseDto.setDuctId((String) cableObj[9]);  //  duct_id
                responseDto.setRemarks((String) cableObj[10]);  //  remarks
                responseDto.setUserId((Long) cableObj[11]);  //  created_by

                responseDto.setCreatedOn(CommonUtil.convertToUserTimeZone0((Instant) cableObj[12], userTimeZone));    //  created_on

                // Assuming geom is stored as JSON string or a compatible object
                if (withGeometry) {
                    responseDto.setGeom(GeoJsonConverterUtil.convertToPointJsonCable((String) cableObj[13], objectMapper));  //  geom
                }
                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting cables {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get cables: ");
        }
    }

    @Transactional
    public void deleteCableByPublicId(UUID publicId, Integer mvnoId) {
        if (!cableRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Cable with public Id not found : " + publicId);
        }

        try {
            cableRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting cable with publicId : " + publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete cable: " + ex.getMessage(), ex);
        }
    }

    public Cable getCableByPublicId(UUID publicId, Integer mvnoId) {
        if (!cableRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Cable with public Id not found : " + publicId);
        }

        try {
            Optional<Cable> entity = cableRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting cable data method getCableByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get cable data method getCableByPublicId: " + ex.getMessage(), ex);
        }
    }

//  TODO remove if not in use
//    public List<CableResponseDTO> getAllCables0() {
//        try {
//            List<Cable> cableList = cableRepository.findAll();
//            if (cableList == null) {
//                throw new IllegalArgumentException("Cable list is null.");
//            } else if (cableList.isEmpty()) {
//                throw new EntityNotFoundException("No cable found for the provided criteria.");
//            }
//            List<CableResponseDTO> responseDtoList = new ArrayList<CableResponseDTO>();
//
//            for (Cable cable : cableList) {
//                CableResponseDTO responseDto = new CableResponseDTO();
//                responseDto.setName(cable.getName());
//                responseDto.setCableType(cable.getCableType());
//                responseDto.setMountingType(cable.getMountingType());
//                responseDto.setStatus(cable.getStatus());
//
//                if (cable.getCableSpecification() != null) {
//                    responseDto.setSpecificationId(cable.getCableSpecification().getId());
//                }
//
//                responseDto.setMeasuredLengthM(cable.getMeasuredLengthM());
//                responseDto.setInstallationDate(cable.getInstallationDate());
//                responseDto.setTrenchId(cable.getTrenchId());
//                responseDto.setDuctId(cable.getDuctId());
//                responseDto.setRemarks(cable.getRemarks());
//
//                // Assuming geom is stored as JSON string or a compatible object
//                responseDto.setGeom(GeoJsonConverterUtil.convertToLineStringJson(cable.getGeom()));
//
//                responseDto.setUserId(cable.getUserId());
//                    responseDto.setCreatedOn(cable.getCreatedOn());
//
//                responseDtoList.add(responseDto);
//            }
//            return responseDtoList;
//        } catch (Exception ex) {
//            logger.error("Error while getting cables {}: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to get cables: ");
//        }
//    }

}