package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.SplitterSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.SplitterSpecificationResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.SplitterSpecification;
import com.keyanna.gis.core.repository.SplitterRepository;
import com.keyanna.gis.core.repository.SplitterSpecificationRepository;
import com.keyanna.gis.core.service.SplitterSpecificationService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Service
public class SplitterSpecificationServiceImpl implements SplitterSpecificationService {

    private static final Logger logger = LoggerFactory.getLogger(SplitterSpecificationServiceImpl.class);
    private final SplitterSpecificationRepository splitterSpecificationRepository;
    private final SplitterRepository splitterRepository;

    public SplitterSpecificationServiceImpl(SplitterSpecificationRepository splitterSpecificationRepository, SplitterRepository splitterRepository) {
        this.splitterSpecificationRepository = splitterSpecificationRepository;
        this.splitterRepository = splitterRepository;
    }

    @Transactional
    public void createSplitterSpecification(SplitterSpecificationRequestDTO splitterSpecificationRequestDTO) {
        try {
            SplitterSpecification splitterSpecification = new SplitterSpecification();
            splitterSpecification.setName(splitterSpecificationRequestDTO.getName());
            splitterSpecification.setPortRatio(splitterSpecificationRequestDTO.getPortRatio());
            splitterSpecification.setDescription(splitterSpecificationRequestDTO.getDescription());
            splitterSpecification.setIsActive(splitterSpecificationRequestDTO.getIsActive());

            splitterSpecificationRepository.save(splitterSpecification);    //  Save to DB
        } catch (DataIntegrityViolationException ex) {
            logger.error("Splitter specification with the same name already exists {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Splitter specification with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating splitter Specification {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create splitter Specification");
        }
    }

    public SplitterSpecification updateSplitterSpecification(Integer id, SplitterSpecificationRequestDTO updatedSplitterSpecificationRequestDTO) {
        SplitterSpecification splitterSpec = splitterSpecificationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Splitter Specification with ID " + id + " not found."));

        try {
            splitterSpec.setName(updatedSplitterSpecificationRequestDTO.getName());
            splitterSpec.setPortRatio(updatedSplitterSpecificationRequestDTO.getPortRatio());
            splitterSpec.setDescription(updatedSplitterSpecificationRequestDTO.getDescription());
            splitterSpec.setIsActive(updatedSplitterSpecificationRequestDTO.getIsActive());

            splitterSpec.setIsActive(updatedSplitterSpecificationRequestDTO.getIsActive());
            splitterSpec.setModifiedOn(Instant.now());    //  modified_on

            return splitterSpecificationRepository.save(splitterSpec);    //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating splitter specification {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update splitter Specification");
        }
    }

    public List<SplitterSpecificationResponseDTO> getAllSplitterSpecifications() {
        try {
            List<SplitterSpecification> splitterSpecificationList = splitterSpecificationRepository.findByIsActiveTrue();

            if (splitterSpecificationList == null) {
                throw new IllegalArgumentException("Splitter Specification list is null.");
            } else if (splitterSpecificationList.isEmpty()) {
                throw new EntityNotFoundException("No splitter Specification found for the provided criteria.");
            }

            List<SplitterSpecificationResponseDTO> responseDtoList = new ArrayList<>();

            for (SplitterSpecification splitterSpecification : splitterSpecificationList) {
                SplitterSpecificationResponseDTO responseDto = new SplitterSpecificationResponseDTO();
                responseDto.setId(splitterSpecification.getId());
                responseDto.setName(splitterSpecification.getName());

                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting splitter specifications {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get splitter specifications: ");
        }
    }

    @Transactional
    public void deleteSplitterSpecificationById(Integer id) {
        splitterSpecificationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("SplitterSpecification with ID " + id + " not found."));

        if (splitterRepository.existsBySplitterSpecification_Id(id)) {
            throw new IllegalStateException("Cannot delete. SplitterSpecification is in use.");
        }

        try {
            splitterSpecificationRepository.deleteById(id);
        } catch (Exception ex) {
            logger.error("Error deleting splitter specification with id {}: {}", id, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete splitter: " + ex.getMessage(), ex);
        }
    }
}
