package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.CableSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.CableSpecificationResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.CableSpecification;
import com.keyanna.gis.core.repository.CableRepository;
import com.keyanna.gis.core.repository.CableSpecificationRepository;
import com.keyanna.gis.core.service.CableSpecificationService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CableSpecificationServiceImpl implements CableSpecificationService {

    private static final Logger logger = LoggerFactory.getLogger(CableSpecificationServiceImpl.class);
    private final CableSpecificationRepository cableSpecificationRepository;
    private final CableRepository cableRepository;

    public CableSpecificationServiceImpl(CableSpecificationRepository cableSpecificationRepository, CableRepository cableRepository) {
        this.cableSpecificationRepository = cableSpecificationRepository;
        this.cableRepository = cableRepository;
    }

    @Transactional
    public void createCableSpecification(CableSpecificationRequestDTO cableSpecificationRequestDTO) {
        try {
            CableSpecification cableSpecification = new CableSpecification();
            cableSpecification.setName(cableSpecificationRequestDTO.getName());
            cableSpecification.setNumberOfTubes(cableSpecificationRequestDTO.getNumberOfTubes());
            cableSpecification.setNumberOfCores(cableSpecificationRequestDTO.getNumberOfCores());
            cableSpecification.setDescription(cableSpecificationRequestDTO.getDescription());
            cableSpecification.setIsActive(cableSpecificationRequestDTO.getIsActive());

            cableSpecificationRepository.save(cableSpecification);    //  Save to DB
        } catch (DataIntegrityViolationException ex) {
            logger.error("Cable specification with the same name already exists {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Cable specification with the same name already exists.");
        } catch (Exception ex) {
            logger.error("Error creating cable Specification {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create cable Specification");
        }
    }

    public CableSpecification updateCableSpecification(Integer id, CableSpecificationRequestDTO updatedCableSpecificationRequestDTO) {
            CableSpecification cableSpec = cableSpecificationRepository.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Cable Specification with ID " + id + " not found."));

        try {
            cableSpec.setName(updatedCableSpecificationRequestDTO.getName());
            cableSpec.setNumberOfTubes(updatedCableSpecificationRequestDTO.getNumberOfTubes());
            cableSpec.setNumberOfCores(updatedCableSpecificationRequestDTO.getNumberOfCores());
            cableSpec.setDescription(updatedCableSpecificationRequestDTO.getDescription());
            cableSpec.setIsActive(updatedCableSpecificationRequestDTO.getIsActive());

            return cableSpecificationRepository.save(cableSpec);    //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating cable specification {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update cable Specification");
        }
    }

    public List<CableSpecificationResponseDTO> getAllCableSpecification() {
        try {
            List<CableSpecification> cableSpecificationList = cableSpecificationRepository.findByIsActiveTrue();

            if (cableSpecificationList == null) {
                throw new IllegalArgumentException("No Cable Specification found");
            } else if (cableSpecificationList.isEmpty()) {
                throw new EntityNotFoundException("No cable Specification found for the provided criteria.");
            }

            List<CableSpecificationResponseDTO> responseDtoList = new ArrayList<>();

            for (CableSpecification cableSpecification : cableSpecificationList) {
                CableSpecificationResponseDTO responseDto = new CableSpecificationResponseDTO();
                responseDto.setId(cableSpecification.getId());
                responseDto.setName(cableSpecification.getName());

                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting cable specifications {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get cable specifications: ");
        }
    }

    @Transactional
    public void deleteCableSpecificationById(Integer id) {
        cableSpecificationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("CableSpecification with ID " + id + " not found."));

        if (cableRepository.existsByCableSpecification_Id(id)) {
            throw new IllegalStateException("Cannot delete. CableSpecification is in use.");
        }

        try {
            cableSpecificationRepository.deleteById(id);
        } catch (Exception ex) {
            logger.error("Error deleting cable specification with id {}: {}", id, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete cable: " + ex.getMessage(), ex);
        }
    }

}
