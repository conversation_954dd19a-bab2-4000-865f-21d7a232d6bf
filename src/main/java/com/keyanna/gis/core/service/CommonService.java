package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.CommonRequestDTO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface CommonService {

    //  TODO DO not delete below method code, Currently not in use
//    Map<String, Object> getGeometriesWithinPolygon(PolygonRequestDTO polygonRequestDTO);

    Map<String, Object> getDataInSurveyArea(Integer surveyAreaId, Integer mvnoId);

    ExcelExportResult getDataInSurveyAreaBom(Integer surveyAreaId);

    record ExcelExportResult(byte[] content, String fileName) {}

    Map<String, Object> findParentNearChildGeom(CommonRequestDTO request);

    Map<String, Object> findNearByGeom(CommonRequestDTO request);

    Object getNetworkElementDataById(UUID id, String layerName);

    List<Map<String, Object>> getStaffDetails(Integer mvnoId);

    Map<String, Object> previewUploadedImage(String directory, String fileName);

    public void updateLatestStatus(Integer surveyAreaId, String status, Integer mvnoId);

    Map<String, Object> getSurveyDataForDigitalization(Integer surveyAreaId, Integer mvnoId);

}
