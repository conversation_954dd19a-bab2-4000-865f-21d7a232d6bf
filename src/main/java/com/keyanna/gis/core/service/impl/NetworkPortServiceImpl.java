package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.LayerInventoryMappingRequestDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.NetworkPort;
import com.keyanna.gis.core.model.OltTray;
import com.keyanna.gis.core.repository.NetworkPortRepository;
import com.keyanna.gis.core.repository.OltTrayRepository;
import com.keyanna.gis.core.service.NetworkPortService;
import jakarta.persistence.Column;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class NetworkPortServiceImpl implements NetworkPortService {

    private static final Logger logger = LoggerFactory.getLogger(NetworkPortServiceImpl.class);
    private final NetworkPortRepository networkPortRepository;
    private final OltTrayRepository oltTrayRepository;

    public NetworkPortServiceImpl(NetworkPortRepository networkPortRepository, OltTrayRepository oltTrayRepository) {
        this.networkPortRepository = networkPortRepository;
        this.oltTrayRepository = oltTrayRepository;
    }

    @Override
    public void createOltTrayNetworkPortEntry(LayerInventoryMappingRequestDTO trayPortCapacityConfig, Integer oltId) {
        try {
            // Step 1: Parse port count per tray
            int portCount;
            try {
                portCount = trayPortCapacityConfig.getParamValue() != null
                        ? Integer.parseInt(trayPortCapacityConfig.getParamValue()) : 0;
            } catch (NumberFormatException e) {
                portCount = 0;
            }

            // Step 2: Fetch all OLT trays by oltId
            List<OltTray> oltTrays = oltTrayRepository.findAllByOltId(oltId);

            if (oltTrays == null || oltTrays.isEmpty()) {
                logger.warn("No OLT trays found for oltId: " + oltId);
                return;
            }

            List<NetworkPort> networkPorts = new ArrayList<>();

            // Step 3: For each tray, create N network ports
            for (OltTray tray : oltTrays) {
                for (int i = 1; i <= portCount; i++) {
                    NetworkPort port = new NetworkPort();
                    port.setLayerId(tray.getId()); // Layer ID is tray ID
                    port.setLayerType(ApiConstants.NETWORK_LAYER_NAME_OLT_TRAY);
                    port.setPortNumber(i);

                    // First 2 ports are "Input", rest are "Output"
                    if (i <= 2) {
                        port.setPortType(ApiConstants.PORT_INPUT);
                    } else {
                        port.setPortType(ApiConstants.PORT_OUTPUT);
                    }

                    port.setPortStatus(ApiConstants.STATUS_AVAILABLE);
                    port.setDescription(null);
                    port.setCreatedOn(LocalDateTime.now());

                    networkPorts.add(port);
                }
            }

            // Step 4: Save all ports
            networkPortRepository.saveAll(networkPorts);

        } catch (Exception ex) {
            logger.error("Error while creating OLT tray port entries: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create OLT tray port entries: " + ex.getMessage(), ex);
        }
    }

    @Transactional
    @Override
    public void markPortAsUsed(Integer id) {
        NetworkPort port = networkPortRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Port not found with id : " + id));

        port.setPortStatus(ApiConstants.STATUS_USED);
        // JPA will detect the change and flush it on transaction commit
    }
}
