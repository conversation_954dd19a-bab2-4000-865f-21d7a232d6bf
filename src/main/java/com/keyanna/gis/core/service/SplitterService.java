package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.SplitterRequestDTO;
import com.keyanna.gis.core.dto.response.NetworkPortResponseDTO;
import com.keyanna.gis.core.dto.response.SduResponseDTO;
import com.keyanna.gis.core.model.Splitter;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public interface SplitterService {

    Splitter createSplitter(SplitterRequestDTO splitterRequestDTO);

    void deleteSplitterByPublicId(UUID publicId, Integer mvnoId);

    void updateSplitter(UUID publicId, SplitterRequestDTO updatedSplitterRequestDTO);

    Splitter getNeSplitterById(UUID publicId, Integer mvnoId);

//    List<SplitterResponseDTO> getAllSplitters(boolean withGeometry, String userTimeZone);

    List<NetworkPortResponseDTO> getAvailablePortsForSplitter(Integer splitterId, Integer mvnoId);

    List<SduResponseDTO> getNearbySDUsForSplitter(Integer splitterId, Integer mvnoId);

}
