package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.LookupSurveyStatusRequestDTO;
import com.keyanna.gis.core.dto.response.LookupSurveyStatusResponseDTO;

import java.util.List;

public interface LookupSurveyStatusService {

    void createLookupSurveyStatus(LookupSurveyStatusRequestDTO requestDTO);

    void updateLookupSurveyStatus(Integer id, LookupSurveyStatusRequestDTO updatedDTO);

    void deleteLookupSurveyStatusById(Integer id,Integer mvnoId);

    List<LookupSurveyStatusResponseDTO> getAllLookupSurveyStatus(Integer mvnoId);

    LookupSurveyStatusResponseDTO getLookupSurveyStatusById(Integer id, Integer mvnoId);
}
