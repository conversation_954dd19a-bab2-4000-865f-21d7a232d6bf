package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.TrenchDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.TrenchLayer;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.OltRepository;
import com.keyanna.gis.core.repository.TrenchLayerRepository;
import com.keyanna.gis.core.service.TrenchLayerService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.io.ParseException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class TrenchLayerServiceImpl implements TrenchLayerService {

    private final GeometryFactory geometryFactory = new GeometryFactory();
    private final TrenchLayerRepository trenchLayerRepository;
    private final SpatialValidator spatialValidator;

    public TrenchLayerServiceImpl(TrenchLayerRepository trenchLayerRepository, SpatialValidator spatialValidator) {
        this.trenchLayerRepository = trenchLayerRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public TrenchLayer create(TrenchDTO dto) throws ParseException {

        try {

            TrenchLayer trenchLayer = new TrenchLayer();
            trenchLayer.setName(dto.getName());
            trenchLayer.setOwner(dto.getOwner());
            trenchLayer.setContractor(dto.getContractor());
            trenchLayer.setRelated_assets(dto.getRelatedAssets());
            trenchLayer.setRemarks(dto.getRemarks());
            trenchLayer.setPublicId(UUID.randomUUID()); // Generate UUID here
            trenchLayer.setStatus(dto.getStatus());
            trenchLayer.setMvnoId(dto.getMvnoId());
            trenchLayer.setSurveyAreaId(dto.getSurveyAreaId());
            trenchLayer.setCreatedBy(dto.getUserId());
            trenchLayer.setWidth_m(dto.getWidthM());
            trenchLayer.setDepth_m(dto.getDepthM());
            trenchLayer.setMeasuredLengthM(dto.getMeasuredLengthM());

            if (dto.getGeom() != null) {
                trenchLayer.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(dto.getGeom()));
            }

            if (!spatialValidator.isValidGeometry(trenchLayer.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            return trenchLayerRepository.save(trenchLayer);
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return null; // or throw a custom runtime exception
        }
    }

    @Override
    public void updateTrench(UUID publicId, TrenchDTO trenchDTO) {
        try {

            TrenchLayer trenchLayer = trenchLayerRepository.findByPublicId(publicId)
                    .orElseThrow(() -> new RuntimeException("Trench not found"));

            trenchLayer.setName(trenchDTO.getName());
            trenchLayer.setStatus(trenchDTO.getStatus());
            trenchLayer.setWidth_m(trenchDTO.getWidthM());
            trenchLayer.setMeasuredLengthM(trenchDTO.getMeasuredLengthM());
            trenchLayer.setDepth_m(trenchDTO.getDepthM());
            trenchLayer.setOwner(trenchDTO.getOwner());
            trenchLayer.setMvnoId(trenchDTO.getMvnoId());
            trenchLayer.setContractor(trenchDTO.getContractor());
            trenchLayer.setRelated_assets(trenchDTO.getRelatedAssets());
            trenchLayer.setRemarks(trenchDTO.getRemarks());
            trenchLayer.setModifiedBy(trenchDTO.getUserId());
            trenchLayer.setSurveyAreaId(trenchDTO.getSurveyAreaId());

            if (trenchDTO.getGeom() != null) {
                trenchLayer.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(trenchDTO.getGeom()));
            }

            if (!spatialValidator.isValidGeometry(trenchLayer.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            trenchLayerRepository.save(trenchLayer);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }


    public TrenchLayer getNeTrenchById(UUID publicId, Integer mvnoId) {
        if (!trenchLayerRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Trench with public Id not found : " + publicId);
        }

        try {
            Optional<TrenchLayer> entity = trenchLayerRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            // logger.error("Error getting Splitter data method getNeSplitterById {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Trench data method getNeTrenchById: " + ex.getMessage(), ex);
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        if (!trenchLayerRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Trench with public Id not found : " + publicId);
        }

        try {
            trenchLayerRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            //    logger.error("Error deleting trench with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete trench: " + ex.getMessage(), ex);
        }
    }


    @Override
    public List<TrenchLayer> getAllTrench(Integer mvnoId) {
        try {

            return trenchLayerRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

}
