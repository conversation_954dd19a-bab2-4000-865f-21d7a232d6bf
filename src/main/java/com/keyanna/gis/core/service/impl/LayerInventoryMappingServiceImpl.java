package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.LayerInventoryMappingRequestDTO;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.service.LayerInventoryMappingService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class LayerInventoryMappingServiceImpl implements LayerInventoryMappingService {

    private static final Logger logger = LoggerFactory.getLogger(LayerInventoryMappingServiceImpl.class);
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;

    public LayerInventoryMappingServiceImpl(LayerInventoryMappingRepository layerInventoryMappingRepository) {
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
    }

    @Transactional
    public void createMultipleLayerInventoryMappings(List<LayerInventoryMappingRequestDTO> requestDTOList) {
        try {
            List<LayerInventoryMapping> mappings = requestDTOList.stream().map(dto -> {

                LayerInventoryMapping mapping = new LayerInventoryMapping();

                mapping.setLayerId(dto.getLayerId());
                mapping.setLayerCode(dto.getLayerCode());
                mapping.setParentParamId(dto.getParentParamId());
                mapping.setParamId(dto.getParamId());
                mapping.setParamName(dto.getParamName());
                mapping.setParamValue(dto.getParamValue());
                mapping.setIsMandatory(dto.getIsMandatory());
                mapping.setIsAccessory(dto.getIsAccessory());
                mapping.setQuantity(dto.getQuantity() != null ? dto.getQuantity() : 0);
                mapping.setCreatedOn(dto.getCreatedOn());
                mapping.setIsConfiguration(mapping.getIsConfiguration());

                return mapping;
            }).toList();

            layerInventoryMappingRepository.saveAll(mappings);
        } catch (Exception ex) {
            logger.error("Error creating multiple LayerInventoryMappings: {}", ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Transactional
    public void deleteByLayerId(Integer layerId) {
        boolean hasMappings = layerInventoryMappingRepository.existsByLayerId(layerId);
        if (hasMappings) {
            layerInventoryMappingRepository.deleteByLayerId(layerId);
        }
    }

    @Transactional
    public void removeMappingsUsingLayerIdAndLayerName(Integer layerId, String layerName) {
        layerInventoryMappingRepository.deleteByLayerIdAndLayerCode(layerId, layerName);
    }

    @Override
    public void deleteByLayerIdAndLayerName(Integer layerId, String layerName) {
        try
        {
            List<LayerInventoryMapping> dataList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(layerId, layerName);
            layerInventoryMappingRepository.deleteAll(dataList);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
