package com.keyanna.gis.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.dto.request.PopRequestDTO;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Pop;
import com.keyanna.gis.core.repository.PopRepository;
import com.keyanna.gis.core.service.PopService;
import com.keyanna.gis.core.utility.CommonUtil;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class PopServiceImpl implements PopService {

    private static final Logger logger = LoggerFactory.getLogger(PopServiceImpl.class);
    private final PopRepository popRepository;
    private final ObjectMapper objectMapper;
    private final SpatialValidator spatialValidator;

    public PopServiceImpl(PopRepository popRepository, ObjectMapper objectMapper, SpatialValidator spatialValidator) {
        this.popRepository = popRepository;
        this.objectMapper = objectMapper;
        this.spatialValidator = spatialValidator;
    }

    @Transactional
    public void createPop(PopRequestDTO popRequestDTO) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                popRequestDTO.getSurveyAreaId(),
                popRequestDTO.getLongitude(),
                popRequestDTO.getLatitude()
        );

        try {

            Point point = GeoJsonConverterUtil.createPointGeom(popRequestDTO.getLongitude(), popRequestDTO.getLatitude());
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Pop pop = new Pop();
            pop.setPublicId(UUID.randomUUID()); // Generate UUID here
            pop.setName(popRequestDTO.getName());
            pop.setAddress(popRequestDTO.getAddress());
            pop.setCategory(popRequestDTO.getCategory());
            pop.setGeom(point);
            pop.setStatus(popRequestDTO.getStatus());
            pop.setParentNeId(popRequestDTO.getParentNeId());
            pop.setParentNeType(popRequestDTO.getParentNeType());

            pop.setCreatedBy(popRequestDTO.getUserId());
            pop.setCreatedOn(Instant.now());
            pop.setMvnoId(popRequestDTO.getMvnoId());
            pop.setSurveyAreaId(popRequestDTO.getSurveyAreaId());

            popRepository.save(pop);    //  Save to DB
        } catch (Exception ex) {
            logger.error("Error creating pop :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create pop: " + ex.getMessage(), ex);
        }
    }

    public Pop getNePopById(UUID publicId, Integer mvnoId) {
        if (!popRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Pop with public Id not found : " + publicId);
        }

        try {
            Optional<Pop> entity = popRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            logger.error("Error getting cable data method getCableByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Pop data method getNePopById: " + ex.getMessage(), ex);
        }
    }

    public void updatePop(UUID publicId, PopRequestDTO updatedPopRequestDTO) {
        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                updatedPopRequestDTO.getSurveyAreaId(),
                updatedPopRequestDTO.getLongitude(),
                updatedPopRequestDTO.getLatitude()
        );

        Pop pop = popRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("Pop with ID " + publicId + " not found."));

        try {
            pop.setName(updatedPopRequestDTO.getName());
            pop.setAddress(updatedPopRequestDTO.getAddress());
            pop.setCategory(updatedPopRequestDTO.getCategory());

            pop.setGeom(GeoJsonConverterUtil.createPointGeom(updatedPopRequestDTO.getLongitude(), updatedPopRequestDTO.getLatitude()));
            if (!spatialValidator.isValidGeometry(pop.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            pop.setStatus(updatedPopRequestDTO.getStatus());
            pop.setParentNeId(updatedPopRequestDTO.getParentNeId());
            pop.setParentNeType(updatedPopRequestDTO.getParentNeType());

            pop.setModifiedBy(updatedPopRequestDTO.getUserId());
            pop.setModifiedOn(Instant.now());    //  modified_on
            pop.setMvnoId(updatedPopRequestDTO.getMvnoId());
            pop.setSurveyAreaId(updatedPopRequestDTO.getSurveyAreaId());


            popRepository.save(pop); //  Save to DB
        } catch (Exception ex) {
            logger.error("Error updating pop {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update pop");
        }
    }

    public List<PopResponseDTO> getAllPops(boolean withGeometry, String userTimeZone, Integer mvnoId) {
        try {
            List<Object[]> popList = new ArrayList<>();
            if (!withGeometry) {
                popList = popRepository.getAllPops(mvnoId);
            } else {
                popList = popRepository.getAllPopsWithGeom(mvnoId);
            }

            if (popList == null) {
                throw new IllegalArgumentException("Pop list is null.");
            } else if (popList.isEmpty()) {
                throw new EntityNotFoundException("No pop found for the provided criteria.");
            }
            List<PopResponseDTO> responseDtoList = new ArrayList<>();

            for (Object[] popObj : popList) {
                PopResponseDTO responseDto = new PopResponseDTO();
                responseDto.setName((String) popObj[0]);    //  name
                responseDto.setAddress((String) popObj[1]); //  address
                responseDto.setCategory((String) popObj[2]);  //  category
                responseDto.setStatus((String) popObj[3]);  //  status

                responseDto.setUserId((Long) popObj[4]); //  created_by

                responseDto.setCreatedOn(CommonUtil.convertToUserTimeZone0((Instant) popObj[5], userTimeZone));    //  created_on

                // Assuming geom is stored as JSON string or a compatible object
                if (withGeometry) {
                    responseDto.setGeom(GeoJsonConverterUtil.convertToPointJsonPop((String) popObj[6], objectMapper));  //  geom
                }
                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting pops {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get pops: ");
        }
    }

    /**
     * This is old method only for back up
     * if anyone wants to use
     */
//    public List<PopResponseDTO> getAllPops0() {
//        try {
//            List<Pop> popList = popRepository.findAll();
//            if (popList == null) {
//                throw new IllegalArgumentException("Pop list is null.");
//            } else if (popList.isEmpty()) {
//                throw new EntityNotFoundException("No pop found for the provided criteria.");
//            }
//            List<PopResponseDTO> responseDtoList = new ArrayList<PopResponseDTO>();
//
//            for (Pop pop : popList) {
//                PopResponseDTO responseDto = new PopResponseDTO();
//                responseDto.setName(pop.getName());
//                responseDto.setAddress(pop.getAddress());
//                responseDto.setCategory(pop.getCategory());
//
//                // Assuming geom is stored as JSON string or a compatible object
//                responseDto.setGeom(GeoJsonConverterUtil.convertToPointJson(pop.getGeom()));
//                responseDto.setStatus(pop.getStatus());
//
//                responseDto.setUserId(pop.getUserId());
////                responseDto.setCreatedOn(pop.getCreatedOn());
//
//                responseDtoList.add(responseDto);
//            }
//            return responseDtoList;
//        } catch (Exception ex) {
//            logger.error("Error while getting pops {}: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to get pops: ");
//        }
//    }
    @Transactional
    public void deletePopByPublicId(UUID publicId, Integer mvnoId) {
        if (!popRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Pop with public Id not found : " + publicId);
        }

        try {
            popRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            logger.error("Error deleting pop with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete pop: " + ex.getMessage(), ex);
        }
    }
}