package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.OdfDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Odf;
import com.keyanna.gis.core.repository.OdfRepository;
import com.keyanna.gis.core.service.OdfService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_ODF;

@Service
@RequiredArgsConstructor
public class OdfServiceImpl implements OdfService {

    private static final Logger logger = LoggerFactory.getLogger(OdfServiceImpl.class);

    private final OdfRepository odfRepository;
    private final CommonUtilComponent commonUtilComponent;


    public Odf createOdf(OdfDTO dto) {
        logger.info("Creating ODF with data: {}", dto);

        try {
            Point point = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            point.setSRID(4326);

            Odf odf = new Odf();
            odf.setName(dto.getName());
            odf.setStatus(dto.getStatus());
            odf.setCustomId(dto.getCustomId());
            odf.setRackNo(dto.getRackNo());
            odf.setTotalPorts(dto.getTotalPorts());
            odf.setOccupiedPorts(dto.getOccupiedPorts());
            odf.setParentNeId(dto.getParentNeId());
            odf.setParentNeType(dto.getParentNeType());
            odf.setMvnoId(dto.getMvnoId());
            odf.setSurveyAreaId(dto.getSurveyAreaId());
            odf.setGeom(point);
            odf.setPublicId(UUID.randomUUID());
            odf.setCreatedBy(dto.getUserId());
            odf.setCreatedOn(LocalDateTime.now());

            Odf savedOdf = odfRepository.save(odf);

            // Create Layer Accessory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedOdf.getId(), ApiConstants.LAYER_CODE_ODF,dto.getStatus());
            }

            logger.info("ODF created successfully");
            return savedOdf;

        } catch (Exception ex) {
            logger.error("Error creating ODF: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create ODF", ex);
        }
    }


    public boolean existsByPublicId(UUID publicId) {
        return odfRepository.existsByPublicId(publicId);
    }


    @Override
    public List<Odf> getAllOdfs(Integer mvnoId) {
        try {
            // Assuming filtering is needed
            return odfRepository.findByMvnoId(mvnoId);
        } catch (DataAccessException dae) {
            logger.error("Database error while fetching ODFs for mvnoId: {}", mvnoId, dae);
            throw new RuntimeException("Database error occurred while fetching ODFs.");
        } catch (Exception ex) {
            logger.error("Unexpected error while fetching ODFs for mvnoId: {}", mvnoId, ex);
            throw new RuntimeException("Unexpected error occurred while fetching ODFs.");
        }
    }


    @Override
    public Odf getOdfByPublicId(UUID publicId, Integer mvnoId) {
        try {
            return (Odf) odfRepository.findByPublicIdAndMvnoId(publicId, mvnoId)
                    .orElseThrow(() -> new EntityNotFoundException("ODF not found for publicId: " + publicId + " and mvnoId: " + mvnoId));
        } catch (EntityNotFoundException enfe) {
            // Specific exception for missing data
            throw enfe;
        } catch (DataAccessException dae) {
            logger.error("Database error while fetching ODF for publicId: {}, mvnoId: {}", publicId, mvnoId, dae);
            throw new RuntimeException("Database error occurred while fetching ODF.");
        } catch (Exception ex) {
            logger.error("Unexpected error while fetching ODF for publicId: {}, mvnoId: {}", publicId, mvnoId, ex);
            throw new RuntimeException("Unexpected error occurred while fetching ODF.");
        }
    }



    public void updateOdf(UUID publicId, OdfDTO dto) {
        logger.info("Updating ODF with publicId: {}", publicId);

        Odf odf = odfRepository.findByPublicId(publicId)
                .orElseThrow(() -> new EntityNotFoundException("ODF not found"));

        try {
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(odf.getId(), LABEL_ODF);
            odf.setName(dto.getName());
            odf.setStatus(dto.getStatus());
            odf.setCustomId(dto.getCustomId());
            odf.setRackNo(dto.getRackNo());
            odf.setTotalPorts(dto.getTotalPorts());
            odf.setOccupiedPorts(dto.getOccupiedPorts());
            odf.setParentNeId(dto.getParentNeId());
            odf.setParentNeType(dto.getParentNeType());
            odf.setMvnoId(dto.getMvnoId());
            odf.setSurveyAreaId(dto.getSurveyAreaId());

            Point point = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            point.setSRID(4326);
            odf.setGeom(point);

            odf.setModifiedBy(dto.getUserId());
            odf.setModifiedOn(LocalDateTime.now());

            Odf savedOdf = odfRepository.save(odf);

            if (dto.getInventoryList() != null && dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedOdf.getId(), ApiConstants.LAYER_CODE_ODF, dto.getStatus());
            }
            logger.info("ODF updated successfully");



        } catch (Exception ex) {
            logger.error("Error updating ODF: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update ODF", ex);
        }
    }


    @Transactional
    public void deleteByPublicId(UUID publicId,Integer mvnoId) {
        Odf odf = (Odf) odfRepository.findByPublicIdAndMvnoId(publicId,mvnoId)
                .orElseThrow(() -> new EntityNotFoundException("ODF not found"));

        try {
            commonUtilComponent.deleteLayerInventoryMappingData(odf.getId(), LABEL_ODF);
            odfRepository.delete(odf);
            logger.info("ODF deleted successfully");
        } catch (Exception ex) {
            logger.error("Error deleting ODF: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete ODF", ex);
        }
    }
}
