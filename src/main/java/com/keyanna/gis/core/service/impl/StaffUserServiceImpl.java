package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.StaffUserDTO;
import com.keyanna.gis.core.model.StaffRoleRel;
import com.keyanna.gis.core.model.StaffUser;
import com.keyanna.gis.core.repository.StaffRoleRelRepository;
import com.keyanna.gis.core.repository.StaffUserRepository;
import com.keyanna.gis.core.service.StaffUserService;

import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class StaffUserServiceImpl implements StaffUserService {

    private static final Logger log = LoggerFactory.getLogger(StaffUserServiceImpl.class);

    private final StaffUserRepository staffUserRepository;
    private final StaffRoleRelRepository staffRoleRelRepository;

    public StaffUserServiceImpl(StaffUserRepository staffUserRepository,
                                StaffRoleRelRepository staffRoleRelRepository) {
        this.staffUserRepository = staffUserRepository;
        this.staffRoleRelRepository = staffRoleRelRepository;
    }

    @Transactional
    @Override
    public void saveFromKafka(StaffUserDTO dto) {
        if (dto == null) {
            log.error("Received null DTO from Kafka");
            return;
        }

        log.info("Received StaffUserDTO from Kafka: {}", dto);

        StaffUser staffUser = staffUserRepository.findByStaffid(dto.getStaffid())
                .orElseGet(() -> {
                    StaffUser su = new StaffUser();
                    su.setStaffid(dto.getStaffid());
                    return su;
                });

        // Set fields
        staffUser.setUsername(dto.getUsername());
        staffUser.setPassword(dto.getPassword());
        staffUser.setFirstname(dto.getFirstname());
        staffUser.setLastname(dto.getLastname());
        staffUser.setStatus(dto.getSstatus());

        if (dto.getLastLoginTime() != null) {
            try {
                staffUser.setLast_login_time(LocalDateTime.parse(dto.getLastLoginTime()));
            } catch (Exception e) {
                log.warn("Invalid lastLoginTime: {}", dto.getLastLoginTime());
            }
        }

        staffUser.setPartnerid(dto.getPartnerid());
        staffUser.setIsDelete(Boolean.TRUE.equals(dto.getIsDelete()));
        staffUser.setMvnoId(dto.getMvnoId());
        staffUser.setBranchId(dto.getBranchid());
        staffUser.setCreatebyname(dto.getCreatebyname());
        staffUser.setUpdatebyname(dto.getUpdatebyname());
        staffUser.setCountryCode(dto.getCountryCode());
        staffUser.setTotalCollected(dto.getTotalCollected());
        staffUser.setTotalTransferred(dto.getTotalTransferred());
        staffUser.setAvailableAmount(dto.getAvailableAmount());
        staffUser.setLcoId(dto.getLcoid());
        staffUser.setHrmsId(dto.getHrmsId());
        staffUser.setDepartment(dto.getDepartment());
        staffUser.setOldpassword1(dto.getOldpassword1());
        staffUser.setOldpassword2(dto.getOldpassword2());
        staffUser.setOldpassword3(dto.getOldpassword3());
        staffUser.setOtp(dto.getOtp());
        staffUser.setOtpvalidate(dto.getOtpvalidate() != null && dto.getOtpvalidate() ? LocalDateTime.now() : null);
        staffUser.setSysstaff(Boolean.TRUE.equals(dto.getSysstaff()));
        staffUser.setEmail(dto.getEmail());
        staffUser.setPhone(dto.getPhone());
        staffUser.setFailcount(dto.getFailcount());
        staffUser.setTacacsAccessLevelGroup(dto.getAccessLevelGroupName());
        staffUser.setMvnoDeactivationFlag(dto.getMvnoDeactivationFlag());
        staffUser.setUuid(dto.getUuid());
        staffUser.setIsPasswordExpired(Boolean.TRUE.equals(dto.getIsPasswordExpired()));
        staffUser.setPasswordDate(dto.getPasswordDate());
        staffUser.setCreatedbystaffid(dto.getCreatedbystaffid());
        staffUser.setLastmodifiedbystaffid(dto.getLastmodifiedbystaffid());
        staffUser.setCreatedate(dto.getCreatedate());
        staffUser.setLastModifiedDate(dto.getLastmodifieddate());
        staffUser.setProfileImage(dto.getProfileImage());

        // Set role (first one only)
        Long primaryRole = (dto.getRoleid() != null && !dto.getRoleid().isEmpty()) ? dto.getRoleid().get(0) : null;
        staffUser.setRoleid(primaryRole);

        // Save user
        StaffUser saved = staffUserRepository.save(staffUser);
        log.info("StaffUser saved: {} (ID: {})", saved.getUsername(), saved.getStaffid());

        // Update staff-role relationship
        staffRoleRelRepository.deleteByStaffid(saved.getStaffid());
        log.info("Existing StaffRoleRel deleted for staffid: {}", saved.getStaffid());

        if (saved.getStaffid() != null && saved.getRoleid() != null) {
            StaffRoleRel rel = new StaffRoleRel();
            rel.setMvnoId(saved.getMvnoId());
            rel.setStaffid(saved.getStaffid());
            rel.setRoleid(saved.getRoleid());
            staffRoleRelRepository.save(rel);

            log.info("StaffRoleRel saved: staffid={} roleid={}", saved.getStaffid(), saved.getRoleid());
        }
    }
}
