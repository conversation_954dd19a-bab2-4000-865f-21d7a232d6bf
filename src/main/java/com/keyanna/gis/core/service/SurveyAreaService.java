package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.SurveyAreaRequestDTO;
import com.keyanna.gis.core.dto.response.SurveyAreaResponseDTO;
import com.keyanna.gis.core.model.SurveyArea;

import java.util.List;
import java.util.UUID;

public interface SurveyAreaService {

    void createSurveyArea(SurveyAreaRequestDTO surveyAreaRequestDTO);

    void updateSurveyArea(UUID publicId, SurveyAreaRequestDTO updatedSurveyAreaRequestDTO);

    void updateSurveyAreaStatus(UUID publicId, String surveyStatusName, Long userId,Integer mvnoId);

    void deleteSurveyAreaByPublicId(UUID publicId,Integer mvnoId);

    SurveyArea getSurveyAreaByPublicId(UUID publicId,Integer mvnoId);

    List<SurveyAreaResponseDTO> getSurveyAreaByUserId(Integer userId,Integer mvnoId, Boolean isAdmin);

    List<SurveyAreaResponseDTO> getSurveyAreaByUserIdAtLayer(Integer userId,Integer mvnoId);

    List<SurveyArea> getAllSurveys(Integer mvnoId);

//    List<SurveyArea> findByLookupSurveyStatus(String initiated);

    //SurveyArea getSurveyStatusId(Integer surveyStatusId);

}
