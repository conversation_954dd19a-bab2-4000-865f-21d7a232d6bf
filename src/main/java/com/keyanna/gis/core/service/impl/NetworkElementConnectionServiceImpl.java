package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.CableCore;
import com.keyanna.gis.core.model.NetworkElementConnection;
import com.keyanna.gis.core.model.Splitter;
import com.keyanna.gis.core.repository.CableCoreRepository;
import com.keyanna.gis.core.repository.NetworkElementConnectionRepository;
import com.keyanna.gis.core.repository.SplitterRepository;
import com.keyanna.gis.core.service.CableCoreService;
import com.keyanna.gis.core.service.NetworkElementConnectionService;
import com.keyanna.gis.core.service.NetworkPortService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class NetworkElementConnectionServiceImpl implements NetworkElementConnectionService {

    private static final Logger logger = LoggerFactory.getLogger(NetworkElementConnectionServiceImpl.class);

    private final NetworkElementConnectionRepository networkElementConnectionRepository;
    private final SplitterRepository splitterRepository;
    private final CableCoreRepository cableCoreRepository;
    private final NetworkPortService networkPortService;
    private final CableCoreService cableCoreService;

    public NetworkElementConnectionServiceImpl(NetworkElementConnectionRepository networkElementConnectionRepository, SplitterRepository splitterRepository, CableCoreRepository cableCoreRepository, NetworkPortService networkPortService, CableCoreService cableCoreService) {
        this.networkElementConnectionRepository = networkElementConnectionRepository;
        this.splitterRepository = splitterRepository;
        this.cableCoreRepository = cableCoreRepository;
        this.networkPortService = networkPortService;
        this.cableCoreService = cableCoreService;
    }

    /**
     * Create Network Element Connection Entry (Fat Splitter To Cable)
     */
    @Override
    @Transactional
    public void connectFatSplitterToCable(Integer splitterId, Integer cableId, String connectionType, Integer splitterPortId, Integer surveyAreaId, Integer mvnoId) {
        try {
            NetworkElementConnection connection = new NetworkElementConnection();

            connection.setCableId(cableId);

            //  Set CoreNumber
            List<CableCore> availableCores = cableCoreRepository.findByCableIdAndCoreStatus(cableId, ApiConstants.STATUS_AVAILABLE);

            // CoreNumber    //  TODO : TODOR change here for other entries
            if (!availableCores.isEmpty()) {
                CableCore cableCore = availableCores.get(0); // or apply custom selection logic
                connection.setCoreNumber(cableCore.getCoreNumber()); // or cableCore.getId() if that's intended
            } else {
                connection.setCoreNumber(null); // or skip this line if field is nullable by default
            }

            connection.setFromLayerId(splitterId);                          // Splitter ID (e.g., 1)
            connection.setFromLayerType(ApiConstants.LAYER_TYPE_SPLITTER);
            connection.setFromPortInfo("SPLITTER_" + splitterId + "_FAT_1_OUTPUT_PORT");

            connection.setToLayerId(cableId);                      // Cable ID as to-layer as well
            connection.setToLayerType(ApiConstants.LAYER_TYPE_CABLE);
            connection.setToPortInfo("CABLE_" + cableId + "_CORE_1");

            Splitter splitter = splitterRepository.findById(splitterId)
                    .orElseThrow(() -> new EntityNotFoundException("Splitter not found"));

            connection.setClosureId(splitter.getParentNeId());                       // FAT ID
            connection.setClosureType(splitter.getParentNeType());                   // fat

            connection.setConnectionType(connectionType);
            connection.setDescription(null);
            connection.setCreatedOn(LocalDateTime.now());

            connection.setSurveyAreaId(surveyAreaId);
            connection.setMvnoId(mvnoId);

            networkElementConnectionRepository.save(connection);

            networkPortService.markPortAsUsed(splitterPortId);

        } catch (Exception ex) {
            logger.error("Error connecting Fat Splitter To Cable method connectFatSplitterToCable {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to connecting Fat Splitter To Cable : " + ex.getMessage(), ex);
        }
    }

    /**
     * Create Network Element Connection Entry (Cable To Sdu)
     */
    @Override
    @Transactional
    public void connectCableToSdu(Integer cableId, String connectionType, Integer sduId, Integer surveyAreaId, Integer mvnoId) {
        try {
            NetworkElementConnection connection = new NetworkElementConnection();

            connection.setCableId(cableId);

            //  Set CoreNumber
            List<CableCore> availableCores = cableCoreRepository.findByCableIdAndCoreStatus(cableId, ApiConstants.STATUS_AVAILABLE);

            Integer cableCoreId = 0;
            // CoreNumber    //  TODO : TODOR change here for other entries
            if (!availableCores.isEmpty()) {
                CableCore cableCore = availableCores.get(0); // or apply custom selection logic
                connection.setCoreNumber(cableCore.getCoreNumber()); // or cableCore.getId() if that's intended
                cableCoreId = cableCore.getId();
            } else {
                connection.setCoreNumber(null); // or skip this line if field is nullable by default
            }

            connection.setFromLayerId(cableId);                          // Cable ID (e.g., 1)
            connection.setFromLayerType(ApiConstants.LAYER_TYPE_CABLE);
            connection.setFromPortInfo("CABLE_" + cableId + "_CORE_1");

            connection.setToLayerId(sduId);                      // Cable ID as to-layer as well
            connection.setToLayerType(ApiConstants.LAYER_TYPE_SDU);
            connection.setToPortInfo("SDU_" + sduId);

            connection.setClosureId(sduId);                       // SDU ID
            connection.setClosureType(ApiConstants.LAYER_TYPE_SDU);                   // sdu

            connection.setConnectionType(connectionType);
            connection.setDescription(null);
            connection.setCreatedOn(LocalDateTime.now());

            connection.setSurveyAreaId(surveyAreaId);
            connection.setMvnoId(mvnoId);

            networkElementConnectionRepository.save(connection);

            /**
             * Patch : Marking cable core used after all two Entry in table(network_element_connection)
             * TODO : Note : In future we need to give user to select port
             */
            // update CoreNumber status Used    //  TODO : TODOR change here for other entries
            if (!availableCores.isEmpty()) {
//                CableCore cableCore = availableCores.get(0);
//                cableCoreService.markPortAsUsed(cableCore.getId());
                cableCoreService.markPortAsUsed(cableCoreId);
            }

        } catch (Exception ex) {
            logger.error("Error connecting Cable To Sdu method connectCableToSdu {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to connecting Cable To Sdu : " + ex.getMessage(), ex);
        }
    }
}