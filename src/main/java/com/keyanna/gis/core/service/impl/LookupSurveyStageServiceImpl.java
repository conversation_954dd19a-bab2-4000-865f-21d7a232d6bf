package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.LookupSurveyStageRequestDTO;
import com.keyanna.gis.core.dto.response.LookupSurveyStageResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LookupSurveyStage;
import com.keyanna.gis.core.repository.LookupSurveyStageRepository;
import com.keyanna.gis.core.service.LookupSurveyStageService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LookupSurveyStageServiceImpl implements LookupSurveyStageService {

    private static final Logger logger = LoggerFactory.getLogger(LookupSurveyStageServiceImpl.class);
    private final LookupSurveyStageRepository lookupSurveyStatusRepository;

    public LookupSurveyStageServiceImpl(LookupSurveyStageRepository lookupSurveyStatusRepository) {
        this.lookupSurveyStatusRepository = lookupSurveyStatusRepository;
    }

    @Override
    public List<LookupSurveyStageResponseDTO> getAllLookupSurveyStage(Integer mvnoId) {
        try {
            List<LookupSurveyStage> lookupSurveyStatusList = lookupSurveyStatusRepository.findByIsActiveTrueAndMvnoId(mvnoId);
            if (lookupSurveyStatusList == null) {
                throw new IllegalArgumentException("No Lookup Survey Stage found");
            } else if (lookupSurveyStatusList.isEmpty()) {
                throw new EntityNotFoundException("No Lookup Survey Stage found for the provided criteria.");
            }
            List<LookupSurveyStageResponseDTO> responseDtoList = new ArrayList<>();
            for (LookupSurveyStage lookupSurveyStatus : lookupSurveyStatusList) {
                LookupSurveyStageResponseDTO responseDto = new LookupSurveyStageResponseDTO();
                responseDto.setId(lookupSurveyStatus.getId());
                responseDto.setName(lookupSurveyStatus.getName());
                responseDtoList.add(responseDto);
            }
            return responseDtoList;
        } catch (Exception ex) {
            logger.error("Error while getting Survey Stages :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Survey Stages: ");
        }
    }

//    @Transactional
//    @Override
//    public void createLookupSurveyStage(LookupSurveyStageRequestDTO requestDTO) {
//        try {
//            LookupSurveyStage surveyStatus = new LookupSurveyStage();
//            surveyStatus.setName(requestDTO.getName());
//            surveyStatus.setDescription(requestDTO.getDescription());
//            surveyStatus.setIsActive(requestDTO.getIsActive());
//
//            lookupSurveyStatusRepository.save(surveyStatus);
//        } catch (DataIntegrityViolationException ex) {
//            logger.error("Survey Stage with the same name already exists: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Survey Stage with the same name already exists.");
//        } catch (Exception ex) {
//            logger.error("Error creating Survey Stage: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to create Survey Stage.");
//        }
//    }
//
//    @Transactional
//    @Override
//    public LookupSurveyStageResponseDTO getLookupSurveyStageById(Integer id) {
//        LookupSurveyStage surveyStatus = lookupSurveyStatusRepository.findById(id)
//                .orElseThrow(() -> new EntityNotFoundException("Survey Stage with ID " + id + " not found."));
//
//        LookupSurveyStageResponseDTO dto = new LookupSurveyStageResponseDTO();
//        dto.setId(surveyStatus.getId());
//        dto.setName(surveyStatus.getName());
//        return dto;
//    }
//
//    @Transactional
//    @Override
//    public void updateLookupSurveyStage(Integer id, LookupSurveyStageRequestDTO updatedDTO) {
//        LookupSurveyStage existing = lookupSurveyStatusRepository.findById(id)
//                .orElseThrow(() -> new EntityNotFoundException("Survey Stage with ID " + id + " not found."));
//
//        try {
//            existing.setName(updatedDTO.getName());
//            existing.setDescription(updatedDTO.getDescription());
//            existing.setIsActive(updatedDTO.getIsActive());
//
//            lookupSurveyStatusRepository.save(existing);
//        } catch (Exception ex) {
//            logger.error("Error updating Survey Stage: {}", ex.getMessage(), ex);
//            throw new RuntimeException("Failed to update Survey Stage.");
//        }
//    }
//
//    @Transactional
//    @Override
//    public void deleteLookupSurveyStageById(Integer id) {
//        LookupSurveyStage surveyStatus = lookupSurveyStatusRepository.findById(id)
//                .orElseThrow(() -> new EntityNotFoundException("Survey Stage with ID " + id + " not found."));
//
//        try {
//            lookupSurveyStatusRepository.deleteById(id);
//        } catch (Exception ex) {
//            logger.error("Error deleting Survey Stage with id {}: {}", id, ex.getMessage(), ex);
//            throw new RuntimeException("Failed to delete Survey Stage: " + ex.getMessage(), ex);
//        }
//    }
}
