package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.PoleDTO;
import com.keyanna.gis.core.dto.response.PoleWithImageDTO;
import com.keyanna.gis.core.model.Pole;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface PoleService {
    Pole create(PoleDTO dto) throws ParseException;
    void updatePole(UUID publicId, PoleDTO dto);
    List<Pole> getAllPoles(Integer mvnoId);
    PoleWithImageDTO getPoleByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId, Integer mvnoId);
    Pole createWithImage(PoleDTO dto, List<MultipartFile> imageFiles) throws org.locationtech.jts.io.ParseException;
    Map<String,Object> previewUploadedImage(String directory, String fileName);
    Map<String,Object> previewImageByName(String fileName);
}