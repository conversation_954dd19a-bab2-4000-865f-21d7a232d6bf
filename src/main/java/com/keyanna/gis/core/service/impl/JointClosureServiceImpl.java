package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.JointClosureDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.JointClosure;
import com.keyanna.gis.core.repository.JointClosureRepository;
import com.keyanna.gis.core.service.JointClosureService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class JointClosureServiceImpl implements JointClosureService {

    private static final Logger logger = LoggerFactory.getLogger(JointClosureServiceImpl.class);
    private final GeometryFactory geometryFactory = new GeometryFactory();
    private final JointClosureRepository jointClosureRepository;
    private final SpatialValidator spatialValidatorpoint;

    public JointClosureServiceImpl(JointClosureRepository jointClosureRepository, SpatialValidator spatialValidatorpoint) {
        this.jointClosureRepository = jointClosureRepository;
        this.spatialValidatorpoint = spatialValidatorpoint;
    }

    @Override
    public List<JointClosure> getAllJointClosures(Integer mvnoId) {
        return jointClosureRepository.findAllByMvnoId(mvnoId);
    }

    @Override
    public JointClosure create(JointClosureDTO dto) {

        // Validation call Point is Within SurveyArea or not
        spatialValidatorpoint.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidatorpoint.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            JointClosure jc = new JointClosure();
            jc.setName(dto.getName());
            jc.setJoint_type(dto.getJointType());
            jc.setCapacity(dto.getCapacity());
            jc.setMounted_in(dto.getMountedIn());
            jc.setStatus(dto.getStatus());
            jc.setParentNeId(dto.getParentNeId());
            jc.setParentNeType(dto.getParentNeType());
            jc.setGeom(point);
            jc.setPublicId(UUID.randomUUID());
            jc.setMvnoId(dto.getMvnoId());
            jc.setSurveyAreaId(dto.getSurveyAreaId());

            jc.setCreated_by(dto.getUserId());
            jc.setCreatedOn(LocalDateTime.now());

            return jointClosureRepository.save(jc);
        } catch (Exception ex) {
            logger.error("Error creating JointClosure {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to create JointClosure: " + ex.getMessage(), ex);
        }
    }

    public JointClosure getJointClosureByPublicId(UUID publicId, Integer mvnoId) {
        if (!jointClosureRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Jointclosure with public Id not found : " + publicId);
        }

        try {
            Optional<JointClosure> entity = jointClosureRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            //  logger.error("Error getting jointClosure data method getJointClosureByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Jointclosure data method getJointClosureByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        try {
            Optional<JointClosure> optionalJointClosure = jointClosureRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            JointClosure jointClosure = optionalJointClosure.orElseThrow(() ->
                    new EntityNotFoundException("JointClosure with public Id not found : " + publicId)
            );
            jointClosureRepository.delete(jointClosure);
        } catch (Exception ex) {
            //    logger.error("Error deleting jointClosure with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete jointClosure: " + ex.getMessage(), ex);
        }

    }

    @Override
    public void updateJointClosure(UUID publicId, JointClosureDTO dto) {
        try {
            JointClosure jc = jointClosureRepository.findByPublicId(publicId)
                    .orElseThrow(() -> new RuntimeException("Joint Closure not found"));

            jc.setName(dto.getName());
            jc.setJoint_type(dto.getJointType());
            jc.setCapacity(dto.getCapacity());
            jc.setMounted_in(dto.getMountedIn());
            jc.setStatus(dto.getStatus());
            jc.setParentNeId(dto.getParentNeId());
            jc.setParentNeType(dto.getParentNeType());

            Point point = geometryFactory.createPoint(new Coordinate(dto.getLongitude(), dto.getLatitude()));
            point.setSRID(4326);
            jc.setGeom(point);
            jc.setMvnoId(dto.getMvnoId());
            jc.setSurveyAreaId(dto.getSurveyAreaId());

            jc.setModifiedBy(dto.getUserId());
            jc.setModifiedOn(LocalDateTime.now());

            jointClosureRepository.save(jc);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
