package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.StreetDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Street;
import com.keyanna.gis.core.repository.StreetRepository;
import com.keyanna.gis.core.service.StreetService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.io.ParseException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class StreetServiceImpl implements StreetService {

    private final StreetRepository streetRepository;
    private final SpatialValidator spatialValidator;

    public StreetServiceImpl(StreetRepository streetRepository, SpatialValidator spatialValidator) {
        this.streetRepository = streetRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Street create(StreetDTO dto) throws ParseException {
        try {

            Street street = new Street();
            street.setName(dto.getName());
            street.setType(dto.getType());
            street.setCode(dto.getCode());
            street.setLengthM(dto.getLengthM());
            street.setOneWay(dto.getOneWay());
            street.setPublicId(UUID.randomUUID()); // Generate UUID here
            street.setStatus(dto.getStatus());
            street.setMvnoId(dto.getMvnoId());
            street.setSurveyAreaId(dto.getSurveyAreaId());
            street.setCreatedBy(dto.getUserId());
            street.setSurface(dto.getSurface());


            if (dto.getGeom() != null) {
                street.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(dto.getGeom()));
            }

            if (!spatialValidator.isValidGeometry(street.getGeom().toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            return streetRepository.save(street);
        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            return null; // or throw a custom runtime exception
        }
    }

    @Override
    public void updateStreet(UUID publicId, StreetDTO streetDTO) {
        try {

            Street street = streetRepository.findByPublicId(publicId)
                    .orElseThrow(() -> new RuntimeException("Street not found"));

            street.setName(streetDTO.getName());
            street.setStatus(streetDTO.getStatus());
            street.setSurface(streetDTO.getSurface());
            street.setType(streetDTO.getType());
            street.setOneWay(streetDTO.getOneWay());
            street.setLengthM(streetDTO.getLengthM());
            street.setMvnoId(streetDTO.getMvnoId());
            street.setCode(streetDTO.getCode());
            street.setModifiedBy(streetDTO.getUserId());
            street.setSurveyAreaId(streetDTO.getSurveyAreaId());

            if (streetDTO.getGeom() != null) {
                street.setGeom(GeoJsonConverterUtil.convertGeometryToLineString(streetDTO.getGeom()));
            }

        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }


    public Street getStreetById(UUID publicId, Integer mvnoId) {
        if (!streetRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Street with public Id not found : " + publicId);
        }

        try {
            Optional<Street> entity = streetRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

            return entity.orElse(null);

        } catch (Exception ex) {
            // logger.error("Error getting Splitter data method getNeSplitterById {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get Street data method getStreetById: " + ex.getMessage(), ex);
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        if (!streetRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Street with public Id not found : " + publicId);
        }

        try {
            streetRepository.deleteByPublicIdAndMvnoId(publicId, mvnoId);
        } catch (Exception ex) {
            //    logger.error("Error deleting trench with publicId {}: {}", publicId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete street: " + ex.getMessage(), ex);
        }
    }


    @Override
    public List<Street> getAllStreet(Integer mvnoId) {
        try {

            return streetRepository.findByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

}
