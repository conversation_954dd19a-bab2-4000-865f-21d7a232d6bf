package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.SurveyUserMappingRequestDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.SurveyArea;
import com.keyanna.gis.core.model.SurveyUserMapping;
import com.keyanna.gis.core.repository.SurveyAreaRepository;
import com.keyanna.gis.core.repository.SurveyUserMappingRepository;
import com.keyanna.gis.core.service.SurveyUserMappingService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class SurveyUserMappingServiceImpl implements SurveyUserMappingService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final Logger logger = LoggerFactory.getLogger(SurveyUserMappingServiceImpl.class);
    private final SurveyUserMappingRepository surveyUserMappingRepository;
    private final SurveyAreaRepository surveyAreaRepository;

    public SurveyUserMappingServiceImpl(SurveyUserMappingRepository surveyUserMappingRepository,SurveyAreaRepository surveyAreaRepository) {
        this.surveyUserMappingRepository = surveyUserMappingRepository;
        this.surveyAreaRepository=surveyAreaRepository;
    }

//    @Transactional
//    public void createMultipleSurveyUserMappings(List<SurveyUserMappingRequestDTO> requestDTOList) {
//        try {
//            List<SurveyUserMapping> mappings = requestDTOList.stream().map(dto -> {
//
//
//                SurveyUserMapping mapping = new SurveyUserMapping();
//                mapping.setSurveyAreaId(dto.getSurveyAreaId());
//                mapping.setUserId(dto.getUserId());
//
//                return mapping;
//            }).toList();
//
//            List<SurveyUserMapping> insertedData = surveyUserMappingRepository.findAll();
//
//            Set<Integer> assignedSurveyAreaIds = insertedData.stream()
//                    .map(SurveyUserMapping::getSurveyAreaId)
//                    .collect(Collectors.toSet());
//
//            for (SurveyUserMapping mapping : mappings) {
//                if (assignedSurveyAreaIds.contains(mapping.getSurveyAreaId())) {
//                    throw new RuntimeException("SurveyUserMapping already exists.");
//                }
//            }
////            for (SurveyUserMapping mapping : mappings) {
////                for (SurveyUserMapping data : insertedData) {
////                    if (data.getSurveyAreaId().equals(mapping.getSurveyAreaId()) && data.getUserId().equals(mapping.getUserId())) {
////                        throw new RuntimeException("SurveyUserMapping already exists");
////                    }
////                }
////
////            }
//
//            surveyUserMappingRepository.saveAll(mappings);
//        } catch (Exception ex) {
//            logger.error("Error creating multiple SurveyUserMappings: {}", ex.getMessage(), ex);
//            throw new RuntimeException(ex.getMessage());
//        }
//    }
@Transactional
public void createMultipleSurveyUserMappings(List<SurveyUserMappingRequestDTO> requestDTOList) {
    try {
        // Step 1: Prepare mappings from input DTOs
        List<SurveyUserMapping> mappings = requestDTOList.stream().map(dto -> {

            Optional<SurveyArea> surveyArea = surveyAreaRepository.findByPublicId(UUID.fromString(dto.getSurveyAreaId()));
            if(surveyArea.isEmpty())
            {
                throw new EntityNotFoundException("SurveyArea not found for publicId: " + dto.getSurveyAreaId());
            }

            SurveyUserMapping mapping = new SurveyUserMapping();
            mapping.setSurveyAreaId(surveyArea.get().getId());
            mapping.setUserId(dto.getUserId());
            return mapping;
        }).toList();

        // Step 2: Check for duplicates in DB
        List<SurveyUserMapping> existingMappings = surveyUserMappingRepository.findAll();
        Set<Integer> assignedSurveyAreaIds = existingMappings.stream()
                .map(SurveyUserMapping::getSurveyAreaId)
                .collect(Collectors.toSet());

        for (SurveyUserMapping mapping : mappings) {
            if (assignedSurveyAreaIds.contains(mapping.getSurveyAreaId())) {
                throw new RuntimeException("SurveyUserMapping already exists for SurveyArea ID: " + mapping.getSurveyAreaId());
            }
        }

        // Step 3: Save new mappings
        surveyUserMappingRepository.saveAll(mappings);

        // Step 4: Fetch status IDs using JdbcTemplate
        Long initiatedId = jdbcTemplate.queryForObject(
                "SELECT id FROM lookup_survey_status WHERE name = 'Initiated'", Long.class);

        Long assignedId = jdbcTemplate.queryForObject(
                "SELECT id FROM lookup_survey_status WHERE name = 'Assigned'", Long.class);

        // Step 5: Update each SurveyArea from 'Initiated' to 'Assigned'
        for (SurveyUserMapping mapping : mappings) {
            // Set to 'Initiated'
            jdbcTemplate.update(
                    "UPDATE survey_area SET survey_status_id = ? WHERE id = ?",
                    initiatedId, mapping.getSurveyAreaId()
            );

            // Immediately set to 'Assigned'
            jdbcTemplate.update(
                    "UPDATE survey_area SET survey_status_id = ? WHERE id = ?",
                    assignedId, mapping.getSurveyAreaId()
            );
        }

    } catch (Exception ex) {
        logger.error("Error creating multiple SurveyUserMappings: {}", ex.getMessage(), ex);
        throw new RuntimeException(ex.getMessage());
    }
}

}
