package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.LookupFdpTypesDTO;
import com.keyanna.gis.core.model.LookupFdpTypes;

import java.util.List;


public interface LookupFdpTypesService  {

    List<LookupFdpTypesDTO> getallLookupFdpTypes();

    LookupFdpTypes getbyId(Integer id);

    void createLookupFdpType(LookupFdpTypesDTO requestDTO);

    void updateLookupFdpType(Integer id, LookupFdpTypesDTO updatedDTO);

     void deleteLookupFdpTypeById(Integer id);
}
