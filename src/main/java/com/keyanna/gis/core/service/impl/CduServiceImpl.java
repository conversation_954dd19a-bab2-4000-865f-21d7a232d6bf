package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.CduDTO;
import com.keyanna.gis.core.dto.response.CduWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Cdu;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.repository.CduRepository;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.service.CduService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static com.keyanna.gis.core.constants.ApiConstants.LABEL_CDU;

@Service
public class CduServiceImpl implements CduService {

    private final CduRepository cduRepository;
    private static final Logger logger = LoggerFactory.getLogger(CduServiceImpl.class);
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final CommonUtilComponent commonUtilComponent;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final SpatialValidator spatialValidator;

    public CduServiceImpl(CduRepository cduRepository, LayerImageMappingRepository layerImageMappingRepository, ImageServiceImpl imageServiceImpl, CommonUtilComponent commonUtilComponent, LayerInventoryMappingRepository layerInventoryMappingRepository, SpatialValidator spatialValidator) {
        this.cduRepository = cduRepository;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.commonUtilComponent = commonUtilComponent;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.spatialValidator = spatialValidator;

    }

    @Override
    public Cdu create(CduDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Cdu cdu = new Cdu();
            cdu.setName(dto.name);
            cdu.setStatus(dto.status);
            cdu.setAddress(dto.address);
            cdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            cdu.setStatus(dto.status);
            cdu.setCategory(dto.category);
            cdu.setFloors(dto.floors);
            cdu.setTowers(dto.towers);
            cdu.setHome_passes(dto.homePasses);
            cdu.setTenancy(dto.tenancy);
            cdu.setSurveyAreaId(dto.surveyAreaId);

            cdu.setGeom(point);
            cdu.setMvnoId(dto.mvnoId);

            cdu.setOpticalLevel(dto.opticalLevel);
            cdu.setRemarks(dto.remarks);
            cdu.setFatNo(dto.fatNo);
            cdu.setFdtNo(dto.fdtNo);
            cdu.setStreetName(dto.getStreetName());

            cdu.setRiser(dto.getRiser());
            cdu.setOltName(dto.getOltName());

            cdu.setCreatedBy(dto.getUserId());
            cdu.setCreatedOn(LocalDateTime.now());

            Cdu savedCdu = cduRepository.save(cdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedCdu.getId(), ApiConstants.LABEL_CDU,dto.getStatus());
//            }

            return savedCdu;

        } catch (Exception e) {
            // Handle or log the exception as needed
            e.printStackTrace(); // or use a logger like log.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void updateCdu(UUID publicId, CduDTO cduDTO) {
        Cdu cdu = cduRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Cdu not found"));

        spatialValidator.validatePointWithinSurveyArea(
                cduDTO.getSurveyAreaId(),
                cduDTO.getLongitude(),
                cduDTO.getLatitude()
        );

        try {
            // Remove old mappings
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(cdu.getId(), LABEL_CDU);

            cdu.setName(cduDTO.getName());
            cdu.setAddress(cduDTO.getAddress());
            cdu.setStatus(cduDTO.getStatus());

            cdu.setCategory(cduDTO.getCategory());
            cdu.setFloors(cduDTO.getFloors());
            cdu.setTowers(cduDTO.getTowers());
            cdu.setHome_passes(cduDTO.getHomePasses());
            cdu.setTenancy(cduDTO.getTenancy());
            cdu.setMvnoId(cduDTO.getMvnoId());
            cdu.setSurveyAreaId(cduDTO.getSurveyAreaId());
            cdu.setFdtNo(cduDTO.getFdtNo());
            cdu.setFatNo(cduDTO.getFatNo());
            cdu.setRemarks(cduDTO.getRemarks());
            cdu.setOpticalLevel(cduDTO.getOpticalLevel());
            cdu.setStreetName(cduDTO.getStreetName());

            cdu.setRiser(cduDTO.getRiser());
            cdu.setOltName(cduDTO.getOltName());

            Point location = GeoJsonConverterUtil.createPointGeom(cduDTO.getLongitude(), cduDTO.getLatitude());
            location.setSRID(4326);
            cdu.setGeom(location);

            cdu.setModifiedBy(cduDTO.getUserId());
            cdu.setModifiedOn(LocalDateTime.now());

            Cdu savedCdu = cduRepository.save(cdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (cduDTO.getInventoryList() != null && !cduDTO.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(cduDTO.getInventoryList(), savedCdu.getId(), ApiConstants.LAYER_CODE_SDU, cduDTO.getStatus());
//            }

        } catch (Exception ex) {
            logger.error("Error updating cdu : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update cdu");
        }
    }

    @Override
    public List<Cdu> getAllCdu(Integer mvnoId) {
        try {

            return cduRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    public CduWithImageDTO getCduByPublicIdAndMvnoId(UUID publicId, Integer mvnoId) {
        if (!cduRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Cdu with public Id not found : " + publicId);
        }

        try {
            Optional<Cdu> entity = cduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Cdu cdu = entity.get();

            List<LayerImageMapping> mappingData = layerImageMappingRepository.findAllByLayerIdAndLayerCode(cdu.getId(), LABEL_CDU);
            List<LayerInventoryMapping> accessoriesList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(cdu.getId(), LABEL_CDU);
            return new CduWithImageDTO(cdu, mappingData, accessoriesList);
        } catch (Exception ex) {
            logger.error("Error getting cdu data method getCduByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get cdu data method getCduByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Cdu createWithImage(CduDTO dto, List<MultipartFile> imageFiles) throws ParseException {
        try {

            spatialValidator.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Cdu cdu = new Cdu();
            cdu.setName(dto.name);
            cdu.setAddress(dto.address);
            cdu.setPublicId(UUID.randomUUID()); // Generate UUID here
            cdu.setStatus(dto.status);
            cdu.setCategory(dto.category);
            cdu.setFloors(dto.floors);
            cdu.setTowers(dto.towers);
            cdu.setHome_passes(dto.homePasses);
            cdu.setTenancy(dto.tenancy);
            cdu.setSurveyAreaId(dto.surveyAreaId);
            cdu.setGeom(point);
            cdu.setMvnoId(dto.mvnoId);
            cdu.setCreatedBy(dto.userId);
            cdu.setCreatedOn(LocalDateTime.now());
            cdu.setOpticalLevel(dto.opticalLevel);
            cdu.setRemarks(dto.remarks);
            cdu.setRiser(dto.getRiser());
            cdu.setOltName(dto.getOltName());
            cdu.setFatNo(dto.fatNo);
            cdu.setStreetName(dto.getStreetName());
            Cdu savedCdu = cduRepository.save(cdu);

            //Note : This will use in future so do not delete below commented code.
//            // Create Layer Accessory Mapping mappings
//            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
//                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedCdu.getId(), ApiConstants.LAYER_CODE_SDU);
//            }

            //Save data in layer image mapping.
            if (imageFiles != null && !imageFiles.isEmpty()) {
                commonUtilComponent.saveLayerImageMappingData(imageFiles, ApiConstants.LAYER_CODE_SDU, savedCdu.getId());
            }
            return savedCdu;
        } catch (Throwable e) {
            logger.error("Failed to create FdcLayer", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Map<String, Object> previewImageByName(String fileName) {
        try {
            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
            if (!optionalLayerImageMapping.isPresent()) {
                throw new RuntimeException("No images found for fileName=" + fileName);
            }

            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());

            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            Map<String, Object> map = new HashMap<>();
            map.put("resources", resource); // resources removed and same contentTypes also
            map.put("contentTypes", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Cdu> optionalCdu = cduRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Cdu cdu = optionalCdu.orElseThrow(() ->
                new EntityNotFoundException("Cdu with public Id not found")
        );

        Integer cduId = cdu.getId(); // Primary key in ne_cdu

        try {
            // Step 1: Delete mappings from layer_accessory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(cduId, LABEL_CDU);

            // Step 3: Remove images from mapping.
            commonUtilComponent.deleteAllAssociatedImageWithNetworkElement(cduId, LABEL_CDU);

            // Step 3: Delete the cdu itself
            cduRepository.delete(cdu);


        } catch (Exception ex) {
            logger.error("Error deleting cdu, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete cdu and related data: " + ex.getMessage(), ex);
        }
    }
}
