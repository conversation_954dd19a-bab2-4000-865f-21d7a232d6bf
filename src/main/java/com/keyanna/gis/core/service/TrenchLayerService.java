package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.TrenchDTO;
import com.keyanna.gis.core.dto.response.PolygonResponseDTO;
import com.keyanna.gis.core.model.TrenchLayer;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface TrenchLayerService {
    TrenchLayer create(TrenchDTO dto) throws ParseException;
    List<TrenchLayer> getAllTrench(Integer mvnoId);
    public void updateTrench(UUID publicId, TrenchDTO trenchDTO);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    TrenchLayer getNeTrenchById(UUID publicId,Integer mvnoId);
}
