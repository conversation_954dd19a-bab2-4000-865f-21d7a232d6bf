package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.response.FdtDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fdt;
import com.keyanna.gis.core.repository.FdtRepository;
import com.keyanna.gis.core.service.FdtService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class FdtServiceImpl implements FdtService {

    private static final Logger logger = LoggerFactory.getLogger(FdtServiceImpl.class);
    private final FdtRepository fdtRepository;
    private final SpatialValidator spatialValidator;

    public FdtServiceImpl(FdtRepository fdtRepository, SpatialValidator spatialValidator) {
        this.fdtRepository = fdtRepository;
        this.spatialValidator = spatialValidator;
    }

    @Override
    public Fdt create(FdtDTO dto) throws ParseException {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Fdt fdt = new Fdt();
            fdt.setPublicId(UUID.randomUUID()); // Generate UUID here
            fdt.setName(dto.getName());
            fdt.setAddress(dto.getAddress());
            fdt.setStatus(dto.getStatus());
            fdt.setCapacity(dto.getCapacity());
            fdt.setParentNeId(dto.getParentNeId());
            fdt.setParentNeType(dto.getParentNeType());
            fdt.setGeom(point);
            fdt.setMvnoId(dto.getMvnoId());
            fdt.setSurveyAreaId(dto.getSurveyAreaId());
            fdt.setPonPort(dto.getPonPort());
            fdt.setCardSlotNumber(dto.getCardSlotNumber());
            fdt.setCoreNumber(dto.getCoreNumber());

            fdt.setCreatedBy(dto.getUserId());
            fdt.setCreatedOn(LocalDateTime.now());

            return fdtRepository.save(fdt);
        } catch (Exception ex) {
            logger.error("Error creating fdt :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create fdt: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void updateFdt(UUID publicId, FdtDTO fdtDTO) {
        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                fdtDTO.getSurveyAreaId(),
                fdtDTO.getLongitude(),
                fdtDTO.getLatitude()
        );

        Fdt fdt = fdtRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Fdt not found"));

        try {
            fdt.setName(fdtDTO.getName());
            fdt.setAddress(fdtDTO.getAddress());
            fdt.setStatus(fdtDTO.getStatus());
            fdt.setCapacity(fdtDTO.getCapacity());
            fdt.setParentNeId(fdtDTO.getParentNeId());
            fdt.setParentNeType(fdtDTO.getParentNeType());
            fdt.setMvnoId(fdtDTO.getMvnoId());
            fdt.setSurveyAreaId(fdtDTO.getSurveyAreaId());
            fdt.setCardSlotNumber(fdtDTO.getCardSlotNumber());
            fdt.setCoreNumber(fdtDTO.getCoreNumber());
            fdt.setPonPort(fdtDTO.getPonPort());


            Point point = GeoJsonConverterUtil.createPointGeom(fdtDTO.getLongitude(), fdtDTO.getLatitude());
            point.setSRID(4326);
            fdt.setGeom(point);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            fdt.setModifiedBy(fdtDTO.getUserId());
            fdt.setModifiedOn(LocalDateTime.now());

            fdtRepository.save(fdt);
        } catch (Exception ex) {
            logger.error("Error updating fdt :" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to updating fdt: " + ex.getMessage(), ex);
        }
    }

    public Fdt getFdtByPublicId(UUID publicId, Integer mvnoId) {
        try {
            Optional<Fdt> entity = fdtRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            if (!entity.isPresent()) {
                throw new EntityNotFoundException("Fdt with public Id not found : " + publicId);
            }
            return entity.orElse(null);
        } catch (Exception ex) {
            logger.error("Error getting fdt data method getFdtByPublicId {}: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to get fdt data method getFdtByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        try {
            Optional<Fdt> optionalFdt = fdtRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            Fdt fdt = optionalFdt.orElseThrow(() ->
                    new EntityNotFoundException("Fdt with this public id is not found")
            );
            fdtRepository.delete(fdt);
        } catch (Exception ex) {
            logger.error("Error deleting fdt, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete fdt: " + ex.getMessage(), ex);
        }
    }

    @Override
    public List<Fdt> getAllFdts(Integer mvnoId) {
        try {

            return fdtRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

}
