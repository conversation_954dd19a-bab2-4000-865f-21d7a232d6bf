package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.JointClosureDTO;
import com.keyanna.gis.core.model.JointClosure;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface JointClosureService {
    JointClosure create(JointClosureDTO dto) throws ParseException;
    List<JointClosure> getAllJointClosures(Integer mvnoId);
    void updateJointClosure(UUID publicId, JointClosureDTO jointClosureDTO);
    JointClosure getJointClosureByPublicId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
}
