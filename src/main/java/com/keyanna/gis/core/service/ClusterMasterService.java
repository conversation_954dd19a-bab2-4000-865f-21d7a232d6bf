package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.request.ClusterMasterRequestDTO;
import com.keyanna.gis.core.model.ClusterMaster;

import java.util.UUID;

public interface ClusterMasterService {

    void createClusterMaster(ClusterMasterRequestDTO clusterMasterRequestDTO);

    void updateClusterMaster(UUID publicId, ClusterMasterRequestDTO updatedClusterMasterRequestDTO,Integer mvnoId);

    void deleteClusterMasterByPublicId(UUID publicId,Integer mvnoId);

    ClusterMaster getClusterMasterByPublicId(UUID publicId,Integer mvnoId);

}
