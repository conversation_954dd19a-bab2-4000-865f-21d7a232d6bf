package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.SduDTO;
import com.keyanna.gis.core.dto.response.SduWithImageDTO;
import com.keyanna.gis.core.model.Sdu;
import org.locationtech.jts.io.ParseException;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface SduService {
    Sdu create(SduDTO dto) throws ParseException;
    public void updateSdu(UUID publicId, SduDTO sduDTO);
    List<Sdu> getAllSdu(Integer mvnoId);
    SduWithImageDTO getSduByPublicId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    Sdu createWithImage(SduDTO dto, List<MultipartFile> imageFiles) throws ParseException;

    Map<String,Object> previewUploadedImage(String directory, String fileName);
    Map<String,Object> previewImageByName(String fileName);
}



