package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.dto.request.LayerMappingDTO;
import com.keyanna.gis.core.repository.LayerMappingRepository;
import com.keyanna.gis.core.service.LayerMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class LayerService implements LayerMappingService {

    @Autowired
    private LayerMappingRepository layerMappingRepository;

    public List<LayerMappingDTO> getParentLayers(String code) {
        List<Object[]> rawResult = layerMappingRepository.findParentLayerRaw(code);

        return rawResult.stream()
                .map(obj -> new LayerMappingDTO(
                        (Integer) obj[0],       // id
                        (String) obj[1],        // name
                        (String) obj[2]         // displayName
                ))
                .collect(Collectors.toList());
    }


}