package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.LookupSurveyStageResponseDTO;

import java.util.List;

public interface LookupSurveyStageService {

//    void createLookupSurveyStage(LookupSurveyStageRequestDTO requestDTO);
//
//    void updateLookupSurveyStage(Integer id, LookupSurveyStageRequestDTO updatedDTO);
//
//    void deleteLookupSurveyStageById(Integer id);

    List<LookupSurveyStageResponseDTO> getAllLookupSurveyStage(Integer mvnoId);

//    LookupSurveyStageResponseDTO getLookupSurveyStageById(Integer id);
}
