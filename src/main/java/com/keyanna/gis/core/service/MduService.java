package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.MduDTO;
import com.keyanna.gis.core.dto.response.MduWithImageDTO;
import com.keyanna.gis.core.model.Mdu;
import org.locationtech.jts.io.ParseException;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface MduService {
    Mdu create(MduDTO dto) throws ParseException;
    public void updateMdu(UUID publicId, MduDTO mduDTO);
    List<Mdu> getAllMdu(Integer mvnoId);
    MduWithImageDTO getMduByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    Mdu createWithImage(MduDTO dto, List<MultipartFile> imageFiles) throws ParseException;

    Map<String,Object> previewUploadedImage(String directory, String fileName);
    Map<String,Object> previewImageByName(String fileName);
}



