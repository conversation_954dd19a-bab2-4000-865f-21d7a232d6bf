package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.PoleDTO;
import com.keyanna.gis.core.dto.response.PoleWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.Pole;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.repository.LayerInventoryMappingRepository;
import com.keyanna.gis.core.repository.PoleRepository;
import com.keyanna.gis.core.service.PoleService;
import com.keyanna.gis.core.utility.GeoJsonConverterUtil;
import com.keyanna.gis.core.utility.component.CommonUtilComponent;
import com.keyanna.gis.core.utility.component.SpatialValidator;
import jakarta.transaction.Transactional;
import org.locationtech.jts.geom.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class PoleServiceImpl implements PoleService {

    private static final Logger logger = LoggerFactory.getLogger(PoleServiceImpl.class);
    private final PoleRepository poleRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final LayerInventoryMappingRepository layerInventoryMappingRepository;
    private final SpatialValidator spatialValidator;
    private final CommonUtilComponent commonUtilComponent;

    public PoleServiceImpl(PoleRepository poleRepository, LayerImageMappingRepository layerImageMappingRepository, ImageServiceImpl imageServiceImpl, LayerInventoryMappingRepository layerInventoryMappingRepository, SpatialValidator spatialValidator, CommonUtilComponent commonUtilComponent) {
        this.poleRepository = poleRepository;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.layerInventoryMappingRepository = layerInventoryMappingRepository;
        this.spatialValidator = spatialValidator;
        this.commonUtilComponent = commonUtilComponent;
    }

    @Override
    public Pole create(PoleDTO dto) {

        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            Point point = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Pole pole = new Pole();
            pole.setName(dto.getName());
            pole.setStatus(dto.getStatus());
            pole.setHeightM(dto.getHeightM());
//            pole.setMaterial(dto.getMaterial()); // commented as not needed for now
//            pole.setPoleType(dto.getPoleType()); // commented as not needed for now
//            pole.setOwnership(dto.getOwnership()); // commented as not needed for now
            pole.setMvnoId(dto.getMvnoId());
            pole.setGeom(point);
            pole.setPublicId(UUID.randomUUID());

            // New fields
            pole.setRemarks(dto.getRemarks());
            pole.setAdss(dto.getAdss());
            pole.setUpb(dto.getUpb());
            pole.setJHook(dto.getJHook());
            pole.setAnchor(dto.getAnchor());
            pole.setSlack(dto.getSlack());
            pole.setJb(dto.getJb());
            pole.setFat(dto.getFat());
            pole.setGuyGrip(dto.getGuyGrip());
            pole.setPhoto(dto.getPhoto());
            pole.setPhoto2(dto.getPhoto2());
            pole.setPhoto3(dto.getPhoto3());
            pole.setSurveyAreaId(dto.getSurveyAreaId());
            pole.setPoleSizeType(dto.getPoleSizeType());
            pole.setCable(dto.getCable());

            pole.setCreatedBy(dto.getUserId());
            pole.setCreatedOn(LocalDateTime.now());

            Pole savedPole = poleRepository.save(pole);

            // Create Layer Inventory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedPole.getId(), ApiConstants.LABEL_POLE, dto.getStatus());
            }

            logger.info("Pole created successfully");
            return savedPole;
        } catch (Exception ex) {
            logger.error("Error creating pole" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create pole: " + ex.getMessage(), ex);
        }
    }

    public PoleWithImageDTO getPoleByPublicIdAndMvnoId(UUID publicId, Integer mvnoId) {
        if (!poleRepository.existsByPublicIdAndMvnoId(publicId, mvnoId)) {
            throw new EntityNotFoundException("Pole with public Id not found : " + publicId);
        }
        try {
            Optional<Pole> entity = poleRepository.findByPublicIdAndMvnoId(publicId, mvnoId);
            List<LayerImageMapping> layerImageMappingData = layerImageMappingRepository.findAllByLayerIdAndLayerCode(entity.get().getId(), ApiConstants.LABEL_POLE);
            List<LayerInventoryMapping> accessoriesList = layerInventoryMappingRepository.findAllByLayerIdAndLayerCode(entity.get().getId(), ApiConstants.LABEL_POLE);
            return new PoleWithImageDTO(entity.get(), layerImageMappingData, accessoriesList);
        } catch (Exception ex) {
            logger.error("Error getting pole data method getPoleByPublicId" + ex.getMessage(), ex);
            throw new RuntimeException("Failed to get pole data method getPoleByPublicId: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void updatePole(UUID publicId, PoleDTO dto) {
        // Validation call Point is Within SurveyArea or not
        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        Pole pole = poleRepository.findByPublicId(publicId)
                .orElseThrow(() -> new RuntimeException("Pole not found"));

        spatialValidator.validatePointWithinSurveyArea(
                dto.getSurveyAreaId(),
                dto.getLongitude(),
                dto.getLatitude()
        );

        try {
            // Remove old mappings
            commonUtilComponent.removeMappingsUsingLayerIdAndLayerName(pole.getId(), ApiConstants.LAYER_CODE_POLE);

            pole.setName(dto.getName());
            pole.setStatus(dto.getStatus());
            pole.setHeightM(dto.getHeightM());
//            pole.setMaterial(dto.getMaterial()); // commented as not needed for now
//            pole.setPoleType(dto.getPoleType()); // commented as not needed for now
//            pole.setOwnership(dto.getOwnership()); // commented as not needed for now
            pole.setMvnoId(dto.getMvnoId());
            pole.setPoleSizeType(dto.getPoleSizeType());


            Point point = GeoJsonConverterUtil.createPointGeom(dto.getLongitude(), dto.getLatitude());
            point.setSRID(4326);
            pole.setGeom(point);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            // New fields
            pole.setRemarks(dto.getRemarks());
            pole.setAdss(dto.getAdss());
            pole.setUpb(dto.getUpb());
            pole.setJHook(dto.getJHook());
            pole.setAnchor(dto.getAnchor());
            pole.setSlack(dto.getSlack());
            pole.setJb(dto.getJb());
            pole.setFat(dto.getFat());
            pole.setGuyGrip(dto.getGuyGrip());
            pole.setPhoto(dto.getPhoto());
            pole.setPhoto2(dto.getPhoto2());
            pole.setPhoto3(dto.getPhoto3());
            pole.setSurveyAreaId(dto.getSurveyAreaId());
            pole.setCable(dto.getCable());

            pole.setModifiedBy(dto.getUserId());
            pole.setModifiedOn(LocalDateTime.now());

            Pole savedPole = poleRepository.save(pole);

            // Create Layer Inventory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedPole.getId(), ApiConstants.LAYER_CODE_POLE,dto.getStatus());
            }

            logger.info("Pole updated successfully");
        } catch (Exception ex) {
            logger.error("Error updating pole : " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to update pole");
        }
    }

    public List<Pole> getAllPoles(Integer mvnoId) {
        try {
            return poleRepository.findAllByMvnoId(mvnoId);
        } catch (Exception e) {
            // Log the exception or handle it as needed
            e.printStackTrace(); // Replace with logger if available
            return Collections.emptyList(); // Return empty list on failure
        }
    }

    @Transactional
    public void deleteByPublicId(UUID publicId, Integer mvnoId) {
        Optional<Pole> optionalPole = poleRepository.findByPublicIdAndMvnoId(publicId, mvnoId);

        Pole pole = optionalPole.orElseThrow(() ->
                new EntityNotFoundException("Pole with public Id not found")
        );

        Integer poleId = pole.getId(); // Primary key in ne_pole

        try {
            // Step 1: Delete mappings from layer_inventory_mapping
            commonUtilComponent.deleteLayerInventoryMappingData(poleId, ApiConstants.LAYER_CODE_POLE);

            // Step 2: Remove images from mapping.
            commonUtilComponent.deleteAllAssociatedImageWithNetworkElement(poleId, ApiConstants.LAYER_CODE_POLE);

            // Step 3: Delete the pole itself
            poleRepository.delete(pole);
        } catch (Exception ex) {
            logger.error("Error deleting pole, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete pole and related data: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Pole createWithImage(PoleDTO dto, List<MultipartFile> imageFiles) {
        logger.info("Creating new pole with data: {}", dto);
        try {

            spatialValidator.validatePointWithinSurveyArea(
                    dto.getSurveyAreaId(),
                    dto.getLongitude(),
                    dto.getLatitude()
            );

            double lon = dto.getLongitude();
            double lat = dto.getLatitude();

            Point point = GeoJsonConverterUtil.createPointGeom(lon, lat);
            point.setSRID(4326);

            if (!spatialValidator.isValidGeometry(point.toText())) {
                throw new RuntimeException("Invalid geometry data");
            }

            Pole pole = new Pole();
            pole.setName(dto.getName());
            pole.setStatus(dto.getStatus());
            pole.setCreatedBy(dto.getUserId());
            pole.setHeightM(dto.getHeightM());
//            pole.setMaterial(dto.getMaterial()); // commented as not needed for now
//            pole.setPoleType(dto.getPoleType()); // commented as not needed for now
//            pole.setOwnership(dto.getOwnership()); // commented as not needed for now
            pole.setMvnoId(dto.getMvnoId());
            pole.setGeom(point);
            pole.setPublicId(UUID.randomUUID());

            // New fields
            pole.setRemarks(dto.getRemarks());
            pole.setAdss(dto.getAdss());
            pole.setUpb(dto.getUpb());
            pole.setJHook(dto.getJHook());
            pole.setAnchor(dto.getAnchor());
            pole.setSlack(dto.getSlack());
            pole.setJb(dto.getJb());
            pole.setFat(dto.getFat());
            pole.setGuyGrip(dto.getGuyGrip());
            pole.setPhoto(dto.getPhoto());
            pole.setPhoto2(dto.getPhoto2());
            pole.setPhoto3(dto.getPhoto3());
            pole.setSurveyAreaId(dto.getSurveyAreaId());
            pole.setPoleSizeType(dto.getPoleSizeType());
            pole.setCable(dto.getCable());

            Pole savedPole = poleRepository.save(pole);

            // Create Layer Inventory Mapping mappings
            if (dto.getInventoryList() != null && !dto.getInventoryList().isEmpty()) {
                commonUtilComponent.saveLayerInventoryMappingData(dto.getInventoryList(), savedPole.getId(), ApiConstants.LABEL_POLE,dto.getStatus());
            }

            //Save data in layer image mapping.
            if (imageFiles != null && !imageFiles.isEmpty()) {
                commonUtilComponent.saveLayerImageMappingData(imageFiles, ApiConstants.LAYER_CODE_POLE, savedPole.getId());
            }

            logger.info("Pole created successfully");
            return savedPole;
        } catch (Throwable e) {
            logger.error("Error creating pole" + e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> previewUploadedImage(String directory, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Resource resource = imageServiceImpl.previewImage(directory, fileName);
            map.put("resource", resource);

            // Determine content type
            String contentType = Files.probeContentType(Paths.get(fileName));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            map.put("contentType", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Map<String, Object> previewImageByName(String fileName) {
        try {
            Optional<LayerImageMapping> optionalLayerImageMapping = layerImageMappingRepository.findByFileName(fileName);
            if (!optionalLayerImageMapping.isPresent()) {
                throw new RuntimeException("No images found for fileName=" + fileName);
            }

            Resource resource = imageServiceImpl.previewImageByPath(optionalLayerImageMapping.get().getFileName());

            String contentType = Files.probeContentType(Paths.get(optionalLayerImageMapping.get().getFileName()));
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            Map<String, Object> map = new HashMap<>();
            map.put("resources", resource); // resources removed and same contentTypes also
            map.put("contentTypes", contentType);
            return map;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}