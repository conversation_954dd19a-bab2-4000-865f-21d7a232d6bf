package com.keyanna.gis.core.service.impl;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.LayerInventoryMappingRequestDTO;
import com.keyanna.gis.core.model.OltTray;
import com.keyanna.gis.core.repository.OltTrayRepository;
import com.keyanna.gis.core.service.OltTrayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class OltTrayServiceImpl implements OltTrayService {

    private static final Logger logger = LoggerFactory.getLogger(OltTrayServiceImpl.class);
    private final OltTrayRepository oltTrayRepository;

    public OltTrayServiceImpl(OltTrayRepository oltTrayRepository) {
        this.oltTrayRepository = oltTrayRepository;
    }

    //  TODO : TODOR change here for olt tray and olt tray port entries
    @Override
    public void createOltTrayAndPortMappings(LayerInventoryMappingRequestDTO trayCapacityConfig, Integer oltId) {
        try {
            // Convert paramValue (String) to int
            int trayCount;
            try {
                trayCount = trayCapacityConfig.getParamValue() != null ? Integer.parseInt(trayCapacityConfig.getParamValue()) : 0;
            } catch (NumberFormatException e) {
                trayCount = 0; // Default if parsing fails
            }

            List<OltTray> oltTrays = new ArrayList<>();

            for (int i = 1; i <= trayCount; i++) {
                OltTray tray = new OltTray();
                tray.setOltId(oltId);
                tray.setTrayName(String.format("Tray-%02d", i)); // Tray-01, Tray-02, etc.
                tray.setTrayStatus(ApiConstants.STATUS_AVAILABLE);
                tray.setPortCapacity(null);
                tray.setDescription(null);
                tray.setCreatedOn(LocalDateTime.now());

                oltTrays.add(tray);
            }

            // Save all trays
            oltTrayRepository.saveAll(oltTrays);
        } catch (Exception ex) {
            logger.error("Error while creating Olt Tray, " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to create creating Olt Tray data: " + ex.getMessage(), ex);
        }
    }

}
