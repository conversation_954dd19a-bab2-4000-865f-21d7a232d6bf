package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.ManholeDTO;
import com.keyanna.gis.core.model.Manhole;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface ManholeService {
    Manhole create(ManholeDTO dto) throws ParseException;

    List<Manhole> getAllManholes(Integer mvnoId);

    void updateManhole(UUID publicId, ManholeDTO dto);

    Manhole getManholeById(UUID publicId,Integer mvnoId);

    void deleteMandholeByPublicId(UUID publicId,Integer mvnoId);
}