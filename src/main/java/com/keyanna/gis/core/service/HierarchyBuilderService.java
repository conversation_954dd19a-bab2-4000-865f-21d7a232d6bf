package com.keyanna.gis.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class HierarchyBuilderService {

    @Autowired private OltRepository oltRepository;
    @Autowired private OdfRepository odfRepository;
    @Autowired private FdpRepository fdpRepository;
    @Autowired private CableRepository cableRepository;
    @Autowired private FatRepository fatRepository;
    @Autowired private SplitterRepository splitterRepository;

    /**
     * Builds the hierarchy tree for the given OLT.
     */
    public Map<String, Object> buildHierarchy(Olt olt) {
        Map<String, Object> treeMap = convertToMap(olt);
        treeMap.put("type", "OLT");
        return buildRecursively(treeMap);
    }

    /**
     * Recursively builds the children nodes.
     */
    private Map<String, Object> buildRecursively(Map<String, Object> parentMap) {
        int parentId = Integer.parseInt(parentMap.get("id").toString());
        String parentType = parentMap.get("type").toString();

        // Handle ODFs
        odfRepository.findByParentNeIdAndParentNeType(parentId, parentType).forEach(odf -> {
            Map<String, Object> childMap = convertToMap(odf);
            childMap.put("type", "ODF");
            addChild(parentMap, childMap);
            buildRecursively(childMap);
        });

        // Handle FATs
        fatRepository.findByParentNeIdAndParentNeType(parentId, parentType).forEach(fat -> {
            Map<String, Object> childMap = convertToMap(fat);
            childMap.put("type", "FAT");
            addChild(parentMap, childMap);
            buildRecursively(childMap);
        });

        // Handle Splitters
        splitterRepository.findByParentNeIdAndParentNeType(parentId, parentType).forEach(splitter -> {
            Map<String, Object> childMap = convertToMap(splitter);
            childMap.put("type", "Splitter");
            addChild(parentMap, childMap);
            buildRecursively(childMap);
        });



        return parentMap;
    }

    /**
     * Adds a child node to the parent node.
     */
    private void addChild(Map<String, Object> parent, Map<String, Object> child) {
        List<Map<String, Object>> children = (List<Map<String, Object>>) parent.get("children");

        if (children == null) {
            children = new ArrayList<>();
        } else {
            children = new ArrayList<>(children); // Make modifiable copy
        }

        children.add(child);
        parent.put("children", children);
    }

    /**
     * Converts an entity to a modifiable Map.
     */
    public Map<String, Object> convertToMap(Object entity) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.setDateFormat(new StdDateFormat());

        return mapper.convertValue(entity, Map.class);
    }
}

