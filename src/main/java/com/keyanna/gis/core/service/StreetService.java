package com.keyanna.gis.core.service;

import com.keyanna.gis.core.dto.response.StreetDTO;
import com.keyanna.gis.core.model.Street;
import org.locationtech.jts.io.ParseException;

import java.util.List;
import java.util.UUID;

public interface StreetService {
    Street create(StreetDTO dto) throws ParseException;
    List<Street> getAllStreet(Integer mvnoId);
     void updateStreet(UUID publicId, StreetDTO streetDTO);
    void deleteByPublicId(UUID publicId,Integer mvnoId);
    Street getStreetById(UUID publicId,Integer mvnoId);
}
