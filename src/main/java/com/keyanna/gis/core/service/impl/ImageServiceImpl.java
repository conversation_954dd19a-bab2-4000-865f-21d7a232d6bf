package com.keyanna.gis.core.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Set;

@Service
public class ImageServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(ImageServiceImpl.class);

    @Value("${upload.directory}")
    private String baseUploadDirectory;

    @Value("${upload.file.size}")
    private Integer uploadFileSize;


    // Supported image types
    private static final Set<String> ALLOWED_CONTENT_TYPES = Set.of(
            "image/jpeg", "image/png", "image/gif", "image/jpg"
    );

    public String uploadImage(MultipartFile file, String directoryName) throws IOException {
        try {
            // Validate inputs
            validateInputs(file);
            // Create target directory
            Path uploadPath = createDirectory(directoryName);
            // Generate unique filename
            String fileName = generateUniqueFileName(file.getOriginalFilename());
//        fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            // Save file
            Path targetPath = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            // Return relative path
            return directoryName + "/" + fileName;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private void validateInputs(MultipartFile file) {
        try {
            // Check directory name
//        if (directoryName == null || directoryName.trim().isEmpty()) {
//            throw new IllegalArgumentException("Directory name cannot be empty");
//        }
//        if (!directoryName.matches("[a-zA-Z0-9_-]+")) {
//            throw new IllegalArgumentException(
//                    "Directory name can only contain letters, numbers, underscores and hyphens"
//            );
//        }
            // Check file
            if (file == null || file.isEmpty()) {
                throw new IllegalArgumentException("File cannot be empty");
            }
            // Check content type
            String contentType = file.getContentType();
            if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
                throw new IllegalArgumentException(
                        "Invalid file type. Allowed types: " + ALLOWED_CONTENT_TYPES
                );
            }
            // Check file size (max 5MB)
            if (file.getSize() > (uploadFileSize * 1024 * 1024)) {
                throw new IllegalArgumentException("File size exceeds 5MB limit");
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private Path createDirectory(String directoryName) throws IOException {
        try {
            Path path = Paths.get(baseUploadDirectory + directoryName);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                logger.info("Created directory: {}", path);
            }
            return path;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private String generateUniqueFileName(String originalFileName) {
        try {
            String cleanFileName = originalFileName.replaceAll("[^a-zA-Z0-9._-]", "");
            return System.currentTimeMillis() + "_" + cleanFileName;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Resource previewImage(String directoryName, String fileName) throws IOException {
        try {
            validateInputs(directoryName, fileName);
            Path filePath = Paths.get(baseUploadDirectory + directoryName, fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (!resource.exists() || !resource.isReadable()) {
                throw new FileNotFoundException("Image not found: " + filePath);
            }
            return resource;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public Resource previewImageByPath(String fileName) throws IOException {
        try {
//        validateInputs(fileName);
            Path filePath = Paths.get(baseUploadDirectory, fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (!resource.exists() || !resource.isReadable()) {
                throw new FileNotFoundException("Image not found: " + filePath);
            }
            return resource;
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private void validateInputs(String directoryName, String fileName) {
        try {
            // Validate directory name
            if (directoryName == null || directoryName.trim().isEmpty()) {
                throw new IllegalArgumentException("Directory name cannot be empty");
            }
            if (!directoryName.matches("[a-zA-Z0-9_-]+")) {
                throw new IllegalArgumentException(
                        "Invalid directory name: Only alphanumeric, underscore and hyphen allowed"
                );
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}