package com.keyanna.gis.core;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@EnableDiscoveryClient
public class GisCoreApplication {

	public static void main(String[] args) {
		SpringApplication.run(GisCoreApplication.class, args);
		System.out.println(" :::::::::::::::::::::::::: System started successfully :::::::::::::::::::::::::: ");
	}

}
