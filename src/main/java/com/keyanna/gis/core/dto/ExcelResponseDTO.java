package com.keyanna.gis.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
public class ExcelResponseDTO {
    private List<CommonFieldsDTO> pop;
    private List<CommonFieldsDTO> fdp;
    private List<CommonFieldsDTO> fdt;
    private List<CommonFieldsDTO> fat;
    private List<CommonFieldsDTO> cable;
    private List<CommonFieldsDTO> splitter;
    private List<CommonFieldsDTO> customer;
    private List<CommonFieldsDTO> duct;
    private List<CommonFieldsDTO> pole;
    private List<CommonFieldsDTO> trench;
    private List<CommonFieldsDTO> handhole;
    private List<CommonFieldsDTO> jointClosure;
    private List<CommonFieldsDTO> manhole;
    private List<CommonFieldsDTO> olt;
    private List<CommonFieldsDTO> odf;
    private List<CommonFieldsDTO> street;
    private List<CommonFieldsDTO> sdu;
    private List<CommonFieldsDTO> mdu;
    private List<CommonFieldsDTO> cdu;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class CommonFieldsDTO {
        private String name;
        private String address;
        private String category;
        private Integer createdBy;
        private Integer port;
        private Integer capacity;
        private String cableTypeName;
        private String cableTypeDescription;
        private String typeName;
        private String typeDescription;
        private String mountingType;
        private String specificationName;
        private String specificationDescription;
        private Integer numberOfCores;
        private String portRatio;
        private String parentBoxType;
        private String tenancy;
        private String customerType;
        private String poleType;
        private Integer heightM;
        private Integer powerLevels;
        private String holeSize;
        private String accessType;
        private String material;
        private String jointType;
        private String manholeSize;
        private Integer depthCm;
        private String coverType;
        private Integer gisLengthM;
        private Integer lengthM;
        private String networkType;
        private Integer widthM;
        private Integer depthM;
        private List<AccessoriesListDTO> accessoriesList;

        @Data
        public static class AccessoriesListDTO {
            private Integer quantity;
            private String paramValue;

        }
    }
}
