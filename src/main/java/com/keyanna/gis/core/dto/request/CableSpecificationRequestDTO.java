package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CableSpecificationRequestDTO {

    /**
     * TODO : Add validation whenever required
     */

    @JsonProperty("name")
    private String name;

    @JsonProperty("numberOfTubes")
    private Integer numberOfTubes;

    @JsonProperty("numberOfCores")
    private Integer numberOfCores;

    @JsonProperty("description")
    private String description;

    @JsonProperty("isActive")
    private Boolean isActive;
}
