package com.keyanna.gis.core.dto.response;

import com.keyanna.gis.core.model.Cdu;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.LayerImageMapping;
import lombok.Data;

import java.util.List;

@Data
public class CduWithImageDTO {

    // From Cdu
    private Cdu cdu;

    // From LayerImageMapping
    private List<LayerImageMapping> images;

    private List<LayerInventoryMapping> inventoryList;

    public CduWithImageDTO(Cdu cdu,List<LayerImageMapping> images, List<LayerInventoryMapping> inventoryList) {
        this.cdu=cdu;
        this.images = images;
        this.inventoryList = inventoryList;
    }


}
