package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.dto.request.CableRequestDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Data
public class CableResponseDTO {

    @NotBlank(message = "Please enter your name")
    @JsonProperty("name")
    private String name;

    private String cableType;

    @NotBlank(message = "Please enter mounting type")
    @JsonProperty("mountingType")
    private String mountingType;

    private String status;
    private Integer specificationId;
    private BigDecimal measuredLengthM;
    private BigDecimal gisLengthM;
    private ZonedDateTime installationDate;
    private String trenchId;
    private String remarks;
    private String ductId;

    @NotNull(message = "Please provide geometry information")
    @JsonProperty("geom")
    private CableResponseDTO.Geometry geom;

    @NotNull(message = "Please enter your user ID")
    @JsonProperty("userId")
    private Long userId;


    private ZonedDateTime createdOn;

    @Data
    public static class Geometry {
        private String type;
        private List<List<Double>> coordinates;   //  For LineString
    }
}
