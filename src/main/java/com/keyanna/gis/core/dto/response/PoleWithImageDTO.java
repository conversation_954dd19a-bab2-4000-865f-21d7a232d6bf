package com.keyanna.gis.core.dto.response;

import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.Pole;
import lombok.Data;

import java.util.List;

@Data
public class PoleWithImageDTO {

    private Pole pole;
    private List<LayerImageMapping> images;
    private List<LayerInventoryMapping> inventoryList;

    public PoleWithImageDTO(Pole pole, List<LayerImageMapping> images, List<LayerInventoryMapping> inventoryList) {
        this.pole =pole;
        this.images = images;
        this.inventoryList = inventoryList;
    }


}
