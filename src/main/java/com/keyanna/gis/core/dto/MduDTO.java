package com.keyanna.gis.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class MduDTO {

    @NotBlank(message = "Name is required")
    public String name;

    public String address;
    public int homePasses;
    public String status;
    public double latitude;
    public double longitude;
    public int towers;
    public int floors;
    public String category;
    public String tenancy;
    public String remarks;
    public String fatNo;
    public String fdtNo;
    public String opticalLevel;
    public PopResponseDTO.Geometry geom;
    public String streetName;

    @JsonProperty("riser")
    private String riser;

    @JsonProperty("oltName")
    private String oltName;

    @NotNull(message = "Please provide userId")
    public Long userId;

    @NotNull(message = "MVNO ID is required")
    public Integer mvnoId;

    @NotNull(message = "Survey Area ID is required")
    public Integer surveyAreaId;

    // Date fields with proper formatting for Kafka
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;

    //Note : This will use in future so do not delete below commented code.
//    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
//    private List<Inventory> inventoryList;
}
