package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.UUID;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
public class SurveyAreaResponseDTO {

    private UUID publicId;

    private Integer id;

    private String name;

    private Integer statusId;

    private String surveyStatusName;

    private String surveyStageName;
}
