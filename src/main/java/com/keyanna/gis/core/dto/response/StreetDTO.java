package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class StreetDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    public String name;

    @NotNull(message = "type is required")
    @JsonProperty("type")
    public String type;

    @NotNull(message = "code is required")
    @JsonProperty("code")
    public String code;

    @JsonProperty("lengthM")
    public BigDecimal lengthM;

    @NotNull(message = "oneWay is required")
    @JsonProperty("oneWay")
    public Boolean oneWay;

    @JsonProperty("surface")
    public String surface;

    @JsonProperty("status")
    public String status;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", message = "Latitude must be greater than or equal to -90")
    @DecimalMax(value = "90.0", message = "Latitude must be less than or equal to 90")
    @JsonProperty("latitude")
    public double latitude;


    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", message = "Longitude must be greater than or equal to -180")
    @DecimalMax(value = "180.0", message = "Longitude must be less than or equal to 180")
    @JsonProperty("longitude")
    public double longitude;

    @JsonProperty("geom")
    private Geometry geom;

    @NotNull(message = "Mvno Id is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area Id is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @NotNull(message = "User Id is required")
    @JsonProperty("userId")
    private Long userId;


    @Data
    public static class Geometry {
        private String type;
        private List<List<Double>> coordinates;
    }
}
