package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.Inventory;
import com.keyanna.gis.core.model.Fat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class FatDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    public String name;

    @NotNull(message = "Please enter the capacity")
    @JsonProperty("capacity")
    public Integer capacity;


    @JsonProperty("address")
    public String address;

    @JsonProperty("longitude")
    public double longitude;

    @JsonProperty("latitude")
    public double latitude;

    @JsonProperty("status")
    public String status;

    @JsonProperty("parentNeId")
    public Integer parentNeId;

    @JsonProperty("parentNeType")
    public String parentNeType;

    @NotNull(message = "Please enter the mvno_id")
    @JsonProperty("mvnoId")
    public Integer mvnoId;

    @NotNull(message = "Please enter the survey area id")
    @JsonProperty("surveyAreaId")
    public Integer surveyAreaId;


    @JsonProperty("remarks")
    public String remarks;

    @JsonProperty("powerLevels")
    public BigDecimal powerLevels;

    @NotNull(message = "Please provide userId")
    @JsonProperty("userId")
    public Long userId;

    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
    private List<Inventory> inventoryList;


}
