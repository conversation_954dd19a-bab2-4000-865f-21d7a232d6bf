package com.keyanna.gis.core.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StaffUserDTO {

    private Integer staffid;
    private String username;
    private String password;
    private String firstname;
    private String lastname;
    private String sstatus;
    private String lastLoginTime;

    private Integer partnerid;
    private Boolean isDelete;
    private Integer mvnoId;
    private Integer branchid;

    private String createbyname;
    private String updatebyname;
    private Integer createdbystaffid;
    private Integer lastmodifiedbystaffid;
    private LocalDateTime createdate;
    private LocalDateTime lastmodifieddate;

    private List<Long> roleid;

    private Long serviceAreaId;
    private Integer parentStaffId;

    private String countryCode;
    private Double totalCollected;
    private Double totalTransferred;
    private Double availableAmount;

    private Integer lcoid;
    private String hrmsId;

    private byte[] profileImage;
    private String department;

    private String oldpassword1;
    private String oldpassword2;
    private String oldpassword3;

    private String otp;
    private Boolean otpvalidate;
    private Boolean sysstaff;

    private Long businessunitid;
    private String email;
    private String phone;
    private Integer failcount;

    private String accessLevelGroupName;
    private Boolean mvnoDeactivationFlag;

    private String uuid;
    private Boolean isPasswordExpired;
    private LocalDateTime passwordDate;
}
