package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class TrenchDTO {
    public Long port;
    public String name;
    public String address;
    public BigDecimal widthM;
    public BigDecimal depthM;
    public BigDecimal measuredLengthM;
    public String owner;
    public String contractor;
    public String  relatedAssets;
    public String remarks;
    public String status;

    @NotNull(message = "Please enter mvnoId")
    public Integer mvnoId;

    @NotNull(message = "Please enter surveyAreaId")
    public Integer surveyAreaId;

    public double latitude;
    public double longitude;
//    public Long modified_by;
//    public LocalDateTime created_on;
//    public LocalDateTime modified_on;

    @NotNull(message = "Please enter userId")
    public Long userId;



//
    @NotNull(message = "Please provide geom")
    @JsonProperty("geom")
    private Geometry geom;

//
    @Data
    public static class Geometry {
        private String type;
       private List<List<Double>> coordinates;
}
}
