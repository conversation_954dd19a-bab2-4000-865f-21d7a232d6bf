package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.Inventory;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class OdfDTO {

    @JsonProperty("publicId")
    private UUID publicId;

    @NotBlank(message = "Name must not be blank")
    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    @NotBlank(message = "Please enter the status")
    private String status;

    @JsonProperty("customId")
    private String customId;

    @JsonProperty("rackNo")
    private String rackNo;

    @JsonProperty("totalPorts")
    private Integer totalPorts;

    @JsonProperty("occupiedPorts")
    private Integer occupiedPorts;

    @JsonProperty("parentNeId")
    private Integer parentNeId;

    @JsonProperty("parentNeType")
    private String parentNeType;

    @NotNull(message = "mvnoId must not be null")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "surveyAreaId must not be null")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @NotNull(message = "User ID (createdBy) must not be null")
    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("longitude")
    private double longitude;



    @JsonProperty("latitude")
    private double latitude;

    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
    private List<Inventory> inventoryList;
}
