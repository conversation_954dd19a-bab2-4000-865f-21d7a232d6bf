package com.keyanna.gis.core.dto.request;

import lombok.Data;

import java.util.List;

@Data
public class CommonRequestDTO {

    private Integer childLayerId;
    private String geomType;
    private GeometryPointDTO geomPoint;
    private GeometryLineStringDTO geomLineString;
    private String layerNames;

    @Data
    public static class GeometryPointDTO {
        private String type;
        private List<Double> coordinates;   //  For Point
    }

    @Data
    public static class GeometryLineStringDTO {
        private String type;
        private List<List<Double>> coordinates;   //  For LineString
    }

}