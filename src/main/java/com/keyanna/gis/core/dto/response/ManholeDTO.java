package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ManholeDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @NotBlank(message = "Please enter the manhole size")
    @JsonProperty("manholeSize")
    private String manholeSize;

    @JsonProperty("depthCm")
    private Integer depthCm;

    @NotBlank(message = "Please enter the cover type")
    @JsonProperty("coverType")
    private String coverType;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", inclusive = true, message = "Longitude must be greater than or equal to -180")
    @DecimalMax(value = "180.0", inclusive = true, message = "Longitude must be less than or equal to 180")
    @JsonProperty("longitude")
    private Double longitude;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", inclusive = true, message = "Latitude must be greater than or equal to -90")
    @DecimalMax(value = "90.0", inclusive = true, message = "Latitude must be less than or equal to 90")
    @JsonProperty("latitude")
    private Double latitude;

    @NotNull(message = "User ID is required")
    @JsonProperty("userId")
    private Long userId;

    @NotNull(message = "MVNO ID is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area ID is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;
}