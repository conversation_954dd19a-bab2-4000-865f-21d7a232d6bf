package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class LayerInventoryMappingRequestDTO {

    @NotNull(message = "Layer ID is required")
    @JsonProperty("layerId")
    private Integer layerId;

    @NotNull(message = "Layer Code is required")
    @JsonProperty("layerCode")
    private String layerCode;

    @JsonProperty("parentParamId")
    private Integer parentParamId;

    @JsonProperty("paramId")
    private Integer paramId;

    @JsonProperty("paramName")
    private String paramName;

    @JsonProperty("paramValue")
    private String paramValue;

    @JsonProperty("isMandatory")
    private Boolean isMandatory;

    @JsonProperty("isAccessory")
    private Boolean isAccessory;

    @JsonProperty("quantity")
    private Integer quantity;

    private LocalDateTime createdOn;

    @JsonProperty("isConfiguration")
    private Boolean isConfiguration;

    @JsonProperty("status")
    private String status;

}
