package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.model.Administrative01;
import com.keyanna.gis.core.model.Administrative02;
import com.keyanna.gis.core.model.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.LineString;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class DuctDTO  {


    private UUID publicId;
    private String  name;
    private String material;
    private BigDecimal diameterMm;
    private BigDecimal measuredLengthM;

    private String status;
    private LocalDate installDate;
    private String owner;

    private Long startNodeId;
    private Long endNodeId;
    public double latitude;
    public double longitude;
    private Integer numSubducts;
    private Integer usedSubducts;

    @NotNull(message = "Please enter userId")
    private Long userId;

    private String networkType;
    private String remarks;

    @NotNull(message = "Please provide the surveyAreaId")
    private Integer surveyAreaId;

    @JsonProperty("geom")
    private Geometry geom;

    @NotNull(message = "Please provide the mvnoId")
    public Integer mvnoId;

    @JsonProperty("parentNeId")
    public Integer parentNeId;

    @JsonProperty("parentNeType")
    public String parentNeType;

    @Data
    public static class Geometry {
        private String type;
        private List<List<Double>> coordinates;
    }

}
