package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class DigitalizationResponseDTO {

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class CableDto {

        private UUID publicId;
        private String cableId;    //  db field : name // TODO change in future
        private String type;
        private String status;
        private Geometry geom;
        private Double lengthInMeters;  //  db field : gis_length_m // TODO change in future
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<List<Double>> coordinates;   //  For LineString
        }
//        private LookupCableTypeDto cableTypeDto;
//        private String mountingType;
//        private String status;
//        private CableSpecificationDto cableSpecificationDto;
//        private Double gisLengthM;
//
//        @Data
//        public static class LookupCableTypeDto {
//            private String typeName;
//            private String typeDescription;
//        }
//
//        @Data
//        public static class CableSpecificationDto {
//            private String specificationName;
//            private Integer numberOfCores;
//            private String specificationDescription;
//        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class FdtDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }
//        private Integer capacity;
//        private String address;
//        private String status;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class JointClosureDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }
//        private String jointType;
//        private Integer capacity;
//        private String status;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class OltDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class SplitterDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }

//        private String status;
//        private SplitterSpecificationDto splitterSpecificationDto;
//
//        @Data
//        public static class SplitterSpecificationDto {
//            private String specificationName;
//            private String specificationPortRatio;
//            private String specificationDescription;
//        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class SduDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;
        private String riser;
        private Integer floors;
        private Integer mvnoId;
        private String status;
        private Integer towers;
        private String address;
        private String oltName;
        private String tenancy;
        private String category;
        private Integer homePasses;
        private String streetName;
        private String opticalLevel;
        private Integer surveyAreaId;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }
    }

//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class CustomerDto {
//        private String name;
//        private String customerType;
//        private String address;
//        private Integer port;
//        private String status;
//    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
    public static class FatDto {
        private UUID publicId;
        private String name;
        private Geometry geom;
        private String layerName;

        @Data
        public static class Geometry {
            private String type;
            private List<Double> coordinates;   //  For Point
        }
//        private Integer capacity;
//        private String address;
//        private String status;
//        private BigDecimal powerLevels;
    }

//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class FdpDto {
//        private String name;
//        private String port;
//        private LookupFdpTypeDto fdpType;
//        private String status;
//
//        @Data
//        public static class LookupFdpTypeDto {
//            private String typeName;
//            private String typeDescription;
//        }
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class HandholeDto {
//        private String name;
//        private String holeSize;
//        private String accessType;
//        private String material;
//        private String status;
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class ManholeDto {
//        private String name;
//        private String manholeSize;
//        private Integer depthCm;
//        private String coverType;
//        private String status;
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class PoleDto {
//        private String name;
//        private String poleType;
//        private String status;
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class PopDto {
//        private String name;
//        private String address;
//        private String category;
//        private String status;
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class DuctDto {
//        private String name;
//        private Double lengthM;
//        private String networkType;
//        private String status;
//    }
//
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class TrenchDto {
//        private String name;
//        private Double widthM;
//        private Double depthM;
//        private Double lengthM;
//        private String status;
//    }
//
//    /**
//     * Common DTO for : convertPolygonJsonDataToResponseDtoDISA
//     */
//    @Data
//    @JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
//    public static class PolygonDataInSurveyArea {
//        private UUID publicId;
//        private String name;
//        private Geometry geom;
//        private String layerName;
//        private String surveyStatusId;
//        private String surveyStartDate;
//        private String surveyEndDate;
//        private String isActive;
//
//        @Data
//        public static class Geometry {
//            private String type;
//            private List<List<List<Double>>> coordinates;   //  For Polygon
//        }
//    }
//
//    @Data
//    public static class GeometryPointDto {
//        private String type;
//        private List<Double> coordinates;   //  For Point
//    }
//
//    @Data
//    public static class GeometryLineStringDto {
//        private String type;
//        private List<List<Double>> coordinates;   //  For LineString
//    }

}
