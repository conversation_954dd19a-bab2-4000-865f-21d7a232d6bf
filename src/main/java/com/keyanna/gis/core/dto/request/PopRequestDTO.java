package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PopRequestDTO {

    /**
     * TODO : Add validation whenever required
     */

    @NotBlank(message = "Please enter your name")
    @JsonProperty("name")
    private String name;


    @JsonProperty("address")
    private String address;

    @NotBlank(message = "Please select category")
    @JsonProperty("category")
    private String category;


    @JsonProperty("latitude")
    private double latitude;

    @JsonProperty("longitude")
    private double longitude;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "User ID is required")
    @JsonProperty("userId")
    private Long userId;


    @JsonProperty("parentNeId")
    private Integer parentNeId;

    @JsonProperty("parentNeType")
    private String parentNeType;

    @NotNull(message = "MVNO ID is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area ID is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;


//    @JsonProperty("createdOn")
//    private LocalDateTime createdOn;

//    private Instant createdOn;
//    @JsonProperty("modifiedBy")

//    private Long modifiedBy;

}
