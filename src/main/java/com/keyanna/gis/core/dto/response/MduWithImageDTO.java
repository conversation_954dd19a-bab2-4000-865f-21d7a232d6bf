package com.keyanna.gis.core.dto.response;

import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.Mdu;
import lombok.Data;

import java.util.List;

@Data
public class MduWithImageDTO {

    // From Mdu
    private Mdu mdu;

    // From LayerImageMapping
    private List<LayerImageMapping> images;

    private List<LayerInventoryMapping> inventoryList;

    public MduWithImageDTO(Mdu mdu,List<LayerImageMapping> images, List<LayerInventoryMapping> inventoryList) {
        this.mdu=mdu;
        this.images = images;
        this.inventoryList = inventoryList;
    }


}
