package com.keyanna.gis.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.anotation.AllowedValues;
import com.keyanna.gis.core.constants.ApiConstants;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class OltTrayDTO {

    @NotNull(message = "Olt ID is required")
    @JsonProperty("oltId")
    private Integer oltId;

    @NotBlank(message = "Please enter the tray name")
    @JsonProperty("trayName")
    private String trayName;

    @NotBlank(message = "Please enter the tray status")
    @JsonProperty("trayStatus")
    private String trayStatus;

    @NotBlank(message = "Please enter the Port Capacity")
    @JsonProperty("portCapacity")
    private Integer portCapacity;

    @NotBlank(message = "Please enter the description")
    @JsonProperty("description")
    private String description;

    @JsonProperty("createdOn")
    private LocalDateTime createdOn;

    @JsonProperty("modifiedOn")
    private LocalDateTime modifiedOn;

}
