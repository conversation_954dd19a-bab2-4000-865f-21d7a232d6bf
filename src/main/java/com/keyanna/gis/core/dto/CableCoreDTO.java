package com.keyanna.gis.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
public class CableCoreDTO {

    @NotNull(message = "Please enter the cable_id")
    @JsonProperty("cable_id")
    public Integer cableId;

    @JsonProperty("core_number")
    public Integer coreNumber;

    @JsonProperty("core_color")
    public String coreColor;

    @JsonProperty("core_status")
    public String coreStatus;

    @JsonProperty("tube_number")
    public Integer tubeNumber;

    @JsonProperty("splice_type")
    public String spliceType;

    @JsonProperty("splice_status")
    public String spliceStatus;
}
