package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
public class NetworkPortResponseDTO {

    public Integer id;

    public Integer layerId;

    public String layerType;

    public String portType;

    public Integer portNumber;

    public String portStatus;

    public String description;

    public LocalDateTime createdOn;

    public LocalDateTime modifiedOn;

}
