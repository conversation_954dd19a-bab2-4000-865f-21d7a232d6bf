package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ClusterMasterRequestDTO {

    /**
     * TODO : Add validation whenever required
     */
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("geom")
    private GeometryPolygonDto geom;

    @JsonProperty("isActive")
    private Boolean isActive;

    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("mvnoId")
    public Integer mvnoId;

    @Data
    public static class GeometryPolygonDto {
        private String type;
        private List<List<List<Double>>> coordinates; //  For Polygon
    }
}
