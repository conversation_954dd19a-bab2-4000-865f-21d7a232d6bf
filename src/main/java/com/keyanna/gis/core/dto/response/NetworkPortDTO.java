package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
public class NetworkPortDTO {

    @NotNull(message = "Please enter the layerId")
    @JsonProperty("layerId")
    public Integer layerId;

    @NotBlank(message = "Please enter the layerType")
    @JsonProperty("layerType")
    public String layerType;

    @NotBlank(message = "Please enter the portType")
    @JsonProperty("portType")
    public String portType;

    @NotNull(message = "Please enter the portNumber")
    @JsonProperty("portNumber")
    public Integer portNumber;

    @NotBlank(message = "Please enter the portStatus")
    @JsonProperty("portStatus")
    public String portStatus;

    @JsonProperty("description")
    public String description;

    @JsonProperty("createdOn")
    public LocalDateTime createdOn;

    @JsonProperty("modifiedOn")
    public LocalDateTime modifiedOn;

}
