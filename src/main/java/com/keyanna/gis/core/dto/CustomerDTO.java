package com.keyanna.gis.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class CustomerDTO {

    @JsonProperty("port")
    private Long port;

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("address")
    private String address;

    @NotBlank(message = "Please enter the customer type")
    @JsonProperty("customerType")
    private String customerType;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", message = "Longitude must be greater than or equal to -180")
    @DecimalMax(value = "180.0", message = "Longitude must be less than or equal to 180")
    @JsonProperty("longitude")
    private Double longitude;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", message = "Latitude must be greater than or equal to -90")
    @DecimalMax(value = "90.0", message = "Latitude must be less than or equal to 90")
    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("activationDate")
    private LocalDate activationDate;

    @NotNull(message = "User Id is required")
    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("parentId")
    private Long parentId;

    @JsonProperty("parentType")
    private String parentType;

    @NotNull(message = "Mvno Id is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "SurveyAreaId is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;
}
