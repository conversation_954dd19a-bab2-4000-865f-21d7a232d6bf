package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor  // Adds no-arg constructor needed by Jackson
public class SurveyAreaRequestDTO {

    @NotBlank(message = "Please enter the survey area name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("surveyStatusId")
    private Integer surveyStatusId;

    @NotNull(message = "Geometry (polygon) is required")
    @JsonProperty("geom")
    private GeometryPolygonDto geom;

    @JsonProperty("isActive")
    private Boolean isActive;

    @JsonProperty("surveyStartDate")
    private LocalDate surveyStartDate;

    @JsonProperty("surveyEndDate")
    private LocalDate surveyEndDate;

    @NotNull(message = "User ID is required")
    @JsonProperty("userId")
    private Long userId;

    @NotNull(message = "MVNO ID is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    /**
     * Constructor used in controller for partial DTO creation
     */
    public SurveyAreaRequestDTO(Boolean isActive, String name, Integer mvnoId) {
        this.isActive = isActive;
        this.name = name;
        this.mvnoId = mvnoId;
    }

    /**
     * Inner static class representing GeoJSON Polygon geometry
     */
    @Data
    public static class GeometryPolygonDto {

        @NotBlank(message = "Geometry type is required (should be 'Polygon')")
        private String type;

        @NotNull(message = "Coordinates must be provided for the polygon geometry")
        @JsonProperty("coordinates")
        private List<List<List<Double>>> coordinates;  // Polygon coordinates format
    }

    /**
     * Inner static DTO class to update survey status, useful for PATCH/PUT endpoints
     */
    @Data
    public static class SurveyAreaUpdateStatusDTO {

        @JsonProperty("publicId")
        private String publicId;

        @NotNull(message = "Survey status Name is required")
        @JsonProperty("surveyStatusName")
        private String surveyStatusName;

        @NotNull(message = "User ID is required")
        @JsonProperty("userId")
        private Long userId;

        @NotNull(message = "Mvno ID is required")
        @JsonProperty("mvnoId")
        private Integer mvnoId;
    }
}
