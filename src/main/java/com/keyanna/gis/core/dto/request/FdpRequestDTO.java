package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FdpRequestDTO {

    /**
     * TODO : Add validation whenever required
     */

    @NotBlank(message = "name must not be blank")
    @JsonProperty("name")
    private String name;

    @NotNull(message = "port is required")
    @JsonProperty("port")
    private Integer port;

    @NotNull(message = "fdpTypeId is required")
    @JsonProperty("fdpTypeId")
    private Integer fdpTypeId;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "Longitude is required")
    @JsonProperty("longitude")
    private double longitude;

    @NotNull(message = "Latitude is required")
    @JsonProperty("latitude")
    private double latitude;

    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("popId")
    private Integer popId;

    @NotNull(message = "mvnoId is required")
    @JsonProperty("mvnoId")
    public Integer mvnoId;

    @NotNull(message = "surveyAreaId is required")
    @JsonProperty("surveyAreaId")
    public Integer surveyAreaId;

//    @JsonProperty("createdOn")
//    private LocalDateTime createdOn;

//    private Instant createdOn;

//    @JsonProperty("modifiedBy")
//    private Long modifiedBy;

}
