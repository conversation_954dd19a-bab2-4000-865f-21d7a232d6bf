package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.*;
import lombok.Data;

@Data
public class FdtDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("address")
    private String address;

    @NotNull(message = "Capacity is required")
    @JsonProperty("capacity")
    private Long capacity;

    @NotNull(message = "User Id is required")
    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", message = "Longitude must be greater than or equal to -180")
    @DecimalMax(value = "180.0", message = "Longitude must be less than or equal to 180")
    @JsonProperty("longitude")
    private Double longitude;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", message = "Latitude must be greater than or equal to -90")
    @DecimalMax(value = "90.0", message = "Latitude must be less than or equal to 90")
    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("parentNeId")
    private Long parentNeId;

    @JsonProperty("parentNeType")
    private String parentNeType;

    @JsonProperty("adm1Id")
    private Integer adm1Id;

    @JsonProperty("adm2Id")
    private Integer adm2Id;

    @NotNull(message = "Mvno Id is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area Id is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @NotNull(message = "Core number is required")
    @Min(value = 1, message = "Core number must be at least 1")
    @Max(value = 24, message = "Core number must not exceed 24")
    private Integer coreNumber;

    @NotNull(message = "Card slot number is required")
    @Min(value = 1, message = "Card slot number must be at least 1")
    @Max(value = 18, message = "Card slot number must not exceed 18")
    private Integer cardSlotNumber;

    @NotNull(message = "PON port is required")
    @Min(value = 1, message = "PON port must be at least 1")
    @Max(value = 16, message = "PON port must not exceed 16")
    private Integer ponPort;

}
