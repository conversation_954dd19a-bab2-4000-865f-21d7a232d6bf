package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.anotation.AllowedValues;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CableRequestDTO {

    @NotBlank(message = "Please enter your name")
    @JsonProperty("name")
    private String name;


    @NotBlank(message = "Please enter mounting type")
    @JsonProperty("mountingType")
    private String mountingType;


    @JsonProperty("status")
    private String status;

    @JsonProperty("cableTypeId")
    private Integer cableTypeId;

    @JsonProperty("specificationId")
    private Integer specificationId;

    @JsonProperty("measuredLengthM")
    private BigDecimal measuredLengthM;

    @JsonProperty("installationDate")
    private LocalDate installationDate;

    @JsonProperty("parentNeId")
    private Integer parentNeId;

    @JsonProperty("parentNeType")
    private String parentNeType;

    @JsonProperty("remarks")
    private String remarks;

    @NotNull(message = "Please provide geometry information")
    @JsonProperty("geom")
    private Geometry geom;

    @NotNull(message = "Please enter your user ID")
    @JsonProperty("userId")
    private Long userId;

    @JsonProperty("createdOn")
    private LocalDateTime createdOn;

    @JsonProperty("modifiedBy")
    private Long modifiedBy;

    @Data
    public static class Geometry {
        private String type;
        private List<List<Double>> coordinates;
    }

    @NotNull(message = "Please enter MVNO ID")
    @JsonProperty("mvnoId")
    public Integer mvnoId;

    @NotNull(message = "Please enter Survey Area ID")
    @JsonProperty("surveyAreaId")
    public Integer surveyAreaId;

    @NotNull(message = "Please enter Splitter Id")
    @JsonProperty("splitterId")
    public Integer splitterId;

    @NotNull(message = "Please enter Splitter Port Id")
    @JsonProperty("splitterPortId")
    public Integer splitterPortId;

    @NotNull(message = "Please enter Splitter Sdu Id")
    @JsonProperty("sduId")
    public Integer sduId;

    @AllowedValues(values = {"Through", "Splice"}, message = "Connection Type must be 'Through' or 'Splice'")
    @NotBlank(message = "Please enter your Connection Type")
    @JsonProperty("connectionType")
    private String connectionType;
}
