package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.anotation.AllowedValues;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.Inventory;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class OltDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    @NotBlank(message = "Please enter the status")
    private String status;

    @AllowedValues(values = {"Primary", "Secondary"}, message = "Optical Level must be 'Primary' or 'Secondary'")
    @NotBlank(message = "Please enter the Optical Level")
    @JsonProperty("opticalLevel")
    private String opticalLevel;

    @AllowedValues(values = {"Yes", "No"}, message = "Up Link Protection must be 'Yes' or 'No'")
    @NotBlank(message = "Please enter the Up Link Protection")
    @JsonProperty("upLinkProtection")
    private String upLinkProtection;

    @AllowedValues(values = {"Yes", "No"}, message = "Power Backup must be 'Yes' or 'No'")
    @NotBlank(message = "Please enter the Power Backup")
    @JsonProperty("powerBackup")
    private String powerBackup;

    @JsonProperty("vendor")
    private String vendor;

    @JsonProperty("model")
    private String model;

    @JsonProperty("slots")
    private Integer slots;

    @JsonProperty("activePorts")
    private Integer activePorts;

    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", inclusive = true, message = "Longitude must be greater than or equal to -180")
    @DecimalMax(value = "180.0", inclusive = true, message = "Longitude must be less than or equal to 180")
    @JsonProperty("longitude")
    private Double longitude;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", inclusive = true, message = "Latitude must be greater than or equal to -90")
    @DecimalMax(value = "90.0", inclusive = true, message = "Latitude must be less than or equal to 90")
    @JsonProperty("latitude")
    private Double latitude;

    @NotNull(message = "MVNO ID is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area ID is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @NotNull(message = "Please provide userId")
    @JsonProperty("userId")
    private Long userId;

    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
    private List<Inventory> inventoryList;

    @JsonProperty("oltTrayCapacity")
    private Integer oltTrayCapacity;

    @JsonProperty("oltTrayPortCapacity")
    private Integer oltTrayPortCapacity;


}
