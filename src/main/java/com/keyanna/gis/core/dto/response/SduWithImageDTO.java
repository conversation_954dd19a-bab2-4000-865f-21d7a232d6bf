package com.keyanna.gis.core.dto.response;

import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import com.keyanna.gis.core.model.Sdu;
import lombok.Data;

import java.util.List;

@Data
public class SduWithImageDTO {

    // From Sdu
    private Sdu sdu;

    // From LayerImageMapping
    private List<LayerImageMapping> images;

    private List<LayerInventoryMapping> inventoryList;

    public SduWithImageDTO(Sdu sdu,List<LayerImageMapping> images, List<LayerInventoryMapping> inventoryList) {
        this.sdu=sdu;
        this.images = images;
        this.inventoryList = inventoryList;
    }


}
