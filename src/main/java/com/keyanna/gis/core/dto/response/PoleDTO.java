package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.Inventory;
import com.keyanna.gis.core.model.Pole;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PoleDTO {

    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("heightM")
    private BigDecimal heightM;

//    @NotBlank(message = "Please enter the material")   // commented as not needed for now
//    @JsonProperty("material")
//    private String material;

//    @NotBlank(message = "Please enter the pole type")  // commented as not needed for now
//    @JsonProperty("poleType")
//    private String poleType;

//    @NotBlank(message = "Please enter the ownership")  // commented as not needed for now
//    @JsonProperty("ownership")
//    private String ownership;

    @JsonProperty("longitude")
    private double longitude;

    @JsonProperty("latitude")
    private double latitude;

    @JsonProperty("status")
    private String status;

    @NotNull(message = "Please enter the mvno_id")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Please provide userId")
    @JsonProperty("userId")
    private Long userId;

    @NotNull(message = "Please enter the surveyAreaId")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("adss")
    private String adss;

    @JsonProperty("upb")
    private String upb;

    @JsonProperty("jHook")
    private String jHook;

    @JsonProperty("anchor")
    private String anchor;

    @JsonProperty("cable")
    private String cable;

    @JsonProperty("slack")
    private String slack;

    @JsonProperty("jb")
    private String jb;

    @JsonProperty("fat")
    private String fat;

    @JsonProperty("guyGrip")
    private String guyGrip;

    @JsonProperty("photo")
    private String photo;

    @JsonProperty("photo2")
    private String photo2;

    @JsonProperty("photo3")
    private String photo3;

    @JsonProperty("poleSizeType")
    private String poleSizeType;

    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
    private List<Inventory> inventoryList;



}