package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class PopResponseDTO {

    @NotBlank(message = "Please enter your name")
    @JsonProperty("name")
    private String name;

    private String address;

    @NotBlank(message = "Please select category")
    @JsonProperty("category")
    private String category;

    @NotNull(message = "Geometry (geom) is required")
    @JsonProperty("geom")
    private Geometry geom;


    private String status;

    @NotNull(message = "User ID is required")
    @JsonProperty("userId")
    private Long userId;


    private ZonedDateTime createdOn;

    @Data
    public static class Geometry {
        private String type;
        private List<Double> coordinates;
    }
}
