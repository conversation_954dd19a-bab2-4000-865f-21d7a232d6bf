package com.keyanna.gis.core.dto;


import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.keyanna.gis.core.data.Auditable;
import com.keyanna.gis.core.dto.CustomACLEntryDTO;
import com.keyanna.gis.core.dto.RoleACLEntryDTO;
import com.keyanna.gis.core.dto.AclMenuStructureDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
public class RoleDTO extends Auditable implements IBaseDto {

    private Long id;

    @NotNull
    private String rolename;

    @NotNull
    private String status;

    @NotNull
    private Boolean sysRole = false;

    private Set<Integer> staffuserIds;

    private List<CustomACLEntryDTO> aclEntryPojoList = new ArrayList<>();

    private List<RoleACLEntryDTO> aclMenu = new ArrayList<>();

    private List<AclMenuStructureDTO> aclMenus = new ArrayList<>();

    private Integer mvnoId;

    private Integer lcoId;

    private String product;

    private Boolean isDelete = false;

    // Optional if used in auditing context
    @JsonIgnore
    public Long getIdentityKey() {
        return id;
    }

    public Integer getMvnoId() {
        return mvnoId;
    }
}
