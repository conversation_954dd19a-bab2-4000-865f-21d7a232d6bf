package com.keyanna.gis.core.dto.response;

import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.model.LayerInventoryMapping;
import lombok.Data;

import java.util.List;

@Data
public class FatWithImageDTO {

    private Fat fat;
    private List<LayerImageMapping> images;
    private List<LayerInventoryMapping> inventoryList;

    public FatWithImageDTO(Fat fat, List<LayerImageMapping> images, List<LayerInventoryMapping> inventoryList) {
        this.fat=fat;
        this.images = images;
        this.inventoryList = inventoryList;
    }
}
