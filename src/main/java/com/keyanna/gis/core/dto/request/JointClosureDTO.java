package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class JointClosureDTO {
    
    @NotBlank(message = "Please enter the name")
    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    @NotBlank(message = "Please enter the joint type")
    @JsonProperty("jointType")
    public String jointType;

    @JsonProperty("capacity")
    private Integer capacity;

    @NotBlank(message = "Please enter where the joint is mounted")
    @JsonProperty("mountedIn")
    public String mountedIn;

    @JsonProperty("parentNeType")
    public String parentNeType;

    @JsonProperty("parentNeId")
    public Long parentNeId;

    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", message = "Longitude must be >= -180")
    @DecimalMax(value = "180.0", message = "Longitude must be <= 180")
    @JsonProperty("longitude")
    private Double longitude;

    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", message = "Latitude must be >= -90")
    @DecimalMax(value = "90.0", message = "Latitude must be <= 90")
    @JsonProperty("latitude")
    private Double latitude;

    @NotNull(message = "User ID is required")
    @JsonProperty("userId")
    private Long userId;

    @NotNull(message = "MVNO ID is required")
    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @NotNull(message = "Survey Area ID is required")
    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;
}
