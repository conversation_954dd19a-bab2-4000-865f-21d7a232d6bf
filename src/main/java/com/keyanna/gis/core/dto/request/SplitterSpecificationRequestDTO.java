package com.keyanna.gis.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SplitterSpecificationRequestDTO {

    /**
     * TODO : Add validation whenever required
     */

    @JsonProperty("name")
    private String name;

    @JsonProperty("portRatio")
    private String portRatio;

    @JsonProperty("description")
    private String description;

    @JsonProperty("isActive")
    private Boolean isActive;
}
