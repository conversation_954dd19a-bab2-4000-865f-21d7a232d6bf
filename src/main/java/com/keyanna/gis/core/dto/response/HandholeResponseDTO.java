package com.keyanna.gis.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class HandholeResponseDTO {

    private Integer id;
    private UUID publicId;
    private String customId;
    private String name;
    private String holeSize;
    private String accessType;
    private String material;
    private Integer adm1Id;
    private Integer adm2Id;
    private Long userId;
    private String geom;
    private String status;
    private Long createdBy;
    private LocalDateTime CreatedOn;
    private LocalDateTime modifiedOn;
    private Long modifiedBy;
    private Integer surveyAreaId;
    private Integer mvnoId;
    private Double latitude;
    private Double longitude;



}
