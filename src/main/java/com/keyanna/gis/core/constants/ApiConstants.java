package com.keyanna.gis.core.constants;

public final class ApiConstants {

    private ApiConstants() {
        // private constructor to prevent instantiation
    }

    //  TODO add other here in future from reference table : layer_master : column (code)
    /**
     * DO Not change below
     */
    //  Start
    public static final String LAYER_CODE_CABLE = "cable";
    public static final String LAYER_CODE_CUSTOMER = "customer";
    public static final String LAYER_CODE_DUCT = "duct";
    public static final String LAYER_CODE_FAT = "fat";
    public static final String LAYER_CODE_FDP = "fdp";
    public static final String LAYER_CODE_FDT = "fdt";
    public static final String LAYER_CODE_HANDHOLE = "handhole";
    public static final String LAYER_CODE_JOINT_CLOSURE = "jointclosure";
    public static final String LAYER_CODE_MANHOLE = "manhole";
    public static final String LAYER_CODE_POP = "pop";
    public static final String LAYER_CODE_POLE = "pole";
    public static final String LAYER_CODE_OLT = "olt";
    public static final String LAYER_CODE_ODF = "odf";
    public static final String LAYER_CODE_SDU = "sdu";
    public static final String LAYER_CODE_TRENCH = "trench";
    public static final String LAYER_CODE_STREET = "street";
    public static final String LAYER_CODE_SPLITTER = "splitter";
    public static final String LAYER_CODE_CDU = "cdu";
    public static final String LAYER_CODE_MDU = "mdu";


    public static final String LAYER_TYPE_SPLITTER = "splitter";
    public static final String LAYER_TYPE_CABLE = "cable";
    public static final String LAYER_TYPE_SDU = "sdu";

    public static final String STATUS_USED = "Used";
    public static final String STATUS_AVAILABLE = "Available";

    //  End

    public static final String DEFAULT_STATUS_PLANNED = "Planned";
    public static final String LABEL_LINESTRING = "LineString";
    public static final String LABEL_POINT = "Point";
    public static final String DEFAULT_STATUS_OWNED = "Owned";
    public static final String DEFAULT_STATUS_RESIDENTIAL = "Residential";

    public static final String POLE_STATUS = "Planned";
    public static final String POLE_MATERIAL = "Wood";
    public static final String POLE_TYPE = "Straight";
    public static final String POLE_ownership = "Private";

    public static final String INDIA_TIME_ZONE_STATIC = "Asia/Kolkata";
//    public static final String INDIA_TIME_ZONE_STATIC = "Africa/Nairobi";

    // Network element statues for status column
    public static final String STATUS_PLANNED = "Planned";
    public static final String STATUS_InService = "InService";
    public static final String STATUS_InActive = "Inactive";


    public static final String LABEL_CABLE = "cable";
    public static final String LABEL_FAT = "fat";
    public static final String LABEL_POP = "pop";
    public static final String LABEL_FDP = "fdp";
    public static final String LABEL_FDT = "fdt";
    public static final String LABEL_CUSTOMER = "customer";
    public static final String LABEL_OLT = "olt";
    public static final String LABEL_STREET = "street";

    public static final String LABEL_ODF = "odf";
    public static final String LABEL_SDU = "sdu";
    public static final String LABEL_CDU = "cdu";
    public static final String LABEL_MDU = "mdu";

    public static final String LABEL_SPLITTER = "splitter";
    public static final String LABEL_HANDHOLE = "handhole";
    public static final String LABEL_MANHOLE = "manhole";
    public static final String LABEL_JOINT_CLOSURE = "jointClosure";
    public static final String LABEL_TRENCH = "trench";
    public static final String LABEL_DUCT = "duct";

    public static final String LABEL_POLE = "pole";
    public static final String LABEL_POLE_SIZE_TYPE_8M = "8m";   //  pole_size_type (Table : ne_pole)
    public static final String LABEL_POLE_SIZE_TYPE_10M = "10m";   //  pole_size_type (Table : ne_pole)
    public static final String LABEL_POLE_SIZE_TYPE_12M = "12m";   //  pole_size_type (Table : ne_pole)

    public static final String LABEL_SURVEY_AREA = "survey_area";
    public static final String LABEL_SURVEY = "Survey"; //  Default Survey stage name

    public static final String ADM00 = "/adm00";
    public static final String ADM01 = "/adm01";
    public static final String ADM02 = "/adm02";
    public static final String STREET = "/Street";
    public static final String BASE_API_URL = "/api/v1/GisCore";
    public static final String FDP_TYPE = "/fdpType";
    public static final String SDU = "/sdu";
    public static final String CDU = "/cdu";
    public static final String MDU = "/mdu";
    public static final String CABLE = "/cable";
    public static final String CABLE_SPECIFICATION = "/cableSpecification";
    public static final String CLUSTER_MASTER = "/clusterMaster";
    public static final String CUSTOMER = "/customer";
    public static final String DUCT = "/duct";
    public static final String FAT = "/fat";
    public static final String FDT = "/fdt";
    public static final String FDP = "/fdp";
    public static final String HANDHOLE = "/handhole";
    public static final String JOINT_CLOSURE = "/jointClosure";
    public static final String LAYERS = "/layers";
    public static final String LOOKUP_CABLE = "/lookupCable";
    public static final String SURVEY_STATUS = "/surveyStatus";
    public static final String SURVEY_STAGE = "/surveyStage";
    public static final String MANHOLE = "/manhole";
    public static final String POLE = "/pole";
    public static final String ODF = "/odf";
    public static final String POP = "/pop";
    public static final String SPLITTER = "/splitter";
    public static final String SPLITTER_SPECIFICATION = "/splitterSpecification";
    public static final String SURVEY_AREA = "/surveyArea";
    public static final String SURVEY_USER_MAPPING = "/surveyUserMapping";
    public static final String LAYER_INVENTORY_MAPPING = "/layerInventoryMapping";
    public static final String TRENCH = "/trench";
    public static final String COMMON = "/common";
    public static final String OLT = "/olt";
    public static final String NETWORK_PORT = "/networkPort";
    public static final String NETWORK_ELEMENT_CONNECTION = "/networkElementConnection";

    public static final String PUBLIC_ID = "publicId";
    public static final String NAME = "name";
    public static final String LAYER_NAME = "layerName";
    public static final String GEOM = "geom";
    public static final String LAYER_MAPPING = "/layerMapping";

    public static final String LABEL_ROLE_ADMIN = "Admin";

    public static final String LABEL_INVENTORY_LIST = "inventoryList";

    //Survey Status
    public static final String SURVEY_STATUS_COMPLETED = "Completed";
    public static final String SURVEY_STATUS_IN_PROGRESS = "In progress";
    public static final String SURVEY_STATUS_INITIATED = "Initiated";
    public static final String SURVEY_STATUS_APPROVED = "Approved";
    public static final String SURVEY_STATUS_DONE = "Done";


    //Survey Stages
    public static final String SURVEY_STAGE_SURVEY = "Survey";
    public static final String SURVEY_STAGE_DIGITALIZATION = "Digitalization";
    public static final String SURVEY_STAGE_DESIGN = "Design";
    public static final String SURVEY_STAGE_BOM = "BOM";
    public static final String SURVEY_STAGE_INVENTORY_ACQUISITION = "Inventory acquisition";
    public static final String SURVEY_STAGE_BUILD = "Build";
    public static final String SURVEY_STAGE_QUALITY_CHECK = "Quality Check";


    public static final String PORT_INPUT = "Input";
    public static final String PORT_OUTPUT = "Output";

    //  OLT Tray
    public static final String NETWORK_LAYER_NAME_OLT_TRAY = "OLT_TRAY";
    public static final String OLT_TRAY_CAPACITY = "Olt Tray Capacity";
    public static final String OLT_TRAY_PORT_CAPACITY = "Olt Tray Port Capacity";

}
