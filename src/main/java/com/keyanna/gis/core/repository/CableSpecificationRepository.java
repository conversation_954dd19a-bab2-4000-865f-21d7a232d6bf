package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.CableSpecification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CableSpecificationRepository extends JpaRepository<CableSpecification, Integer> {

    List<CableSpecification> findByIsActiveTrue();

    boolean existsById(Integer id);

    void deleteById(Integer id);

}
