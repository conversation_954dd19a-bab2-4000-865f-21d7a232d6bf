package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Common;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Repository
public interface CommonRepository extends JpaRepository<Common, Integer> {

    //  TODO DO not delete below method code, Currently not in use
    // Native query to fetch the geometries from multiple tables
//    @Query(value = "SELECT * FROM get_geometries_within_polygon(:geojsonInput)", nativeQuery = true)
//    String findGeometriesWithinPolygon(@Param("geojsonInput") String geojsonInput);

//    @Query(value = "SELECT * FROM get_geometries_in_survey_area(:surveyAreaId)", nativeQuery = true)
//    String getDataInSurveyArea(@Param("surveyAreaId") Integer surveyAreaId,@Param("mvnoId") Integer mvnoId);

    @Query(value = "SELECT * FROM get_data_in_survey_area_bom(:surveyAreaId)", nativeQuery = true)  // old bom  creation
    String getDataInSurveyAreaBom(@Param("surveyAreaId") Integer surveyAreaId);

//    @Query(value = "SELECT * FROM get_designed_data_in_survey_area_bom(:surveyAreaId)", nativeQuery = true) // new bom creation
//    String getDataInSurveyAreaBom(@Param("surveyAreaId") Integer surveyAreaId);

    @Query(value = "SELECT * FROM find_parent_data_near_child(:childLayerId, :childGeom, :radiusMeter)", nativeQuery = true)
    String findParentNearChildGeom(
            @Param("childLayerId") Integer childLayerId,
            @Param("childGeom") String childGeom,
            @Param("radiusMeter") Double searchRadiusMeter
    );

    @Query(value = "SELECT * FROM find_near_by_geom(:geojson_input, :radius_length_meters, :table_names)", nativeQuery = true)
    String findNearByGeom(
            @Param("geojson_input") String geoJsonInputString,
            @Param("radius_length_meters") Double radiusLengthMeters,
            @Param("table_names") String[] tableNamesArray
    );


    @Query(value = """
    SELECT u.staffid, u.username
    FROM tblmstaffuser u
        JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
        JOIN tblmroles r ON rel.roleid = r.roleid
    WHERE LOWER(r.rolename) != LOWER(:roleName)
      AND u.mvnoid = :mvnoId
    """, nativeQuery = true)
    List<Map<String, Object>> getStaffDetailsExcludingRoleAndMvnoId(
            @Param("roleName") String roleName,
            @Param("mvnoId") Integer mvnoId
    );

    @Query(value = "SELECT * FROM get_geometries_in_survey_area(:surveyAreaId, :mvnoId)", nativeQuery = true)
    String getDataInSurveyArea(@Param("surveyAreaId") Integer surveyAreaId, @Param("mvnoId") Integer mvnoId);

    @Query(value = "SELECT * FROM get_survey_area_data_digitalization(:surveyAreaId, :mvnoId)", nativeQuery = true)
    String getSurveyDataForDigitalization(@Param("surveyAreaId") Integer surveyAreaId, @Param("mvnoId") Integer mvnoId);

}
