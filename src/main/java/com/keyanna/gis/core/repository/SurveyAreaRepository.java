package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LookupSurveyStatus;
import com.keyanna.gis.core.model.SurveyArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SurveyAreaRepository extends JpaRepository<SurveyArea, Integer> {


    void deleteByPublicId(UUID publicId);

    Optional<SurveyArea> findByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);

    @Query(value = """
            WITH user_info AS (
                SELECT u.staffid, LOWER(r.rolename) AS rolename
                FROM tblmstaffuser u
                JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
                JOIN tblmroles r ON rel.roleid = r.roleid
                WHERE u.staffid = :userId
            )
            SELECT sa.id, sa.public_id, sa.name, lss.id AS status_id, lss.name AS status_name
            FROM survey_area sa
            JOIN user_info ui ON TRUE
            LEFT JOIN lookup_survey_status lss ON sa.survey_status_id = lss.id
            WHERE
                -- Admin: only surveys created by that user
                (ui.rolename = LOWER(:roleName) AND sa.created_by = ui.staffid)
            
                -- Normal user: only assigned surveys via mapping
                OR (ui.rolename != LOWER(:roleName) AND sa.id IN (
                    SELECT sum.survey_area_id
                    FROM survey_user_mapping sum
                    WHERE sum.user_id = ui.staffid
                ));
            """, nativeQuery = true)
    List<Object[]> getSurveyAreaByUserId0(@Param("userId") Integer userId, @Param("roleName") String roleName);


    @Query(value = """
    WITH user_info AS (
        SELECT u.staffid, LOWER(r.rolename) AS rolename
        FROM tblmstaffuser u
        JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
        JOIN tblmroles r ON rel.roleid = r.roleid
        WHERE u.staffid = :userId
    )
    SELECT sa.id, sa.public_id, sa.name, lss.id AS status_id, lss.name AS status_name, lsstg.name AS stage_name
    FROM survey_area sa
    JOIN user_info ui ON TRUE
    LEFT JOIN lookup_survey_status lss ON sa.survey_status_id = lss.id
    LEFT JOIN lookup_survey_stage lsstg ON sa.survey_stage_id = lsstg.id
    WHERE
        sa.mvno_id = :mvnoId AND (
            -- Admin: return all surveys for the mvno
            ui.rolename = LOWER(:roleName)

            -- Other users: return assigned surveys only for the mvno
            OR (ui.rolename != LOWER(:roleName) AND sa.id IN (
                SELECT sum.survey_area_id
                FROM survey_user_mapping sum
                WHERE sum.user_id = ui.staffid
            ))
        );
    """, nativeQuery = true)
    List<Object[]> getSurveyAreaByUserIdAndMvnoId(@Param("userId") Integer userId,@Param("mvnoId") Integer mvnoId, @Param("roleName") String roleName);

    /**
     * 1. If rolename = Admin and sysrole = true
     * 1.1 Then All survey should be visible
     * For other users
     * 2. If Survey Created by particular user
     * 2.1 Then it should be visible
     * 3. If survey assigned to that user then it should be visible
     *
     * @param userId
     * @return
     */

//    @Query(value = """
//            WITH user_info AS (
//                SELECT
//                    u.staffid,
//                    LOWER(r.rolename) AS rolename,
//                    r.sysrole
//                FROM tblmstaffuser u
//                JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
//                JOIN tblmroles r ON rel.roleid = r.roleid
//                WHERE u.staffid = :userId
//            )
//            SELECT
//                sa.id,
//                sa.public_id,
//                sa.name,
//                lss.id AS status_id,
//                lss.name AS status_name,
//                lsstg.name AS stage_name
//            FROM survey_area sa
//            JOIN user_info ui ON TRUE
//            LEFT JOIN lookup_survey_status lss ON sa.survey_status_id = lss.id
//            LEFT JOIN lookup_survey_stage lsstg ON sa.survey_stage_id = lsstg.id
//            WHERE (
//                -- Admin with sysrole = true can see all
//                ui.rolename = LOWER(:roleName) AND ui.sysrole = TRUE
//            )
//            OR (
//                -- For other users:
//                -- 1. Created by the user
//                sa.created_by = ui.staffid
//
//                -- 2. OR assigned to the user
//                OR sa.id IN (
//                    SELECT sum.survey_area_id
//                    FROM survey_user_mapping sum
//                    WHERE sum.user_id = ui.staffid
//                )
//            )
//            """, nativeQuery = true)
  //  List<Object[]> getSurveyAreaByUserIdAtLayer(@Param("userId") Integer userId, @Param("roleName") String roleName);
    @Query(value = """
    WITH user_info AS (
        SELECT 
            u.staffid, 
            LOWER(r.rolename) AS rolename, 
            r.sysrole
        FROM tblmstaffuser u
        JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
        JOIN tblmroles r ON rel.roleid = r.roleid
        WHERE u.staffid = :userId
    )
    SELECT 
        sa.id, 
        sa.public_id, 
        sa.name, 
        lss.id AS status_id, 
        lss.name AS status_name, 
        lsstg.name AS stage_name
    FROM survey_area sa
    JOIN user_info ui ON TRUE
    LEFT JOIN lookup_survey_status lss ON sa.survey_status_id = lss.id
    LEFT JOIN lookup_survey_stage lsstg ON sa.survey_stage_id = lsstg.id
    WHERE sa.mvno_id = :mvnoId AND (
        -- Admin with sysrole = true can see all for mvnoId
        (ui.rolename = LOWER(:roleName) AND ui.sysrole = TRUE)
        
        OR (
            -- Other users can:
            -- 1. See what they created
            sa.created_by = ui.staffid

            -- 2. Or see what they are assigned to
            OR sa.id IN (
                SELECT sum.survey_area_id
                FROM survey_user_mapping sum
                WHERE sum.user_id = ui.staffid
            )
        )
    )
    """, nativeQuery = true)
    List<Object[]> getSurveyAreaByUserIdAtLayerAndMvnoId(
            @Param("userId") Integer userId,
            @Param("roleName") String roleName,
            @Param("mvnoId") Integer mvnoId
    );


    @Query(value = """
                SELECT COUNT(*) > 0
                FROM survey_area sa
                WHERE sa.id = :surveyAreaId
                  AND ST_Within(ST_SetSRID(ST_MakePoint(:longitude, :latitude), 4326), sa.geom)
            """, nativeQuery = true)
    boolean isPointWithinSurveyArea(@Param("surveyAreaId") Integer surveyAreaId,
                                    @Param("longitude") Double longitude,
                                    @Param("latitude") Double latitude);


    @Query(value = "SELECT ST_Contains(sa.geom, ST_SetSRID(ST_Point(:lon, :lat), 4326)) " +
            "FROM survey_area sa WHERE sa.id = :surveyAreaId", nativeQuery = true)
    boolean isPointInsideSurveyArea(@Param("surveyAreaId") Integer surveyAreaId,
                                    @Param("lon") double lon,
                                    @Param("lat") double lat);

    List<SurveyArea> findByLookupSurveyStatus(LookupSurveyStatus lookupSurveyStatus);


//    @Query(value = """
//        SELECT sa.* FROM survey_area sa
//        JOIN tbltstaffrolerel srr ON sa.created_by = srr.staffid
//        JOIN tblmroles r ON srr.roleid = r.roleid
//        WHERE r.rolename = 'Admin' AND sa.survey_status_id = 2
//        """, nativeQuery = true)

    //       @Query(value = """
    //       SELECT sa.* FROM survey_area sa
    //      JOIN tbltstaffrolerel srr ON sa.created_by = srr.staffid
    //       JOIN tblmroles r ON srr.roleid = r.roleid
    //      WHERE r.rolename = 'Admin' AND sa.survey_status_id = 2
    //      """, nativeQuery = true)
    //   List<SurveyArea> findInitiatedSurveysByAdminRole();

    @Query(value = """
            SELECT 
                sa.id,               -- s[0]
                sa.name,             -- s[1]
                sa.description,      -- s[2]
                sa.survey_status_id, -- s[3]
                ST_AsGeoJSON(sa.geom), -- s[4]
                sa.is_active,        -- s[5]
                sa.survey_start_date,-- s[6]
                sa.survey_end_date,  -- s[7]
                sa.created_by,       -- s[8]
                sa.mvno_id           -- s[9]
            FROM survey_area sa
            JOIN tbltstaffrolerel srr ON sa.created_by = srr.staffid
            JOIN tblmroles r ON srr.roleid = r.roleid
            WHERE LOWER(r.rolename) = 'admin' AND sa.survey_status_id = 2
            """, nativeQuery = true)
    List<Object[]> findInitiatedSurveyDataByAdminRole(Integer mvnoId);

    Optional<SurveyArea> findByPublicId(UUID publicId);

    List<SurveyArea> findAllByMvnoId(Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Query(value = """
            WITH user_info AS (
                SELECT u.staffid, LOWER(r.rolename) AS rolename
                FROM tblmstaffuser u
                JOIN tbltstaffrolerel rel ON u.staffid = rel.staffid
                JOIN tblmroles r ON rel.roleid = r.roleid
                WHERE u.staffid = :userId
            )
            SELECT
                sa.id,
                sa.public_id,
                sa.name,
                lss.id AS status_id,
                lss.name AS status_name,
                lsstg.name AS stage_name
            FROM survey_area sa
            JOIN user_info ui ON TRUE
            LEFT JOIN lookup_survey_status lss ON sa.survey_status_id = lss.id
            LEFT JOIN lookup_survey_stage lsstg ON sa.survey_stage_id = lsstg.id
            WHERE
                sa.mvno_id = :mvnoId
                AND lss.name = :surveyStatus
                AND sa.created_by = ui.staffid
                AND ui.rolename = LOWER(:roleName);
    """, nativeQuery = true)
    List<Object[]> getInitiatedSurveyAreaByUserIdAndMvnoId(@Param("userId") Integer userId,@Param("mvnoId") Integer mvnoId, @Param("roleName") String roleName,@Param("surveyStatus") String surveyStatus);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);
}