package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LookupSurveyStage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LookupSurveyStageRepository extends JpaRepository<LookupSurveyStage, Integer> {

    List<LookupSurveyStage> findByIsActiveTrueAndMvnoId(Integer mvnoId);

    LookupSurveyStage findByNameIgnoreCase(String name);

}
