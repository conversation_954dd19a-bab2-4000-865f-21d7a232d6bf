package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.AdministrativeUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdministrativeUnitRepository extends JpaRepository<AdministrativeUnit, Long> {
    List<AdministrativeUnit> findByParent_Adm2Code(Long adm1Code); // get all adm2 under adm1
}