package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Mdu;
import com.keyanna.gis.core.model.Fat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface MduRepository extends JpaRepository<Mdu, Integer>, PublicIdRepository<Mdu> {

    @Override
    Optional<Mdu> findByPublicId(UUID publicId);

    @Query(value = """
    SELECT * FROM ne_mdu nb
    LEFT JOIN layer_image_mapping lim ON lim.layer_id = nb.id
    WHERE lim.layer_type = :layerType AND nb.public_id = :publicId
""", nativeQuery = true)
    Optional<Mdu> findMduWithPublicIdAndLayerImageMapping(@Param("publicId") UUID publicId, @Param("layerType") String layerType);

    boolean existsByPublicId(UUID publicId);

    void deleteByPublicId(UUID publicId);

    Optional<Mdu> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<Mdu> findAllByMvnoId(Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE Mdu nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Mdu e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Mdu> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
