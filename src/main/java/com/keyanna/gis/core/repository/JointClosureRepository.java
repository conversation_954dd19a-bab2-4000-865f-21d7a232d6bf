package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.JointClosure;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface JointClosureRepository extends JpaRepository<JointClosure, Integer>, PublicIdRepository<JointClosure> {

    boolean existsByPublicId(UUID publicId);

    void deleteByPublicId(UUID publicId);

    Optional<JointClosure> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<JointClosure> findAllByMvnoId(Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE JointClosure nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM JointClosure e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<JointClosure> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);


    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}