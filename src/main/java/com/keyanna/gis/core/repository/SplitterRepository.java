package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SplitterRepository extends JpaRepository<Splitter, Integer>, PublicIdRepository<Splitter> {


    boolean existsBySplitterSpecification_Id(Integer id);


    List<Splitter> findByParentNeIdAndParentNeType(Integer parentId, String parentNeType);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    Optional<Splitter> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE Splitter nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status, @Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Splitter e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Splitter> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId, @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

    /**
     * TODO remove this condition in future, AND np.port_type = 'Output'
     */
    @Query("""
            SELECT np
            FROM NetworkPort np
            WHERE LOWER(np.layerType) = LOWER(:layerType)
              AND np.portStatus = :portStatus
              AND np.layerId = :splitterId
              AND np.portType = 'Output'
              AND EXISTS (
                  SELECT 1 FROM Splitter s
                  WHERE s.id = np.layerId AND s.mvnoId = :mvnoId
              )
            """)
    List<NetworkPort> findAvailablePortsBySplitterIdAndMvnoId(
            @Param("splitterId") Integer splitterId,
            @Param("mvnoId") Integer mvnoId,
            @Param("layerType") String layerType,
            @Param("portStatus") String portStatus
    );

    @Query(value = """
            SELECT sdu.id, sdu.name
            FROM ne_sdu sdu
            WHERE sdu.mvno_id = :mvnoId
              AND ST_DWithin(
                    sdu.geom::geography,
                    (
                        SELECT geom::geography
                        FROM ne_splitter
                        WHERE id = :splitterId AND mvno_id = :mvnoId
                    ),
                    :radius
                  )
            """, nativeQuery = true)
    List<Object[]> getNearbySDUsForSplitter(@Param("splitterId") Integer splitterId,
                                            @Param("mvnoId") Integer mvnoId,
                                            @Param("radius") Double radius);

}
