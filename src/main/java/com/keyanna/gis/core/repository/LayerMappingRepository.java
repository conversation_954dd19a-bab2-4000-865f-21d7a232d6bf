package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LayerMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LayerMappingRepository extends JpaRepository<LayerMapping, Integer> {


    @Query(value = """
    SELECT lm.id, lm.name, lm.display_name
    FROM layer_master lm
    WHERE lm.id IN (
        SELECT map.parent_layer_id
        FROM layer_mapping map
        WHERE map.child_layer_id = (
            SELECT id FROM layer_master WHERE code = :code LIMIT 1
        )
    )
    """, nativeQuery = true)
    List<Object[]> findParentLayerRaw(@Param("code") String code);


}
