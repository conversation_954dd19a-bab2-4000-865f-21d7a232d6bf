package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.Olt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FatRepository extends JpaRepository<Fat, Integer>, PublicIdRepository<Fat> {

    Optional<Fat> findByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);


    @Query(value = """
    SELECT * FROM fat f
    LEFT JOIN layer_image_mapping lim ON lim.layer_id = f.id
    WHERE lim.layer_type = :layerType AND f.public_id = :publicId
""", nativeQuery = true)
    Optional<Fat> findFatWithPublicIdAndLayerImageMapping(@Param("publicId") UUID publicId,@Param("layerType") String layerType);

    boolean existsByPublicIdAndMvnoId(UUID publicId,Integer mvnoId);

    void deleteByPublicId(UUID publicId);
    List<Fat> findByParentNeIdAndParentNeType(Integer parentId, String parentNeType);

    List<Fat> findAllByMvnoId(Integer mvnoId);

    @Modifying
    @Query("UPDATE Fat nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Fat e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Fat> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
