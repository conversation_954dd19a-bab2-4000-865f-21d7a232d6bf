package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.SurveyBomVersionMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SurveyBomVersionMappingRepository extends JpaRepository<SurveyBomVersionMapping, Integer> {


    @Modifying
    @Query("UPDATE SurveyBomVersionMapping nf SET nf.status = :status WHERE nf.surveyId = :surveyId")
    void updateStatus(@Param("status") String status, @Param("surveyId") String surveyId);


}
