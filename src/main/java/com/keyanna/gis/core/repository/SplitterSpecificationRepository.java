package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.SplitterSpecification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SplitterSpecificationRepository extends JpaRepository<SplitterSpecification, Integer> {

    List<SplitterSpecification> findByIsActiveTrue();

//    boolean existsById(Integer id);
//
//    void deleteById(Integer id);

}
