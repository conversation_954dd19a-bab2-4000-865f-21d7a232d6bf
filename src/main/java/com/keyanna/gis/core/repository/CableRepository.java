package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.model.Splitter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CableRepository extends JpaRepository<Cable, Integer>, PublicIdRepository<Cable> {

    boolean existsByPublicId(UUID publicId);

    @Override
    Optional<Cable> findByPublicId(UUID publicId);

    boolean existsByCableSpecification_Id(Integer id);

    void deleteByPublicId(UUID publicId);

    // Without geom
    @Query(
            value = "SELECT name, cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, trench_id, duct_id, remarks, created_by, created_on " +
                    "FROM ne_cable " +
                    "WHERE mvno_id = :mvnoId",
            nativeQuery = true
    )
    List<Object[]> getAllCables(@Param("mvnoId") Integer mvnoId);


    // With geom
    @Query(
            value = "SELECT name, cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, trench_id, duct_id, remarks, created_by, created_on, ST_AsGeoJSON(geom) as geom " +
                    "FROM ne_cable " +
                    "WHERE mvno_id = :mvnoId",
            nativeQuery = true
    )
    List<Object[]> getAllCablesWithGeom(@Param("mvnoId") Integer mvnoId);


    List<Olt> findByParentNeId(Integer parentId);


    Optional<Cable> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE Cable nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Cable e WHERE e.surveyAreaId = :surveyAreaId  AND e.mvnoId = :mvnoId")
    List<Cable> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId ,@Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
