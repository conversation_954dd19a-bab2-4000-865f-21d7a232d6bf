package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.model.Street;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface StreetRepository  extends JpaRepository<Street, Integer> ,PublicIdRepository<Street>{


    void deleteByPublicId(UUID publicId);


    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    Optional<Street> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<Street> findByMvnoId(Integer mvnoId);

    @Modifying
    @Query("UPDATE Street nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status, @Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Street e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Street> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
