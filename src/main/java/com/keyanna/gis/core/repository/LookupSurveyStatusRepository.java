package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LookupSurveyStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LookupSurveyStatusRepository extends JpaRepository<LookupSurveyStatus, Integer> {

    Optional<LookupSurveyStatus> findByNameIgnoreCase(String name);

    //Optional<LookupSurveyStatus> findBySurveyId(Integer surveyStatusId);


    List<LookupSurveyStatus> findByIsActiveTrueAndMvnoId(Integer mvnoId);



    Optional<LookupSurveyStatus> findByIdAndMvnoId(Integer id, Integer mvnoId);

    Optional<LookupSurveyStatus> findByMvnoId(Integer mvnoId);

    void deleteByIdAndMvnoId(Integer id, Integer mvnoId);

//    boolean existsById(Integer id);
//    void deleteById(Integer id);

}
