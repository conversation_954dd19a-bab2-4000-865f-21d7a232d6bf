package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Administrative01;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface Administrative01Repository extends JpaRepository<Administrative01,Long> {

    @Query(
            value = ""
                    + "SELECT "
                    + "  a.id, "
                    + "  a.adm1_name, "
                    + "  a.adm1_code, "
                    + "  ST_AsGeoJSON(a.geom) "
                    + "FROM administrative_01 a "
                    + "WHERE a.adm0_code = :adm0Code",
            nativeQuery = true
    )
    List<Object[]> findSelectedFieldsByAdm0Code(
            @Param("adm0Code") String adm0Code
    );
}
