package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.BomMaster;
import com.keyanna.gis.core.model.BomVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface BomVersionRepository extends JpaRepository<BomVersion, Long> {
    @Query("SELECT MAX(v.versionNo) FROM BomVersion v WHERE v.bomMaster.id = :bomMasterId")
    Optional<Integer> findMaxVersionNumberByBomMasterId(@Param("bomMasterId") Long bomMasterId);

    @Modifying
    @Query("UPDATE BomVersion v SET v.isActive = false WHERE v.bomMaster.id = :bomMasterId AND v.versionNo < :versionNo")
    int updateActiveBomVersion(@Param("bomMasterId") Long bomMasterId, @Param("versionNo") Integer versionNo);

    boolean existsByBomMasterAndVersionNo(BomMaster bomMaster, Integer versionNo);

    @Query("SELECT v.id FROM BomVersion v WHERE v.bomMaster.id = :bomMasterId AND v.versionNo = :versionNo")
    int getIdByBomMasterIdAndVersionNo(Long bomMasterId, Integer versionNo);
}
