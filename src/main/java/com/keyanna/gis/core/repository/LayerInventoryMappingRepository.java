package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LayerInventoryMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LayerInventoryMappingRepository extends JpaRepository<LayerInventoryMapping, Integer> {

    void deleteByLayerId(Integer layerId);

    boolean existsByLayerId(Integer layerId);

    void deleteByLayerIdAndLayerCode(Integer layerId, String layerCode);

    List<LayerInventoryMapping> findAllByLayerIdAndLayerCode(Integer layerId, String layerCode);

    @Modifying
    @Query("UPDATE LayerInventoryMapping nf SET nf.status = :status WHERE nf.layerCode = :layerCode AND nf.layerId = :layerId")
    void updateStatus(@Param("status") String status, @Param("layerCode") String layerCode, @Param("layerId") Integer layerId);


    @Query("SELECT l.id FROM LayerInventoryMapping l WHERE l.layerCode = :layerCode AND l.layerId = :layerId")
   List <Integer> getIdByLayerCodeAndLayerId(@Param("layerCode") String layerCode, @Param("layerId") Integer layerId);


}
