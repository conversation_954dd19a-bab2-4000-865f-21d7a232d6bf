package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Pop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PopRepository extends JpaRepository<Pop, Integer>, PublicIdRepository<Pop> {


    @Query(
            value = "SELECT name, address, category, status, created_by, created_on " +
                    "FROM ne_pop " +
                    "WHERE mvno_id = :mvnoId",
            nativeQuery = true
    )
    List<Object[]> getAllPops(@Param("mvnoId") Integer mvnoId);



    @Query(
            value = "SELECT name, address, category, status, created_by, created_on, ST_AsGeoJSON(geom) as geom " +
                    "FROM ne_pop " +
                    "WHERE mvno_id = :mvnoId",
            nativeQuery = true
    )
    List<Object[]> getAllPopsWithGeom(@Param("mvnoId") Integer mvnoId);


    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    Optional<Pop> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE Pop nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Pop e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Pop> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
