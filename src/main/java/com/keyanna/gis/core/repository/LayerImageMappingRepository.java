package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LayerImageMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LayerImageMappingRepository extends JpaRepository<LayerImageMapping, Long> {

    Optional<LayerImageMapping> findByFileName(String filePath);
    List<LayerImageMapping> findAllByLayerIdAndLayerCode(Integer layerId, String layerCode);

}
