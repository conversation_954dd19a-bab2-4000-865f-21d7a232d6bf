package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.AdministrativeUnit;
import com.keyanna.gis.core.model.CableCore;
import com.keyanna.gis.core.model.Handhole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CableCoreRepository extends JpaRepository<CableCore, Integer> {

    List<CableCore> findByCableIdAndCoreStatus(Integer cableId, String coreStatus);

}
