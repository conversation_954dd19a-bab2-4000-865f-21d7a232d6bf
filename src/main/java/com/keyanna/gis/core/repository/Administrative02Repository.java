package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Administrative02;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface Administrative02Repository extends JpaRepository<Administrative02, Long> {

    @Query(
            value = ""
                    + "SELECT "
                    + "  a.id, "
                    + "  a.adm2_name, "
                    + "  a.adm2_code, "
                    + "  ST_AsGeoJSON(a.geom) "
                    + "FROM administrative_02 a "
                    + "WHERE a.adm0_code = :adm0Code",
            nativeQuery = true
    )
    List<Object[]> findSelectedFieldsByAdm0Code(
            @Param("adm0Code") String adm0Code
    );
}
