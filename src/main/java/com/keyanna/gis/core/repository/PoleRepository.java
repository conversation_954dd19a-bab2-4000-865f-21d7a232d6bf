package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.Pole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PoleRepository extends JpaRepository<Pole, Integer>, PublicIdRepository<Pole> {



    void deleteByPublicId(UUID publicId);

    Optional<Pole> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<Pole> findAllByMvnoId(Integer mvnoId);


    @Modifying
    @Query("UPDATE Pole nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Pole e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Pole> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);
    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
