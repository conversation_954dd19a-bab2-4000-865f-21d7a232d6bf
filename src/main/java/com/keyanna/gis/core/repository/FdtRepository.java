package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.model.Fdt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FdtRepository extends JpaRepository<Fdt, Integer>, PublicIdRepository<Fdt> {

    boolean existsByPublicId(UUID publicId);

    List<Olt> findByParentNeId(Integer parentId);

    Optional<Fdt> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<Fdt> findAllByMvnoId(Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);


    @Modifying
    @Query("UPDATE Fdt nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);


    @Query("SELECT e FROM Fdt e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Fdt> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);


    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);

}
