package com.keyanna.gis.core.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class RepositoryRegistry {

    private final Map<String, PublicIdRepository<?>> repositoryMap = new HashMap<>();
    public static final String TABLE_NAME_CUSTOMER = "ne_customer";
    public static final String TABLE_NAME_FAT = "ne_fat";
    public static final String TABLE_NAME_FDT = "ne_fdt";
    public static final String TABLE_NAME_FDP = "ne_fdp";
    public static final String TABLE_NAME_HANDHOLE = "ne_handhole";
    public static final String TABLE_NAME_JOINT_CLOSURE = "ne_joint_closure";
    public static final String TABLE_NAME_MANHOLE = "ne_manhole";
    public static final String TABLE_NAME_POLE = "ne_pole";
    public static final String TABLE_NAME_POP = "ne_pop";
    public static final String TABLE_NAME_SPLITTER = "ne_splitter";
    public static final String TABLE_NAME_CABLE = "ne_cable";
    public static final String TABLE_NAME_DUCT = "ne_duct";
    public static final String TABLE_NAME_TRENCH = "ne_trench";

    @Autowired
    public RepositoryRegistry(
            CustomerRepository customerRepository,
            FatRepository fatRepository,
            FdtRepository fdtLayerRepository,
            FdpRepository fdpRepository,
            HandholeRepository handholeRepository,
            JointClosureRepository jointClosureRepository,
            ManholeRepository manholeRepository,
            PoleRepository poleRepository,
            PopRepository popRepository,
            SplitterRepository splitterRepository,
            CableRepository cableRepository,
            DuctRepository ductRepository,
            TrenchLayerRepository trenchLayerRepository)
    {

        repositoryMap.put(TABLE_NAME_CUSTOMER, customerRepository);
        repositoryMap.put(TABLE_NAME_FAT, fatRepository);
        repositoryMap.put(TABLE_NAME_FDT, fdtLayerRepository);
        repositoryMap.put(TABLE_NAME_FDP, fdpRepository);
        repositoryMap.put(TABLE_NAME_HANDHOLE, handholeRepository);
        repositoryMap.put(TABLE_NAME_JOINT_CLOSURE, jointClosureRepository);
        repositoryMap.put(TABLE_NAME_MANHOLE, manholeRepository);
        repositoryMap.put(TABLE_NAME_POLE, poleRepository);
        repositoryMap.put(TABLE_NAME_POP, popRepository);
        repositoryMap.put(TABLE_NAME_SPLITTER, splitterRepository);

        repositoryMap.put(TABLE_NAME_CABLE, cableRepository);
        repositoryMap.put(TABLE_NAME_DUCT, ductRepository);
        repositoryMap.put(TABLE_NAME_TRENCH, trenchLayerRepository);

        // Add more repositories as needed
    }

    public PublicIdRepository<?> getRepository(String tableName) {
        return repositoryMap.get(tableName);
    }
}
