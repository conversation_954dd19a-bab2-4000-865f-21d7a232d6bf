package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.ClusterMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ClusterMasterRepository extends JpaRepository<ClusterMaster, Integer> {


    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    Optional<ClusterMaster> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);
}
