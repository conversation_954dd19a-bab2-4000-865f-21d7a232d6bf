package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.BomMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface BomMasterRepository extends JpaRepository<BomMaster, Long> {
    Optional<BomMaster> findBySurveyAreaId(Integer surveyAreaId);
    boolean existsBySurveyAreaId(Integer surveyAreaId);
}
