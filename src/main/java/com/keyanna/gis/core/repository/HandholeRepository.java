package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.Handhole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface HandholeRepository extends JpaRepository<Handhole, Integer>, PublicIdRepository<Handhole> {

    boolean existsByPublicId(UUID publicId);

    void deleteByPublicId(UUID publicId);

    Optional<Handhole> findByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    List<Handhole> findByMvnoId(Integer mvnoId);

    void deleteByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    boolean existsByPublicIdAndMvnoId(UUID publicId, Integer mvnoId);

    @Modifying
    @Query("UPDATE Handhole nf SET nf.status = :status WHERE nf.surveyAreaId = :surveyAreaId AND nf.mvnoId = :mvnoId")
    void updateStatus(@Param("surveyAreaId") Integer surveyAreaId, @Param("status") String status ,@Param("mvnoId") Integer mvnoId);

    @Query("SELECT e FROM Handhole e WHERE e.surveyAreaId = :surveyAreaId AND e.mvnoId = :mvnoId")
    List<Handhole> existsForSurveyArea(@Param("surveyAreaId") Integer surveyAreaId , @Param("mvnoId") Integer mvnoId);

    boolean existsBySurveyAreaIdAndMvnoId(Integer surveyAreaId, Integer mvnoId);


}
