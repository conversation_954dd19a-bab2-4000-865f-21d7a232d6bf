package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.StaffUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface StaffUserRepository extends JpaRepository<StaffUser, Integer> {

    @Query("SELECT su FROM StaffUser su WHERE su.isDelete = false ORDER BY su.staffid DESC")
    Page<StaffUser> findAllActive(Pageable pageable);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND ST_Within(su.geom, ST_Buffer(ST_GeomFromText(:wkt, 4326), :radius))",
            nativeQuery = true)
    List<StaffUser> findWithinRadius(@Param("wkt") String wktGeometry,
                                     @Param("radius") double radiusInMeters);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "JOIN tbltstaffservicearearel sa ON su.staffid = sa.staffid " +
            "WHERE su.is_delete = false " +
            "AND sa.serviceareaid = :serviceAreaId " +
            "AND su.status = 'ACTIVE'",
            nativeQuery = true)
    List<StaffUser> findAvailableStaffByServiceArea(@Param("serviceAreaId") Integer serviceAreaId);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND ST_DWithin(su.geom, ST_SetSRID(ST_Point(:longitude, :latitude), 4326), :distance)",
            nativeQuery = true)
    List<StaffUser> findNearbyStaff(@Param("longitude") double longitude,
                                    @Param("latitude") double latitude,
                                    @Param("distance") double distanceInMeters);

    Optional<StaffUser> findByStaffid(Integer staffid);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "JOIN tbltstaffrolerel sr ON su.staffid = sr.staffid " +
            "JOIN tblmroles r ON sr.roleid = r.roleid " +
            "WHERE su.is_delete = false " +
            "AND r.gis_permission_level >= :minPermissionLevel",
            nativeQuery = true)
    List<StaffUser> findByMinimumGisPermission(@Param("minPermissionLevel") int permissionLevel);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND su.field_team = true " +
            "AND su.last_location_update > NOW() - INTERVAL '1 hour'",
            nativeQuery = true)
    List<StaffUser> findActiveFieldTeam();

    @Modifying
    @Transactional
    @Query(value = "UPDATE tblmstaffuser SET geom = ST_SetSRID(ST_Point(:longitude, :latitude), 4326) " +
            "WHERE staffid = :staffId",
            nativeQuery = true)
    void updateStaffLocation(@Param("staffId") Integer staffId,
                             @Param("longitude") double longitude,
                             @Param("latitude") double latitude);

    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND su.MVNOID IN :mvnoIds " +
            "AND (:searchTerm IS NULL OR " +
            "     su.username LIKE %:searchTerm% OR " +
            "     CONCAT(su.firstname, ' ', su.lastname) LIKE %:searchTerm%)",
            countQuery = "SELECT COUNT(*) FROM tblmstaffuser su " +
                    "WHERE su.is_delete = false " +
                    "AND su.MVNOID IN :mvnoIds " +
                    "AND (:searchTerm IS NULL OR " +
                    "     su.username LIKE %:searchTerm% OR " +
                    "     CONCAT(su.firstname, ' ', su.lastname) LIKE %:searchTerm%)",
            nativeQuery = true)
    Page<StaffUser> searchByMvno(@Param("mvnoIds") List<Integer> mvnoIds,
                                 @Param("searchTerm") String searchTerm,
                                 Pageable pageable);

    @Transactional(readOnly = true)
    @Query("SELECT su FROM StaffUser su WHERE su.isDelete = false AND su.lastModifiedDate >= :since")
    List<StaffUser> findRecentlyModified(@Param("since") LocalDateTime since);

    @Transactional(readOnly = true)
    @Query(value = "SELECT su.staffid, su.username, ST_AsText(su.geom) as location " +
            "FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND su.geom IS NOT NULL",
            nativeQuery = true)
    List<Object[]> findStaffLocations();

    @Transactional(readOnly = true)
    @Query(value = "SELECT su.* FROM tblmstaffuser su " +
            "WHERE su.is_delete = false " +
            "AND su.status = 'ACTIVE' " +
            "AND NOT EXISTS (" +
            "    SELECT 1 FROM tblfieldassignments fa " +
            "    WHERE fa.staffid = su.staffid " +
            "    AND fa.status = 'IN_PROGRESS'" +
            ")",
            nativeQuery = true)
    List<StaffUser> findAvailableFieldStaff();
}

