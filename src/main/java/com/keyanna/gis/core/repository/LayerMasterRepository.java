package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.LayerMaster;
import com.keyanna.gis.core.model.Pop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface LayerMasterRepository extends JpaRepository<LayerMaster, Integer> {

    Optional<LayerMaster> findByDisplayName(String displayName);

    List<LayerMaster> findByMvnoId(Integer mvnoId);
}
