package com.keyanna.gis.core.repository;

import com.keyanna.gis.core.model.Administrative00;
import com.keyanna.gis.core.model.Administrative01;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface Administartor00Repository  extends JpaRepository<Administrative00,Long> {


//    @Query(value = "SELECT id, adm1_name, adm1_code, geom FROM survey_area WHERE adm0_code = :adm0Code", nativeQuery = true)
//    List<Object[]> findByAdm0CodeRaw(@Param("adm0Code") String adm0Code);


    /**
     * Returns a list of Object[] where:
     *   row[0] = id (Long)
     *   row[1] = adm0_name (String)
     *   row[2] = adm0_code (String)
     *   row[3] = geom as GeoJSO<PERSON> (String via ST_AsGeoJSON)
     */
    @Query(
            value = ""
                    + "SELECT "
                    + "  a.id, "
                    + "  a.adm0_name, "
                    + "  a.adm0_code, "
                    + "  ST_AsGeoJSON(a.geom) "
                    + "FROM administrative_00 a "
                    + "WHERE a.adm0_code = :adm0Code",
            nativeQuery = true
    )
    List<Object[]> findSelectedFieldsByAdm0Code(
            @Param("adm0Code") String adm0Code
    );
}
