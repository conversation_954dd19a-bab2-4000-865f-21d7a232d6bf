package com.keyanna.gis.core.anotation;

import com.keyanna.gis.core.utility.AllowedValuesValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = AllowedValuesValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AllowedValues {
    String message() default "Invalid value";
    String[] values(); // <-- custom values allowed
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}