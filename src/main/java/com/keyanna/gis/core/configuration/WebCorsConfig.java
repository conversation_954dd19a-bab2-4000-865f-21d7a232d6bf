package com.keyanna.gis.core.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebCorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                //  TODO add your URL
//                .allowedOrigins("http://localhost:4200") // Your Angular app's URL
                .allowedOrigins("*") // Your Angular app's URL
                .allowedMethods("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS")
                .allowedHeaders("*");
//                .allowCredentials(true); // only if you are using cookies/session
    }
}