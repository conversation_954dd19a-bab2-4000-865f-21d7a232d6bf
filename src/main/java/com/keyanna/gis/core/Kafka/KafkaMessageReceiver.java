package com.keyanna.gis.core.Kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.keyanna.gis.core.dto.StaffUserDTO;
import com.keyanna.gis.core.dto.RoleDTO;
import com.keyanna.gis.core.service.impl.StaffUserServiceImpl;
import com.keyanna.gis.core.service.impl.RoleServiceImpl;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class KafkaMessageReceiver {

    @Autowired
    private StaffUserServiceImpl staffUserServiceImpl;

    @Autowired
    private RoleServiceImpl roleServiceImpl;

    private static final Log log = LogFactory.getLog(KafkaMessageReceiver.class);

    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    public void handleMessage(KafkaMessageData message) {
        String eventType = message.getEventType();
        switch (eventType) {
            case "STAFF_CREATED":
            case "STAFF_UPDATED":
                saveStaff(message);
                break;
            case "ROLE_CREATED":
            case "ROLE_UPDATED":
                saveRole(message);
                break;
            default:
                log.warn("Unhandled Kafka event type: " + eventType);
        }
    }

    private void saveStaff(KafkaMessageData message) {
        try {
            StaffUserDTO dto = objectMapper.convertValue(message.getData(), StaffUserDTO.class);
            staffUserServiceImpl.saveFromKafka(dto);
            log.info("STAFF message processed: " + dto.getUsername());
        } catch (Exception e) {
            log.error("Error processing STAFF message", e);
        }
    }

    private void saveRole(KafkaMessageData message) {
        try {
            RoleDTO dto = objectMapper.convertValue(message.getData(), RoleDTO.class);
            roleServiceImpl.saveFromKafka(dto);
            log.info("ROLE_CREATED message processed: " + dto.getRolename());
        } catch (Exception e) {
            log.error("Error processing ROLE_CREATED message", e);
        }
    }
}
