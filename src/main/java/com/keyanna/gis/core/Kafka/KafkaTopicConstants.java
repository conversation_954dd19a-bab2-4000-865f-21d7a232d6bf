package com.keyanna.gis.core.Kafka;

public class KafkaTopicConstants {
    public static final String POP_DESIGN_TOPIC = "pop-events";
    public static final String FDP_DESIGN_TOPIC = "fdp-events";
    public static final String FAT_DESIGN_TOPIC = "fat-events";
    public static final String MDU_DESIGN_TOPIC= "mdu-events";
    public static final String SDU_DESIGN_TOPIC= "sdu-events";
    public static final String CDU_DESIGN_TOPIC = "cdu-events";
    public static final String CUSTOMER_DESIGN_TOPIC = "customer-events";
    public static final String SURVEY_AREA_DESIGN_TOPIC = "survey-area-design-events";
    public static final String POLE_DESIGN_TOPIC = "pole-events";

    // Additional network element topics
    public static final String CABLE_DESIGN_TOPIC = "cable-events";
    public static final String DUCT_DESIGN_TOPIC = "duct-events";
    public static final String FDT_DESIGN_TOPIC = "fdt-events";
    public static final String HANDHOLE_DESIGN_TOPIC = "handhole-events";
    public static final String JOINT_CLOSURE_DESIGN_TOPIC = "joint-closure-events";
    public static final String MANHOLE_DESIGN_TOPIC = "manhole-events";
    public static final String ODF_DESIGN_TOPIC = "odf-events";
    public static final String OLT_DESIGN_TOPIC = "olt-events";
    public static final String SPLITTER_DESIGN_TOPIC = "splitter-events";
    public static final String TRENCH_DESIGN_TOPIC = "trench-events";
}