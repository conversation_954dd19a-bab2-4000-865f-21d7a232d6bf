package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.dto.request.SurveyAreaRequestDTO;
import com.keyanna.gis.core.model.deserialize.PolygonDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPolygonSerializer;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.keyanna.gis.core.model.SurveyArea;
import org.locationtech.jts.geom.Polygon;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurveyAreaKafkaDTO {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("publicId")
    private UUID publicId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;


    private String surveyStatus;


    private String surveyStage;

    @JsonSerialize(using = GeometryPolygonSerializer.class)
    @JsonDeserialize(using = PolygonDeserializer.class)
    @JsonProperty("geom")
    private Polygon geom;


    @JsonProperty("isActive")
    private Boolean isActive = true;

    @JsonProperty("surveyStartDate")
    private String surveyStartDate;

    @JsonProperty("surveyEndDate")
    private String surveyEndDate;

    @JsonProperty("userId")
    private Integer userId;

    @JsonProperty("mvnoId")
    private Integer mvnoId;


    @JsonProperty("createdOn")
    private String createdOn;

    @JsonProperty("modifiedOn")
    private String modifiedOn;

    @Data
    public static class GeometryPolygonDto {

        @NotBlank(message = "Geometry type is required (should be 'Polygon')")
        private String type;

        @NotNull(message = "Coordinates must be provided for the polygon geometry")
        @JsonProperty("coordinates")
        private List<List<List<Double>>> coordinates;  // Polygon coordinates format
    }

    public SurveyAreaKafkaDTO(SurveyArea surveyArea) {
        this.id = surveyArea.getId();
        this.publicId = surveyArea.getPublicId();
        this.name = surveyArea.getName();
        this.description = surveyArea.getDescription();
        this.surveyStatus = surveyArea.getLookupSurveyStatus() != null ?
            surveyArea.getLookupSurveyStatus().getName() : null;
        this.surveyStage = surveyArea.getLookupSurveyStage() != null ?
            surveyArea.getLookupSurveyStage().getName() : null;
        this.isActive = surveyArea.getIsActive();
        this.surveyStartDate = String.valueOf(surveyArea.getSurveyStartDate());
        this.surveyEndDate = String.valueOf(surveyArea.getSurveyEndDate());
        this.userId = Math.toIntExact(surveyArea.getCreatedBy());
        this.mvnoId = surveyArea.getMvnoId();
        this.createdOn = surveyArea.getCreatedOn() != null ?
            java.time.LocalDateTime.ofInstant(surveyArea.getCreatedOn(), java.time.ZoneOffset.UTC)
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        this.modifiedOn = surveyArea.getModifiedOn() != null ?
            java.time.LocalDateTime.ofInstant(surveyArea.getModifiedOn(), java.time.ZoneOffset.UTC)
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        this.geom = surveyArea.getGeom();
    }
}

