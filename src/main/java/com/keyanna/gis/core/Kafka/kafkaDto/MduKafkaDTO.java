package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.model.Mdu;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import com.keyanna.gis.core.model.serialize.GeometryPolygonSerializer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class MduKafkaDTO {


    private Integer id;

    public String name;
    private UUID publicId;
    public String address;
    public Integer homePasses;
    public String status;

    public Integer floors;


    public Integer towers;


    public String category;


    public String tenancy;

    public Integer surveyAreaId;
    public String remarks;
    public String fatNo;
//    public String fdtNo;
    public String opticalLevel;
    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @JsonProperty("geom")
    public Point geom;


    private String streetName;
    private String customId;


    public Integer userId;


    public Integer mvnoId;
//    private String riser;

    private String oltName;


    private String photo;




    // Date fields with proper formatting for Kafka
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;
    public MduKafkaDTO(Mdu mdu) {
        this.id = mdu.getId();
        this.publicId = mdu.getPublicId();
        this.customId = mdu.getCustom_id();
        this.name = mdu.getName();
        this.address = mdu.getAddress();
        this.floors = mdu.getFloors();
        this.towers = mdu.getTowers();
        this.category = mdu.getCategory();
        this.tenancy = mdu.getTenancy();
        this.status = mdu.getStatus();
        this.geom = mdu.getGeom();
        this.homePasses = mdu.getHome_passes();
        this.userId = Math.toIntExact(mdu.getCreatedBy());
        this.mvnoId = mdu.getMvnoId();
        this.surveyAreaId = mdu.getSurveyAreaId();
        this.remarks = mdu.getRemarks();
        this.fatNo = mdu.getFatNo();
        this.opticalLevel = mdu.getOpticalLevel();
        this.streetName = mdu.getStreetName();
        this.createdOn = mdu.getCreatedOn();
        this.modifiedOn = mdu.getModifiedOn();

    }


}
