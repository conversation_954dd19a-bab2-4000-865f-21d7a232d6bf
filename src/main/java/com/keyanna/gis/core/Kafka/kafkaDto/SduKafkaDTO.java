package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.Sdu;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.Column;

import jakarta.persistence.criteria.CriteriaBuilder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@NoArgsConstructor
@Data
public class SduKafkaDTO {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("publicId")
    private UUID publicId;

    @JsonProperty("custom_id")
    private String custom_id;

    @JsonProperty("name")
    public String name;

    @JsonProperty("address")
    public String address;

    @JsonProperty("homePasses")
    public Integer homePasses;

    @JsonProperty("floors")
    public Integer floors;

    @JsonProperty("towers")
    public Integer towers;

    @JsonProperty("tenancy")
    public String tenancy;

    @JsonProperty("category")
    public String category ;

    @JsonProperty("status")
    public String status;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @JsonProperty("userId")
    private Integer userId;

    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("fatNo")
    private String fatNo;

    @JsonProperty("fdtNo")
    private String fdtNo;

    @JsonProperty("opticalLevel")
    private String opticalLevel;

    @JsonProperty("photo")
    private String photo;

    @JsonProperty("streetName")
    private String streetName;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;
    public SduKafkaDTO(Sdu sdu) {
        this.id = sdu.getId();
        this.name = sdu.getName();
        this.publicId = sdu.getPublicId();
        this.address = sdu.getAddress();
        this.homePasses = sdu.getHome_passes();
        this.status = sdu.getStatus();
        this.towers = sdu.getTowers();
        this.floors = sdu.getFloors();
        this.category = sdu.getCategory();
        this.tenancy = sdu.getTenancy();
        this.userId = Math.toIntExact(sdu.getCreatedBy());
        this.mvnoId = sdu.getMvnoId();
        this.surveyAreaId = sdu.getSurveyAreaId();
        this.remarks = sdu.getRemarks();
        this.fatNo = sdu.getFatNo();
        this.fdtNo = sdu.getFdtNo();
        this.opticalLevel = sdu.getOpticalLevel();
        this.streetName = sdu.getStreetName();
        this.createdOn = sdu.getCreatedOn();
        this.modifiedOn = sdu.getModifiedOn();
        this.geom = sdu.getGeom();
    }

    //Note : This will use in future so do not delete below commented code.
//    @JsonProperty(ApiConstants.LABEL_INVENTORY_LIST)
//    private List<Inventory> inventoryList;
}
