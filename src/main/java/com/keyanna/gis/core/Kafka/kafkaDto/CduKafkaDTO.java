package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.Cdu;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;

import java.util.UUID;

@Data
@NoArgsConstructor
public class    CduKafkaDTO {

    private Integer id;

    private UUID publicId;

    private String custom_id;


    public String name;


    public String address;


    public Integer homePasses;


    public Integer floors;


    public Integer towers;


    public String tenancy;


    public String category;

    public String status;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @JsonProperty("geom")
    public Point geom;

    private Integer userId;
    private Integer mvnoId;

    private Integer surveyAreaId;

    private String remarks;
    private String fatNo;


    private String fdtNo;


    private String opticalLevel;

    private String photo;


    private String streetName;

    // Date fields with proper formatting for Kafka
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;
    public CduKafkaDTO(Cdu cdu) {
        this.id = cdu.getId();
        this.publicId = cdu.getPublicId();
        this.name = cdu.getName();
        this.address = cdu.getAddress();
        this.floors = cdu.getFloors();
        this.towers = cdu.getTowers();
        this.tenancy = cdu.getTenancy();
        this.category = cdu.getCategory();
        this.status = cdu.getStatus();
        this.geom = cdu.getGeom();
        this.userId = Math.toIntExact(cdu.getCreatedBy());
        this.mvnoId = cdu.getMvnoId();
        this.surveyAreaId = cdu.getSurveyAreaId();
        this.remarks = cdu.getRemarks();
        this.fatNo = cdu.getFatNo();
        this.fdtNo = cdu.getFdtNo();
        this.opticalLevel = cdu.getOpticalLevel();
        this.streetName = cdu.getStreetName();
        this.createdOn = cdu.getCreatedOn();
        this.modifiedOn = cdu.getModifiedOn();
    }

}
