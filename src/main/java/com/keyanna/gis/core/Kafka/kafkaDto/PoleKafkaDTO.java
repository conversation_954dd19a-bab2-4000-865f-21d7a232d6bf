package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.Pole;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class PoleKafkaDTO {



    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("publicId")
    private UUID publicId;

    @JsonProperty("customId")
    private String customId;

    @JsonProperty("heightM")
    private BigDecimal heightM;

    @JsonProperty("material")
    private String material;

    @JsonProperty("poleType")
    private String poleType;

    @JsonProperty("ownership")
    private String ownership;

    @JsonProperty("status")
    private String status;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @JsonProperty("userId")
    private Integer userId;

    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("adss")
    private String adss;

    @JsonProperty("upb")
    private String upb;

    @JsonProperty("jHook")
    private String jHook;

    @JsonProperty("anchor")
    private String anchor;

    @JsonProperty("cable")
    private String cable;

    @JsonProperty("slack")
    private String slack;

    @JsonProperty("jb")
    private String jb;

    @JsonProperty("fat")
    private String fat;

    @JsonProperty("guyGrip")
    private String guyGrip;

    @JsonProperty("photo")
    private String photo;

    @JsonProperty("photo2")
    private String photo2;

    @JsonProperty("photo3")
    private String photo3;

    @JsonProperty("poleSizeType")
    private String poleSizeType;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;
    public PoleKafkaDTO(Pole pole) {
        this.id = pole.getId();
        this.publicId = pole.getPublicId();
        this.customId = pole.getCustomId();
        this.name = pole.getName();
        this.heightM = pole.getHeightM();
        this.status = pole.getStatus();
        this.mvnoId = pole.getMvnoId();
        this.userId = Math.toIntExact(pole.getCreatedBy());
        this.surveyAreaId = pole.getSurveyAreaId();
        this.remarks = pole.getRemarks();
        this.adss = pole.getAdss();
        this.upb = pole.getUpb();
        this.jHook = pole.getJHook();
        this.anchor = pole.getAnchor();
        this.cable = pole.getCable();
        this.slack = pole.getSlack();
        this.jb = pole.getJb();
        this.fat = pole.getFat();
        this.guyGrip = pole.getGuyGrip();
        this.photo = pole.getPhoto();
        this.photo2 = pole.getPhoto2();
        this.photo3 = pole.getPhoto3();
        this.poleSizeType = pole.getPoleSizeType();
        this.createdOn = pole.getCreatedOn();
        this.modifiedOn = pole.getModifiedOn();
        this.geom = pole.getGeom();

//        if (this.geom != null) {
//            this.longitude = geom.getX();
//            this.latitude = geom.getY();
//        }
    }



}
