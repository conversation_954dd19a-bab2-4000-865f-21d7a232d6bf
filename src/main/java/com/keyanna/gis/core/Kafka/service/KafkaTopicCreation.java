
package com.keyanna.gis.core.Kafka.service;

import com.keyanna.gis.core.Kafka.KafkaTopicConstants;
import jakarta.annotation.PostConstruct;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.config.TopicConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Component;


@Component
public class KafkaTopicCreation {

    @Value("${topicPartition:3}")
    private String topicPartition;

    @Value("${replicationFactor:1}")
    private String replicationFactor;

    @Autowired
    private KafkaAdmin adminClient;

    @PostConstruct
    public void init() {
        createKafkaTopics();
    }

    /**
     * Creates a single Kafka topic with specified configuration
     *
     * @param topic The topic name to create
     */
    public void createTopic(String topic) {
        NewTopic newTopic = TopicBuilder.name(topic)
                .partitions(Integer.parseInt(topicPartition))
                .replicas(Integer.parseInt(replicationFactor))
                .config(TopicConfig.CLEANUP_POLICY_CONFIG, TopicConfig.CLEANUP_POLICY_COMPACT)
                .build();

        adminClient.createOrModifyTopics(newTopic);
        System.out.println("*************Kafka Topic Created : " + topic + " ****************");
    }

    /**
     * Creates all required Kafka topics for GIS Core network infrastructure events
     */
    public void createKafkaTopics() {
        createTopic(KafkaTopicConstants.POP_DESIGN_TOPIC); // POP
        createTopic(KafkaTopicConstants.FDP_DESIGN_TOPIC); // FDP
        createTopic(KafkaTopicConstants.FAT_DESIGN_TOPIC); // FAT
        createTopic(KafkaTopicConstants.MDU_DESIGN_TOPIC); // MDU
        createTopic(KafkaTopicConstants.SDU_DESIGN_TOPIC); // SDU
        createTopic(KafkaTopicConstants.CDU_DESIGN_TOPIC); // CDU
        createTopic(KafkaTopicConstants.CUSTOMER_DESIGN_TOPIC); // CUSTOMER
        createTopic(KafkaTopicConstants.SURVEY_AREA_DESIGN_TOPIC); // SURVEY AREA DESIGN STAGE

    }
}

