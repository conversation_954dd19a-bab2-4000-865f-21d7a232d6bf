package com.keyanna.gis.core.Kafka.kafkaDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.deserialize.PolygonDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import com.keyanna.gis.core.model.serialize.GeometryPolygonSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;
import org.springframework.cglib.core.Local;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class FatKafkaDTO {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("publicId")
    private UUID publicId;

    @JsonProperty("custom_id")
    private String custom_id;

    @JsonProperty("name")
    public String name;

    @JsonProperty("capacity")
    public Integer capacity;

    @JsonProperty("address")
    public String address;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @JsonProperty("geom")
    private Point geom;

    @JsonProperty("status")
    public String status;

    @JsonProperty("parentNeId")
    public Integer parentNeId;

    @JsonProperty("parentNeType")
    public String parentNeType;

    @JsonProperty("userId")
    private Integer userId;

    @JsonProperty("mvnoId")
    private Integer mvnoId;

    @JsonProperty("remarks")
    public String remarks;

    @JsonProperty("powerLevels")
    public BigDecimal powerLevels;

    @JsonProperty("surveyAreaId")
    private Integer surveyAreaId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedOn;
    public FatKafkaDTO(Fat fat) {
        this.id = fat.getId();
        this.publicId = fat.getPublicId();
        this.custom_id = fat.getCustom_id();
        this.name = fat.getName();
        this.capacity = fat.getCapacity();
        this.address = fat.getAddress();
        this.status = fat.getStatus();
        this.parentNeId = fat.getParentNeId();
        this.parentNeType = fat.getParentNeType();
        this.userId = Math.toIntExact(fat.getCreatedBy());
        this.mvnoId = fat.getMvnoId();
        this.remarks = fat.getRemarks();
        this.powerLevels = fat.getPowerLevels();
        this.surveyAreaId = fat.getSurveyAreaId();
        this.createdOn = fat.getCreatedOn();
        this.modifiedOn = fat.getModifiedOn();
        this.geom = fat.getGeom();
//        if (fat.getGeom() != null) {
//            this.longitude = fat.getGeom().getX();
//            this.latitude = fat.getGeom().getY();
//        }
    }
}
