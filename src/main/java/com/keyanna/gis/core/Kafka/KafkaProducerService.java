package com.keyanna.gis.core.Kafka;

import com.keyanna.gis.core.Kafka.kafkaDto.SurveyAreaKafkaDTO;
import com.keyanna.gis.core.model.SurveyArea;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
public class KafkaProducerService {

    @Autowired
    private KafkaTemplate<String, KafkaMessageData> kafkaTemplate;
    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerService.class);

    public void sendSurveyDesignData(SurveyArea surveyArea, String eventType) {
        try {
            // Create DTO from SurveyArea entity
            SurveyAreaKafkaDTO surveyAreaDto = new SurveyAreaKafkaDTO(surveyArea);
            KafkaMessageData message = new KafkaMessageData(surveyAreaDto, "SURVEY_AREA", eventType);
            kafkaTemplate.send(KafkaTopicConstants.SURVEY_AREA_DESIGN_TOPIC,
                    KafkaTopicConstants.SURVEY_AREA_DESIGN_TOPIC + "-key",
                    message);
            logger.info("Successfully sent survey area {} to Kafka topic {}",
                    surveyArea.getPublicId(),
                    KafkaTopicConstants.SURVEY_AREA_DESIGN_TOPIC);
        } catch (Exception e) {
            System.err.println("Error sending survey area to Kafka: " + e.getMessage());
        }
    }

    public void sendNetworkElementData(Object data, String dataType, String eventType) {
        try {
            KafkaMessageData message = new KafkaMessageData(data, dataType, eventType);

            String topic = getTopicByDataType(dataType);

            // Use topic + "-key" pattern instead of dynamic key
            String messageKey = topic + "-key";

            logger.info("Attempting to send {} data to Kafka topic: {}, eventType: {}, key: {}",
                    dataType, topic, eventType, messageKey);

            kafkaTemplate.send(topic, messageKey, message)
                    .whenComplete((result, failure) -> {
                        if (failure != null) {
                            logger.error("Failed to send {} data to Kafka topic {} with key {}: {}",
                                    dataType, topic, messageKey, failure.getMessage(), failure);
                        } else {
                            logger.info("Successfully sent {} data to Kafka topic {} with key {} and offset: {}",
                                    dataType, topic, messageKey, result.getRecordMetadata().offset());
                        }
                    });

        } catch (Exception e) {
            logger.error("Error sending {} data to Kafka: {}", dataType, e.getMessage(), e);
        }
    }

    private String getTopicByDataType(String dataType) {
        return switch (dataType) {
            case "CDU" -> KafkaTopicConstants.CDU_DESIGN_TOPIC;
            case "POLE" -> KafkaTopicConstants.POLE_DESIGN_TOPIC;
            case "MDU" -> KafkaTopicConstants.MDU_DESIGN_TOPIC;
            case "SDU" -> KafkaTopicConstants.SDU_DESIGN_TOPIC;
            case "FDP" -> KafkaTopicConstants.FDP_DESIGN_TOPIC;
            case "FAT" -> KafkaTopicConstants.FAT_DESIGN_TOPIC;
            case "POP" -> KafkaTopicConstants.POP_DESIGN_TOPIC;
            case "CUSTOMER" -> KafkaTopicConstants.CUSTOMER_DESIGN_TOPIC;
            case "CABLE" -> KafkaTopicConstants.CABLE_DESIGN_TOPIC;
            case "DUCT" -> KafkaTopicConstants.DUCT_DESIGN_TOPIC;
            case "FDT" -> KafkaTopicConstants.FDT_DESIGN_TOPIC;
            case "HANDHOLE" -> KafkaTopicConstants.HANDHOLE_DESIGN_TOPIC;
            case "JOINT_CLOSURE" -> KafkaTopicConstants.JOINT_CLOSURE_DESIGN_TOPIC;
            case "MANHOLE" -> KafkaTopicConstants.MANHOLE_DESIGN_TOPIC;
            case "ODF" -> KafkaTopicConstants.ODF_DESIGN_TOPIC;
            case "OLT" -> KafkaTopicConstants.OLT_DESIGN_TOPIC;
            case "SPLITTER" -> KafkaTopicConstants.SPLITTER_DESIGN_TOPIC;
            case "TRENCH" -> KafkaTopicConstants.TRENCH_DESIGN_TOPIC;
            default -> {
                logger.error("Unknown dataType: {}. No topic mapping found.", dataType);
                throw new IllegalArgumentException("Unsupported dataType: " + dataType + ". Please add topic mapping for this network element type.");
            }
        };
    }
}