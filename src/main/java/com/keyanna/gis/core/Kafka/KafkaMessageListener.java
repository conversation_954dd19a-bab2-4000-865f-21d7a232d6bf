package com.keyanna.gis.core.Kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.keyanna.gis.core.dto.RoleDTO;
import com.keyanna.gis.core.dto.StaffUserDTO;
import com.keyanna.gis.core.service.impl.RoleServiceImpl;
import com.keyanna.gis.core.service.impl.StaffUserServiceImpl;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class KafkaMessageListener {

    private final KafkaMessageReceiver kafkaMessageReceiver; // << inject your receiver

    @KafkaListener(topics = "staff-topic", groupId = "gis-staff-group")
    public void handleStaffMessage(KafkaMessageData message) {
        kafkaMessageReceiver.handleMessage(message); // << delegate to receiver
    }

    @KafkaListener(topics = "role-topic", groupId = "gis-role-group")
    public void handleRoleMessage(KafkaMessageData message) {
        kafkaMessageReceiver.handleMessage(message); // << reuse same logic
    }
}