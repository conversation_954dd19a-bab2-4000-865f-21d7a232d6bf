package com.keyanna.gis.core.enumfolder;

public enum CoreColor {

    BL<PERSON>("Blue"),
    ORANGE("Orange"),
    GREEN("Green"),
    BROWN("<PERSON>"),
    GRAY("Gray"),
    WHITE("White"),
    RED("Red"),
    BLACK("Black"),
    YELLOW("Yellow"),
    VIOLET("Violet"),
    PINK("Pink"),
    LIGHT_BLUE("Light Blue");

    private final String displayName;

    CoreColor(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static CoreColor getByIndex(int index) {
        CoreColor[] values = CoreColor.values();
        return values[index % values.length];  // Cycle if cores > colors
    }

}
