package com.keyanna.gis.core.config;

import com.keyanna.gis.core.Kafka.KafkaMessageData;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka Configuration for GIS Core
 *
 * This configuration provides the KafkaAdmin bean required for topic creation
 * and management operations.
 */
@Configuration
public class KafkaConfig {

    @Value(value = "${kafka-url}")
    private String kafkaUrl;

    /**
     * Creates KafkaAdmin bean for topic management operations
     * Used by KafkaTopicCreation service to create topics automatically
     *
     * @return KafkaAdmin configured with bootstrap servers
     */
    @Bean
    public KafkaAdmin adminClient() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaUrl);
        return new KafkaAdmin(configs);
    }

    /**
     * Creates ProducerFactory for Kafka message publishing
     * Configures serializers for String keys and JSON values
     *
     * @return ProducerFactory configured for network infrastructure events
     */
    @Bean
    public ProducerFactory<String, KafkaMessageData> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaUrl);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);

        // Producer performance and reliability settings
        configProps.put(ProducerConfig.ACKS_CONFIG, "all"); // Wait for all replicas to acknowledge
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3); // Retry failed sends
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384); // Batch size for efficiency
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 1); // Wait time for batching
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432); // Buffer memory

        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * Creates KafkaTemplate for sending messages to Kafka topics
     * Used by services to publish network infrastructure events
     *
     * @return KafkaTemplate configured for network infrastructure events
     */
    @Bean
    public KafkaTemplate<String, KafkaMessageData> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
