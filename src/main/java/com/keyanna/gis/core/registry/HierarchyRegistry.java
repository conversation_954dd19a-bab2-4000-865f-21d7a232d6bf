package com.keyanna.gis.core.registry;

import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.repository.OltRepository;
import com.keyanna.gis.core.service.HierarchyBuilderService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class HierarchyRegistry {

    private final ConcurrentHashMap<Integer, Object> oltHierarchyMap = new ConcurrentHashMap<>();

    @Autowired
    private HierarchyBuilderService hierarchyBuilderService;

    @Autowired
    private OltRepository oltRepository;

    public Object findById(Integer id) {
        return oltHierarchyMap.get(id);
    }
    //making tree
    @PostConstruct
    public void makeHierarchy() {
        List<Olt> oltList = oltRepository.findAll();
        for(Olt olt:oltList){
            oltHierarchyMap.put(olt.getId(),hierarchyBuilderService.buildHierarchy(olt));
        }
    }
}
