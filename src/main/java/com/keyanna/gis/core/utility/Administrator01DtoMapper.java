package com.keyanna.gis.core.utility;

import com.keyanna.gis.core.dto.response.Administrator01DTO;

import java.util.List;
import java.util.stream.Collectors;

public class Administrator01DtoMapper {

    public static Administrator01DTO map(Object[] row) {
        Administrator01DTO dto = new Administrator01DTO();
        dto.setId(((Number)row[0]).longValue());
        dto.setAdm1Name((String)row[1]);
        dto.setAdm1Code((String)row[2]);

        // ▶︎ Directly assign the GeoJSON returned by ST_AsGeoJSON()
        dto.setGeom((String)row[3]);
        return dto;
    }

    /**
     * Map a list of rows into a list of DTOs.
     */
    public static List<Administrator01DTO> mapList(List<Object[]> rows) {
        return rows.stream()
                .map(Administrator01DtoMapper::map)
                .collect(Collectors.toList());
    }

}
