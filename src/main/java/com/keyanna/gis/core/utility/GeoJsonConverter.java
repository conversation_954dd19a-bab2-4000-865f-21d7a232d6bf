package com.keyanna.gis.core.utility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Cable;
import org.locationtech.jts.geom.Geometry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GeoJsonConverter {

    private static final Logger logger = LoggerFactory.getLogger(GeoJsonConverter.class);
//    private static final GeometryJSON geometryJSON = new GeometryJSON(10);

//    public static Map<String, Object> mapToGeoJSONFromDirectGeom(List<Cable> cableList, ObjectMapper objectMapper) {
//        try {
//            if (cableList == null) {
//                throw new IllegalArgumentException("Cable list is null. Cannot generate GeoJSON.");
//            }
//
//            if (cableList.isEmpty()) {
//                throw new EntityNotFoundException("No cables found for the provided criteria.");
//            }
//            Map<String, Object> geoJson = new HashMap<>();
//            geoJson.put("type", "FeatureCollection");
//
//            List<Map<String, Object>> featureList = new ArrayList<>();
//
//            Map<String, Object> feature = new HashMap<>();
//            for (Cable cable : cableList) {
//                feature = new HashMap<>();
//                feature.put("type", "Feature");
//
//                // Properties
//                Map<String, Object> properties = new HashMap<>();
//                //            properties.put("id", poi.getId());
//                feature.put("properties", properties);
//
//                // Geometry
//                // Convert GeoJSON string to a JSON Object
//                String geoJsonString = geometryToGeoJson(cable.getGeom());
//                Map<String, Object> geometry = objectMapper.readValue(geoJsonString, Map.class);
//
//                feature.put("geometry", geometry);
//                featureList.add(feature);
//            }
//
//            geoJson.put("features", featureList);
//
//            return geoJson;
//        } catch (JsonProcessingException e) {
//            logger.error("Error parsing GeoJSON for cable geometry", e);
//            throw new RuntimeException("Failed to parse GeoJSON geometry: " + e.getMessage(), e);
//        } catch (Exception e) {
//            logger.error("Unexpected error while converting to GeoJSON", e);
//            throw new RuntimeException("Unexpected error during GeoJSON conversion: " + e.getMessage(), e);
//        }
//    }

//    public static String geometryToGeoJson(Geometry geometry) {
//        if (geometry == null) return null;
//        StringWriter writer = new StringWriter();
//        try {
//            geometryJSON.write(geometry, writer);
//            return writer.toString();
//        } catch (IOException e) {
//            throw new RuntimeException("Failed to convert geometry to GeoJSON", e);
//        }
//    }
}
