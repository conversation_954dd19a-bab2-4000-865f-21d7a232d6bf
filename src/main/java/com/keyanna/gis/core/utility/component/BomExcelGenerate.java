package com.keyanna.gis.core.utility.component;

import com.keyanna.gis.core.dto.ExcelResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.BomMaster;
import com.keyanna.gis.core.model.BomVersion;
import com.keyanna.gis.core.model.SurveyArea;
import com.keyanna.gis.core.model.SurveyBomVersionMapping;
import com.keyanna.gis.core.repository.*;
import com.keyanna.gis.core.service.CommonService;
import com.keyanna.gis.core.service.SurveyBomVersionMappingService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

@Component
public class BomExcelGenerate {

    private static final Logger logger = LoggerFactory.getLogger(BomExcelGenerate.class);
    private final BomMasterRepository bomMasterRepository;
    private final BomVersionRepository bomVersionRepository;
    private final String bomDataDirectory;
    private final SurveyBomVersionMappingService surveyBomVersionMappingService;
    private  final SurveyAreaRepository  surveyAreaRepository;
    int totalElements = 0;
    int totalColIndex = 0;

    public BomExcelGenerate(BomMasterRepository bomMasterRepository,
                            BomVersionRepository bomVersionRepository,
                            @Value("${bom.data.directory:src/main/resources/bom_data}") String bomDataDirectory,SurveyBomVersionMappingService surveyBomVersionMappingService, SurveyAreaRepository surveyAreaRepository) {
        this.bomMasterRepository = bomMasterRepository;
        this.bomVersionRepository = bomVersionRepository;
        this.bomDataDirectory = bomDataDirectory;
        this.surveyBomVersionMappingService = surveyBomVersionMappingService;
        this.surveyAreaRepository = surveyAreaRepository;
    }

//    @Transactional
    public CommonService.ExcelExportResult generateExcel(ExcelResponseDTO request, Integer areaId, Long userId) throws IOException {
        // 1. Generate Excel content
        byte[] excelContent;

        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // Generate Excel content
            generateExcelContent(workbook, request);
            workbook.write(out);
            excelContent = out.toByteArray();
        }

        // 2. Save file and track version
        String savedFileName = saveExcelAndTrackVersion(excelContent, areaId, userId);

        return new CommonService.ExcelExportResult(excelContent, savedFileName);
    }

    @Transactional
    public String saveExcelAndTrackVersion(byte[] excelContent, Integer areaId, Long userId) throws IOException {
        // 0. Create BOM data directory if not exists
        createBomDataDirectoryIfNotExists();
           Integer mvnoId = surveyAreaRepository.findById(areaId)
                   .filter(surveyArea -> surveyArea.getMvnoId() != null)
                   .map(SurveyArea::getMvnoId).orElseThrow(() -> new IllegalStateException("MvnoId for SurveyArea not found"));
        // 1. Find BomMaster
        BomMaster bomMaster = bomMasterRepository.findBySurveyAreaId(areaId)
                .orElseGet(() -> {
                    // Create new BomMaster for this survey area
                    BomMaster newBomMaster = new BomMaster();
                    newBomMaster.setSurveyAreaId(areaId);
                    newBomMaster.setStatus(BomMaster.Status.Active);
                    newBomMaster.setCreatedBy(userId);
                    newBomMaster.setCreatedOn(LocalDateTime.now());
                    newBomMaster.setMvnoId(mvnoId);
                    return newBomMaster;
                });

        // 2. Calculate next version number
        int nextVersion = bomVersionRepository.findMaxVersionNumberByBomMasterId(bomMaster.getId())
                .map(version -> version + 1)
                .orElse(1);

        // 3. Generate filename with version and set it in BomMaster
        String fileName = String.format("BOM_A_%d_V_%d.xlsx", areaId, nextVersion);
        Path filePath = Paths.get(bomDataDirectory, fileName);
        bomMaster.setName(fileName);
        bomMasterRepository.save(bomMaster);

        // 4. Save file to disk
        Files.write(filePath, excelContent);

        // 5. Create and save BomVersion
        BomVersion bomVersion = new BomVersion();
        bomVersion.setBomMaster(bomMaster);
        bomVersion.setVersionNo(nextVersion);
        bomVersion.setFileName(fileName);
        bomVersion.setFilePath(filePath.toString());
        bomVersion.setCreatedBy(userId);
        bomVersion.setCreatedOn(LocalDateTime.now());
        bomVersion.setIsActive(true);
        bomVersion.setMvnoId(mvnoId);
        bomVersionRepository.save(bomVersion);
        bomVersionRepository.flush(); // need to flush to get generated ID

        bomVersionRepository.updateActiveBomVersion(bomMaster.getId(), nextVersion);
            Integer bomVersionId = bomVersionRepository.getIdByBomMasterIdAndVersionNo(bomMaster.getId(), nextVersion);
          surveyBomVersionMappingService.insertToSurveyBomVersionMapping(areaId, bomVersionId,  userId.toString(),mvnoId);
        logger.info("Saved BOM version {} for area {} to {}", nextVersion, areaId, filePath);
        return fileName;
    }

    private void createBomDataDirectoryIfNotExists() {
        try {
            Path path = Paths.get(bomDataDirectory);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                logger.info("Created BOM data directory: {}", path.toAbsolutePath());
            }
        } catch (IOException e) {
            logger.error("Failed to create BOM data directory: {}", bomDataDirectory, e);
            throw new RuntimeException("Failed to create BOM data directory", e);
        }
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    private CellStyle createSectionHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        return style;
    }

    private CellStyle createAccessoryHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setItalic(true);
        font.setBold(true);
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        return style;
    }

    private CellStyle createAccessoryStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setItalic(true);
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setIndention((short) 1); // Add indentation
        return style;
    }

    private void generateExcelContent(Workbook workbook, ExcelResponseDTO request) {
        Sheet sheet = workbook.createSheet("Network Elements");

        // Create styles
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle sectionHeaderStyle = createSectionHeaderStyle(workbook);

        int rowNum = 0;
        totalElements = 0;
        totalColIndex = 0;

        String title = "Bill of Material (BOM) Report";
        rowNum = addTitleToSheet(sheet, rowNum, title);

        // Process each section with their specific column configurations
        rowNum = addSectionToSheet(sheet, "POP / Point of Presence", request.getPop(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Address", "Category"));

        rowNum = addSectionToSheet(sheet, "FDP / Fiber Distribution Point", request.getFdp(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Port", "FDP Type Name",
                        "FDP Type Description"));

        rowNum = addSectionToSheet(sheet, "FDT / Fiber Distribution Terminal", request.getFdt(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Address", "Capacity"));

        rowNum = addSectionToSheet(sheet, "FAT / Fiber Access Terminal", request.getFat(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Address", "Capacity", "Power Levels"));

        rowNum = addSectionToSheet(sheet, "Cable", request.getCable(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Cable Type Name",
                        "Cable Type Description", "Mounting Type", "Cable Specification Name",
                        "No. of Cores", "Cable Specification Description", "GIS Length (m)"));

        rowNum = addSectionToSheet(sheet, "Splitter", request.getSplitter(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Splitter Specification Name",
                        "Splitter Specification Port Ratio", "Splitter Specification Description"));

        rowNum = addSectionToSheet(sheet, "Customer", request.getCustomer(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Address", "Port", "Customer Type"));

        rowNum = addSectionToSheet(sheet, "Pole", request.getPole(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Pole Type Name", "Height (m)", "Material"));

        rowNum = addSectionToSheet(sheet, "Handhole", request.getHandhole(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Hole Size", "Access Type", "Material"));

        rowNum = addSectionToSheet(sheet, "Joint Closure", request.getJointClosure(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Joint Type", "Capacity"));

        rowNum = addSectionToSheet(sheet, "Manhole", request.getManhole(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Manhole Size", "Depth (cm)", "Cover Type"));

        rowNum = addSectionToSheet(sheet, "Duct", request.getDuct(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Length (m)", "Network Type"));

        rowNum = addSectionToSheet(sheet, "Trench", request.getTrench(),
                rowNum, headerStyle, sectionHeaderStyle,
                Arrays.asList("Name", "Width (m)", "Depth (m)", "Length (m)"));

        rowNum++;

        Row row = sheet.createRow(rowNum);
        setCellValue(row, totalColIndex, String.format("Total: %d", totalElements));

        // Highlight total
        Workbook workbook1 = row.getSheet().getWorkbook();
        CellStyle style = workbook1.createCellStyle();
        style.cloneStyleFrom(row.getCell(totalColIndex).getCellStyle());
        Font font = workbook1.createFont();
        font.setBold(true);
        style.setFont(font);
        row.getCell(totalColIndex).setCellStyle(style);

        // Auto-size all columns
        for (int i = 0; i < 20; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private int addTitleToSheet(Sheet sheet, int rowNum, String title) {
        // Title row
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(title);
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 13);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.TOP);
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));

        // MetaData row after one row gap
        rowNum++; // Add one row gap

        rowNum = addMetaDataToSheet(sheet, rowNum, "Project Name",
                "FTTX Lagos", titleStyle);
        rowNum = addMetaDataToSheet(sheet, rowNum, "Plan Name",
                "Lekki Phase 1", titleStyle);
        rowNum = addMetaDataToSheet(sheet, rowNum, "Date",
                new SimpleDateFormat("d-M-yy").format(new Date()), titleStyle);

        // Add one more empty row before sections start
        rowNum++;
        return rowNum;
    }

    private int addMetaDataToSheet(Sheet sheet, int rowNum, String infoField, String infoValue, CellStyle titleStyle) {
        Row dateRow = sheet.createRow(rowNum++);

        // Date label
        Cell dateLabelCell = dateRow.createCell(0);
        dateLabelCell.setCellValue(infoField);
        dateLabelCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 1));

        // Current date
        Cell dateValueCell = dateRow.createCell(2);
        dateValueCell.setCellValue(infoValue);
        return rowNum;
    }

    private int addSectionToSheet(Sheet sheet, String sectionName, List<ExcelResponseDTO.CommonFieldsDTO> elements,
                                  int startRow, CellStyle headerStyle, CellStyle sectionHeaderStyle,
                                  List<String> visibleColumns) {
        if (elements == null || elements.isEmpty()) {
            return startRow;
        }

        int rowNum = startRow;

        rowNum++; // Add empty row before new section

        // Create section header row
        Row sectionHeaderRow = sheet.createRow(rowNum++);
        Cell sectionCell = sectionHeaderRow.createCell(0);
        sectionCell.setCellValue(sectionName);
        sectionCell.setCellStyle(sectionHeaderStyle);

        // Merge cells for section header
        sheet.addMergedRegion(new CellRangeAddress(
                rowNum - 1,     // first row (0-based)
                rowNum - 1,              // last row (same as first row)
                0,                       // first column (0-based)
                visibleColumns.size()    // last column (0-based)
        ));

        // Create a mapping of column names to their positions and getters
        Map<String, BiConsumer<Row, ExcelResponseDTO.CommonFieldsDTO>> columnHandlers = createColumnHandlers(visibleColumns);

        // Add column headers
        Row headerRow = sheet.createRow(rowNum++);
        int colIndex = 0;

        // Add other headers based on visible columns
        for (String column : visibleColumns) {
            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(column);
            cell.setCellStyle(headerStyle);
        }

        Cell cell = headerRow.createCell(colIndex);
        cell.setCellValue("Material Count");
        cell.setCellStyle(headerStyle);

        // Add data rows
        int itemNumber = 1;
        for (ExcelResponseDTO.CommonFieldsDTO element : elements) {
            Row row = sheet.createRow(rowNum++);

            // Add data for visible columns
            for (String column : visibleColumns) {
                if (columnHandlers.containsKey(column)) {
                    columnHandlers.get(column).accept(row, element);
                }
            }

            // Material Count
            setNumericCellValue(row, visibleColumns.size(), itemNumber++);

            // Add accessories if they exist
            if (element.getAccessoriesList() != null && !element.getAccessoriesList().isEmpty()) {
                rowNum = addAccessoriesRows(sheet, rowNum, element.getAccessoriesList());
            }
        }

        Row row = sheet.createRow(rowNum++);
        setCellValue(row, colIndex, String.format("Sub-Total: %d", itemNumber - 1));

        // Highlight sub-total
        Workbook workbook1 = row.getSheet().getWorkbook();
        CellStyle style = workbook1.createCellStyle();
        style.cloneStyleFrom(row.getCell(colIndex).getCellStyle());
        Font font = workbook1.createFont();
        font.setBold(true);
        style.setFont(font);
        row.getCell(colIndex).setCellStyle(style);

        // Track total count of items
        totalElements += itemNumber - 1;
        totalColIndex = colIndex;

        return rowNum;
    }

    private int addAccessoriesRows(Sheet sheet, int startRow, List<ExcelResponseDTO.CommonFieldsDTO.AccessoriesListDTO> accessories) {
        int rowNum = startRow;
        CellStyle accessoryStyle = createAccessoryStyle(sheet.getWorkbook());
        CellStyle headerStyle = createAccessoryHeaderStyle(sheet.getWorkbook());

        // Add headers row
        Row headerRow = sheet.createRow(rowNum++);

        // Add one column margin and then headers
        Cell inventoryHeaderCell = headerRow.createCell(1);
        inventoryHeaderCell.setCellValue("Inventory List");
        inventoryHeaderCell.setCellStyle(headerStyle);

        Cell paramValueHeaderCell = headerRow.createCell(2);
        paramValueHeaderCell.setCellValue("Param Value");
        paramValueHeaderCell.setCellStyle(headerStyle);

        Cell quantityHeaderCell = headerRow.createCell(3);
        quantityHeaderCell.setCellValue("Quantity");
        quantityHeaderCell.setCellStyle(headerStyle);

        // Add data rows
        int count = 1;
        for (ExcelResponseDTO.CommonFieldsDTO.AccessoriesListDTO accessory : accessories) {
            Row accessoryRow = sheet.createRow(rowNum++);

            // Add one column margin and then data
            Cell countCell = accessoryRow.createCell(1);
            countCell.setCellValue(count++);
            countCell.setCellStyle(accessoryStyle);

            Cell paramValueCell = accessoryRow.createCell(2);
            paramValueCell.setCellValue(accessory.getParamValue() != null ? accessory.getParamValue() : "-");
            paramValueCell.setCellStyle(accessoryStyle);

            Cell quantityCell = accessoryRow.createCell(3);
            if (accessory.getQuantity() != null) {
                quantityCell.setCellValue(accessory.getQuantity());
            } else {
                quantityCell.setCellValue("-");
            }
            quantityCell.setCellStyle(accessoryStyle);
        }

        return rowNum;
    }

    private Map<String, BiConsumer<Row, ExcelResponseDTO.CommonFieldsDTO>> createColumnHandlers(List<String> visibleColumns) {
        Map<String, BiConsumer<Row, ExcelResponseDTO.CommonFieldsDTO>> handlers = new HashMap<>();
        AtomicInteger colIndex = new AtomicInteger(0); // Start from 1 (0 is for Sr. No.)

        for (String column : visibleColumns) {
            final int currentCol = colIndex.getAndIncrement();
            switch (column) {
                case "Name":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getName()));
                    break;
                case "Address":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getAddress()));
                    break;
                case "Category":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getCategory()));
                    break;
                case "Created By":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getCreatedBy()));
                    break;
                case "Port":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getPort()));
                    break;
                case "FDP Type Name":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getTypeName()));
                    break;
                case "FDP Type Description":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getTypeDescription()));
                    break;
                case "Capacity":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getCapacity()));
                    break;
                case "Cable Type Name":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getCableTypeName()));
                    break;
                case "Cable Type Description":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getCableTypeDescription()));
                    break;
                case "Mounting Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getMountingType()));
                    break;
                case "Cable Specification Name", "Splitter Specification Name":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getSpecificationName()));
                    break;
                case "No. of Cores":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getNumberOfCores()));
                    break;
                case "Cable Specification Description", "Splitter Specification Description":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getSpecificationDescription()));
                    break;
                case "Splitter Specification Port Ratio":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getPortRatio()));
                    break;
                case "Parent Box Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getParentBoxType()));
                    break;
                case "Tenancy":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getTenancy()));
                    break;
                case "Pole Type Name":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getPoleType()));
                    break;
                case "Power Levels":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getPowerLevels()));
                    break;
                case "Hole Size":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getHoleSize()));
                    break;
                case "Access Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getAccessType()));
                    break;
                case "Material":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getMaterial()));
                    break;
                case "Joint Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getJointType()));
                    break;
                case "Manhole Size":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getManholeSize()));
                    break;
                case "Depth (cm)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getDepthCm()));
                    break;
                case "Cover Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getCoverType()));
                    break;
                case "GIS Length (m)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getGisLengthM()));
                    break;
                case "Length (m)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getLengthM()));
                    break;
                case "Network Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getNetworkType()));
                    break;
                case "Width (m)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getWidthM()));
                    break;
                case "Depth (m)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getDepthM()));
                    break;
                case "Height (m)":
                    handlers.put(column, (row, element) -> setNumericCellValue(row, currentCol, element.getHeightM()));
                    break;
                case "Customer Type":
                    handlers.put(column, (row, element) -> setCellValue(row, currentCol, element.getCustomerType()));
                    break;
            }
        }

        return handlers;
    }

    // Helper methods remain the same as before
    private void setCellValue(Row row, int colIndex, String value) {
        Cell cell = row.createCell(colIndex);
        if (value.isBlank()){
            cell.setCellValue("-");
        } else {
            cell.setCellValue(value);
        }

        // Keeping columns other than first column centered
        if (colIndex != 0) {
            Workbook workbook = row.getSheet().getWorkbook();
            CellStyle style = workbook.createCellStyle();
            style.cloneStyleFrom(cell.getCellStyle()); // Preserve existing styles
            style.setAlignment(HorizontalAlignment.CENTER);
            cell.setCellStyle(style);
        }
    }

    private void setNumericCellValue(Row row, int colIndex, Number value) {
        if (value != null) {
            Cell cell = row.createCell(colIndex);
            cell.setCellValue(value.doubleValue());

            Workbook workbook = row.getSheet().getWorkbook();
            CellStyle style = workbook.createCellStyle();
            style.cloneStyleFrom(cell.getCellStyle()); // Preserve existing styles
            style.setAlignment(HorizontalAlignment.CENTER);
            cell.setCellStyle(style);
        } else {
            row.createCell(colIndex).setCellValue("-");
        }
    }
}
