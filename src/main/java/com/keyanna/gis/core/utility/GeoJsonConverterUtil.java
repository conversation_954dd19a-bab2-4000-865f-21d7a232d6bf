package com.keyanna.gis.core.utility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.*;
import com.keyanna.gis.core.dto.response.CableResponseDTO;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.dto.response.StreetDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.model.TrenchLayer;
import org.locationtech.jts.geom.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GeoJsonConverterUtil {

    private static final Logger logger = LoggerFactory.getLogger(GeoJsonConverterUtil.class);
//    private static final GeometryJSON geometryJSON = new GeometryJSON(10);
    private static final GeometryFactory geometryFactory = new GeometryFactory();

    public static CableResponseDTO.Geometry convertToLineStringJson(LineString geom) {
        CableResponseDTO.Geometry geometryDto = new CableResponseDTO.Geometry();
        geometryDto.setType(ApiConstants.LABEL_LINESTRING);

        List<List<Double>> coords = new ArrayList<List<Double>>();
        Coordinate[] coordinates = geom.getCoordinates();

        for (int i = 0; i < coordinates.length; i++) {
            List<Double> point = new ArrayList<Double>();
            point.add(coordinates[i].x); // longitude
            point.add(coordinates[i].y); // latitude
            coords.add(point);
        }

        geometryDto.setCoordinates(coords);
        return geometryDto;
    }

    public static PopResponseDTO.Geometry convertToPointJsonPop(String geoJsonString, ObjectMapper objectMapper) throws IOException {
        return objectMapper.readValue(geoJsonString, PopResponseDTO.Geometry.class);
    }

    public static CableResponseDTO.Geometry convertToPointJsonCable(String geoJsonString, ObjectMapper objectMapper) throws IOException {
        return objectMapper.readValue(geoJsonString, CableResponseDTO.Geometry.class);
    }

    /**
     * @param
     * @return
     */
//    public static PopResponseDTO.Geometry convertToPointJson(Point geom) {
//        PopResponseDTO.Geometry geometryDto = new PopResponseDTO.Geometry();
//        geometryDto.setType("Point");
//
//        List<Double> coords = new ArrayList<>();
//        coords.add(geom.getX()); // longitude
//        coords.add(geom.getY()); // latitude
//
//        // Note: setCoordinates should accept List<Double> for Point type
//        geometryDto.setCoordinates(coords);
//
//        return geometryDto;
//    }

//    //  TODO : use responseDTO for setting data like : public static class Geometry {
//    public static Map<String, Object> mapCableListToGeoJSONFromDirectGeom(List<Cable> cableList, ObjectMapper objectMapper) {
//        try {
//            if (cableList == null) {
//                throw new IllegalArgumentException("Cable list is null. Cannot generate GeoJSON.");
//            }
//
//            if (cableList.isEmpty()) {
//                throw new EntityNotFoundException("No cables found for the provided criteria.");
//            }
//            Map<String, Object> geoJson = new HashMap<>();
//            geoJson.put("type", "FeatureCollection");
//
//            List<Map<String, Object>> featureList = new ArrayList<>();
//
//            Map<String, Object> feature = new HashMap<>();
//            for (Cable cable : cableList) {
//                feature = new HashMap<>();
//                feature.put("type", "Feature");
//
//                // Properties
//                Map<String, Object> properties = new HashMap<>();
//                //            properties.put("id", poi.getId());
//                feature.put("properties", properties);
//
//                // Geometry
//                // Convert GeoJSON string to a JSON Object
//                String geoJsonString = geometryToGeoJson(cable.getGeom());
//                Map<String, Object> geometry = objectMapper.readValue(geoJsonString, Map.class);
//
//                feature.put("geometry", geometry);
//                featureList.add(feature);
//            }
//
//            geoJson.put("features", featureList);
//
//            return geoJson;
//        } catch (JsonProcessingException ex) {
//            logger.error("Error parsing GeoJSON for cable geometry", ex);
//            throw new RuntimeException("Failed to parse GeoJSON geometry: " + ex.getMessage(), ex);
//        } catch (Exception ex) {
//            logger.error("Unexpected error while converting to GeoJSON", ex);
//            throw new RuntimeException("Unexpected error during GeoJSON conversion: " + ex.getMessage(), ex);
//        }
//    }

//    public static Map<String, Object> mapCableListToGeoJSONFromDirect(List<TrenchLayer> trenchList, ObjectMapper objectMapper) {
//        try {
//            if (trenchList == null) {
//                throw new IllegalArgumentException("Cable list is null. Cannot generate GeoJSON.");
//            }
//
//            if (trenchList.isEmpty()) {
//                throw new EntityNotFoundException("No cables found for the provided criteria.");
//            }
//            Map<String, Object> geoJson = new HashMap<>();
//            geoJson.put("type", "FeatureCollection");
//
//            List<Map<String, Object>> featureList = new ArrayList<>();
//
//            Map<String, Object> feature = new HashMap<>();
//            for (TrenchLayer cable : trenchList) {
//                feature = new HashMap<>();
//                feature.put("type", "Feature");
//
//                // Properties
//                Map<String, Object> properties = new HashMap<>();
//                //            properties.put("id", poi.getId());
//                feature.put("properties", properties);
//
//                // Geometry
//                // Convert GeoJSON string to a JSON Object
//                String geoJsonString = geometryToGeoJson(cable.getGeom());
//                Map<String, Object> geometry = objectMapper.readValue(geoJsonString, Map.class);
//
//                feature.put("geometry", geometry);
//                featureList.add(feature);
//            }
//
//            geoJson.put("features", featureList);
//
//            return geoJson;
//        } catch (JsonProcessingException ex) {
//            logger.error("Error parsing GeoJSON for cable geometry", ex);
//            throw new RuntimeException("Failed to parse GeoJSON geometry: " + ex.getMessage(), ex);
//        } catch (Exception ex) {
//            logger.error("Unexpected error while converting to GeoJSON", ex);
//            throw new RuntimeException("Unexpected error during GeoJSON conversion: " + ex.getMessage(), ex);
//        }
//    }


//    public static String geometryToGeoJson(Geometry geometry) {
//        if (geometry == null) return null;
//        StringWriter writer = new StringWriter();
//        try {
//            geometryJSON.write(geometry, writer);
//            return writer.toString();
//        } catch (IOException ex) {
//            throw new RuntimeException("Failed to convert geometry to GeoJSON", ex);
//        }
//    }

    public static LineString convertGeometryToLineString(CableRequestDTO.Geometry geom) {
        if (!ApiConstants.LABEL_LINESTRING.equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only LineString geometry is supported.");
        }

        List<List<Double>> coords = geom.getCoordinates();
        Coordinate[] coordinates = coords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordinates);
    }

    public static LineString convertGeometryToLineString(TrenchDTO.Geometry geom) {
        if (!ApiConstants.LABEL_LINESTRING.equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only LineString geometry is supported.");
        }

        List<List<Double>> coords = geom.getCoordinates();
        Coordinate[] coordinates = coords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordinates);
    }

    public static LineString convertGeometryToLineString(DuctDTO.Geometry geom) {
        if (!ApiConstants.LABEL_LINESTRING.equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only LineString geometry is supported.");
        }

        List<List<Double>> coords = geom.getCoordinates();
        Coordinate[] coordinates = coords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordinates);
    }

    public static Point createPointGeom(double longitude, double latitude) {
        /**
         * When you use the Coordinate(double x, double y) constructor in JTS, by convention:
         * x typically represents the longitude.
         * y typically represents the latitude.
         */
        Coordinate coordinate = new Coordinate(longitude, latitude);
        return geometryFactory.createPoint(coordinate);
    }

    public static Polygon convertGeometryToPolygon(SurveyAreaRequestDTO.GeometryPolygonDto geom) {
        if (!"Polygon".equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only Polygon geometry is supported.");
        }

        List<List<List<Double>>> coords = geom.getCoordinates();

        if (coords == null || coords.isEmpty()) {
            throw new IllegalArgumentException("Coordinates cannot be null or empty for Polygon.");
        }

        // The first list is the outer shell (LinearRing)
        List<List<Double>> shellCoords = coords.get(0);
        Coordinate[] shell = shellCoords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        LinearRing shellRing = geometryFactory.createLinearRing(shell);

        // Holes (interior rings) if any
        LinearRing[] holes = null;
        if (coords.size() > 1) {
            holes = new LinearRing[coords.size() - 1];
            for (int i = 1; i < coords.size(); i++) {
                List<List<Double>> holeCoords = coords.get(i);
                Coordinate[] hole = holeCoords.stream()
                        .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                        .toArray(Coordinate[]::new);
                holes[i - 1] = geometryFactory.createLinearRing(hole);
            }
        }

        return geometryFactory.createPolygon(shellRing, holes);
    }

    public static Polygon convertGeometryToPolygon(ClusterMasterRequestDTO.GeometryPolygonDto geom) {
        if (!"Polygon".equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only Polygon geometry is supported.");
        }

        List<List<List<Double>>> coords = geom.getCoordinates();

        if (coords == null || coords.isEmpty()) {
            throw new IllegalArgumentException("Coordinates cannot be null or empty for Polygon.");
        }

        // The first list is the outer shell (LinearRing)
        List<List<Double>> shellCoords = coords.get(0);
        Coordinate[] shell = shellCoords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        LinearRing shellRing = geometryFactory.createLinearRing(shell);

        // Holes (interior rings) if any
        LinearRing[] holes = null;
        if (coords.size() > 1) {
            holes = new LinearRing[coords.size() - 1];
            for (int i = 1; i < coords.size(); i++) {
                List<List<Double>> holeCoords = coords.get(i);
                Coordinate[] hole = holeCoords.stream()
                        .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                        .toArray(Coordinate[]::new);
                holes[i - 1] = geometryFactory.createLinearRing(hole);
            }
        }

        return geometryFactory.createPolygon(shellRing, holes);
    }

    public static LineString convertGeometryToLineString(StreetDTO.Geometry geom) {
        if (!ApiConstants.LABEL_LINESTRING.equalsIgnoreCase(geom.getType())) {
            throw new IllegalArgumentException("Only LineString geometry is supported.");
        }

        List<List<Double>> coords = geom.getCoordinates();
        Coordinate[] coordinates = coords.stream()
                .map(coord -> new Coordinate(coord.get(0), coord.get(1)))
                .toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordinates);
    }

    }
    //  TODO remove if not in use
//    public static Point convertGeometryToPoint(PopRequestDTO.Geometry geom) {
//        if (!ApiConstants.LABEL_POINT.equalsIgnoreCase(geom.getType())) {
//            throw new IllegalArgumentException("Only Point geometry is supported.");
//        }
//
//        List<Double> coords = (List<Double>) geom.getCoordinates(); // [longitude, latitude]
//
//        if (coords.size() != 2) {
//            throw new IllegalArgumentException("Point geometry must have exactly two coordinates.");
//        }
//
//        Coordinate coordinate = new Coordinate(coords.get(0), coords.get(1)); // x = lon, y = lat
//        return geometryFactory.createPoint(coordinate);
//    }


