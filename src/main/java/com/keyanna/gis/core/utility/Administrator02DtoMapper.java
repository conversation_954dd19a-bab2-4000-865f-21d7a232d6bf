package com.keyanna.gis.core.utility;

import com.keyanna.gis.core.dto.response.Administrator02DTO;

import java.util.List;
import java.util.stream.Collectors;

public class Administrator02DtoMapper {

    public static Administrator02DTO map(Object[] row) {
        Administrator02DTO dto = new Administrator02DTO();
        dto.setId(((Number)row[0]).longValue());
        dto.setAdm2Name((String)row[1]);
        dto.setAdm2Code((String)row[2]);

        // ▶︎ Directly assign the GeoJSON returned by ST_AsGeoJSON()
        dto.setGeom((String)row[3]);
        return dto;
    }

    /**
     * Map a list of rows into a list of DTOs.
     */
    public static List<Administrator02DTO> mapList(List<Object[]> rows) {
        return rows.stream()
                .map(Administrator02DtoMapper::map)
                .collect(Collectors.toList());
    }

}
