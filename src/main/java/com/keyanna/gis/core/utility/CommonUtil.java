package com.keyanna.gis.core.utility;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.DigitalizationResponseDTO;
import com.keyanna.gis.core.dto.response.PolygonResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.CableSpecification;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

public class CommonUtil {

    //  Note : Reference method to use in : CommonServiceImpl.buildResponseMap
//    public static List<PolygonResponseDTO.Splitter> convertSplitterJsonToResponseDTO(JsonNode splitterArrayNode, ObjectMapper objectMapper) throws IOException {
//        List<PolygonResponseDTO.Splitter> responseDtoList = new ArrayList<>();
//
//        // Iterate through the 'splitter' array and map each item to GeometryResponseDTO.Splitter
//        for (JsonNode splitterNode : splitterArrayNode) {
//            PolygonResponseDTO.Splitter responseDto = new PolygonResponseDTO.Splitter();
//
//            responseDto.setName(splitterNode.path("name").asText());
//
//            //  Specification
//            PolygonResponseDTO.Splitter.SplitterSpecification splitterSpecObj = new PolygonResponseDTO.Splitter.SplitterSpecification();
//            splitterSpecObj.setSpecificationId(splitterNode.path("specificationId").asInt());
//            splitterSpecObj.setSpecificationName(splitterNode.path("specificationName").asText());
//            splitterSpecObj.setSpecificationPortRatio(splitterNode.path("portRatio").asText());
//            splitterSpecObj.setSpecificationDescription(splitterNode.path("specificationDescription").asText());
//            splitterSpecObj.setSpecificationIsActive(splitterNode.path("specificationIsActive").asText());
//            responseDto.setSplitterSpecification(splitterSpecObj);
//
//            responseDto.setParentFatId(splitterNode.path("parentFatId").asInt());
//            responseDto.setParentFdtId(splitterNode.path("parentFdtId").asInt());
//            responseDto.setParentBoxType(splitterNode.path("parentBoxType").asText());
//            responseDto.setStatus(splitterNode.path("status").asText());
//            responseDto.setUserId(splitterNode.path("createdBy").asLong());
//
//            // Handle the geometry
//            String geoJsonString = splitterNode.path("geom").asText();
//            responseDto.setGeom(objectMapper.readValue(geoJsonString, PolygonResponseDTO.Splitter.Geometry.class));
//
//            responseDtoList.add(responseDto);
//        }
//        return responseDtoList;
//    }

    /**
     * customer
     */
    public static List<PolygonResponseDTO.CustomerDto> convertCustomerJsonToResponseDTO(JsonNode customerArrayNode) {
        List<PolygonResponseDTO.CustomerDto> responseDtoList = new ArrayList<>();

        if (customerArrayNode == null || customerArrayNode.isNull() || !customerArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'customer' array and map each item to PolygonResponseDTO.CustomerDto
        for (JsonNode customerNode : customerArrayNode) {
            PolygonResponseDTO.CustomerDto responseDto = new PolygonResponseDTO.CustomerDto();

            responseDto.setName(customerNode.path("name").asText());
            responseDto.setCustomerType(customerNode.path("customerType").asText());
            responseDto.setAddress(customerNode.path("address").isMissingNode() ? null : customerNode.path("address").asText());
            responseDto.setPort(customerNode.path("port").isMissingNode() ? null : customerNode.path("port").asInt());
            responseDto.setStatus(customerNode.path("status").isMissingNode() ? null : customerNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * fat
     */
    public static List<DigitalizationResponseDTO.FatDto> convertFatJsonToResponseDTO(JsonNode fatArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.FatDto> responseDtoList = new ArrayList<>();

        if (fatArrayNode == null || fatArrayNode.isNull() || !fatArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'fat' array and map each item to PolygonResponseDTO.FatDto
        for (JsonNode fatNode : fatArrayNode) {
            DigitalizationResponseDTO.FatDto responseDto = new DigitalizationResponseDTO.FatDto();

            responseDto.setPublicId(UUID.fromString(fatNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(fatNode.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(fatNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = fatNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.FatDto.Geometry.class));

//            responseDto.setCapacity(fatNode.path("capacity").isMissingNode() ? null : fatNode.path("capacity").asInt());
//            responseDto.setAddress(fatNode.path("address").isMissingNode() ? null : fatNode.path("address").asText());
//            responseDto.setPowerLevels(fatNode.path("powerLevels").isMissingNode() || fatNode.path("powerLevels").isNull() ? null : fatNode.path("powerLevels").decimalValue());
//            responseDto.setStatus(fatNode.path("status").isMissingNode() ? null : fatNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * fdt
     */
    public static List<DigitalizationResponseDTO.FdtDto> convertFdtJsonToResponseDTO(JsonNode fdtArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.FdtDto> responseDtoList = new ArrayList<>();

        if (fdtArrayNode == null || fdtArrayNode.isNull() || !fdtArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'fdt' array and map each item to DigitalizationResponseDTO.FdtDto
        for (JsonNode fdtNode : fdtArrayNode) {
            DigitalizationResponseDTO.FdtDto responseDto = new DigitalizationResponseDTO.FdtDto();

            responseDto.setPublicId(UUID.fromString(fdtNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(fdtNode.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(fdtNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = fdtNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.FdtDto.Geometry.class));

//            responseDto.setCapacity(fdtNode.path("capacity").asInt());
//            responseDto.setAddress(fdtNode.path("address").isMissingNode() ? null : fdtNode.path("address").asText());
//            responseDto.setStatus(fdtNode.path("status").isMissingNode() ? null : fdtNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * fdp
     */
    public static List<PolygonResponseDTO.FdpDto> convertFdpJsonToResponseDTO(JsonNode fdpArrayNode) {
        List<PolygonResponseDTO.FdpDto> responseDtoList = new ArrayList<>();

        if (fdpArrayNode == null || fdpArrayNode.isNull() || !fdpArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'fdp' array and map each item to PolygonResponseDTO.FdpDto
        for (JsonNode fdpNode : fdpArrayNode) {
            PolygonResponseDTO.FdpDto responseDto = new PolygonResponseDTO.FdpDto();

            responseDto.setName(fdpNode.path("name").asText());
            responseDto.setPort(fdpNode.path("port").asText());
            responseDto.setStatus(fdpNode.path("status").isMissingNode() ? null : fdpNode.path("status").asText());

            //  Type
            PolygonResponseDTO.FdpDto.LookupFdpTypeDto lookUpFdpTypeObj = new PolygonResponseDTO.FdpDto.LookupFdpTypeDto();
            lookUpFdpTypeObj.setTypeName(fdpNode.path("typeName").asText());
            lookUpFdpTypeObj.setTypeDescription(fdpNode.path("typeDescription").isMissingNode() ? null : fdpNode.path("typeDescription").asText());
            responseDto.setFdpType(lookUpFdpTypeObj);

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * handhole
     */
    public static List<PolygonResponseDTO.HandholeDto> convertHandholeJsonToResponseDTO(JsonNode handholeArrayNode) {
        List<PolygonResponseDTO.HandholeDto> responseDtoList = new ArrayList<>();

        if (handholeArrayNode == null || handholeArrayNode.isNull() || !handholeArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'handhole' array and map each item to PolygonResponseDTO.HandholeDto
        for (JsonNode handholeNode : handholeArrayNode) {
            PolygonResponseDTO.HandholeDto responseDto = new PolygonResponseDTO.HandholeDto();

            responseDto.setName(handholeNode.path("name").asText());
            responseDto.setHoleSize(handholeNode.path("holeSize").isMissingNode() ? null : handholeNode.path("holeSize").asText());
            responseDto.setAccessType(handholeNode.path("accessType").asText());
            responseDto.setMaterial(handholeNode.path("material").isMissingNode() ? null : handholeNode.path("material").asText());
            responseDto.setStatus(handholeNode.path("status").isMissingNode() ? null : handholeNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * jointClosure
     */
    public static List<DigitalizationResponseDTO.JointClosureDto> convertJointClosureJsonToResponseDTO(JsonNode jointClosureArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.JointClosureDto> responseDtoList = new ArrayList<>();

        if (jointClosureArrayNode == null || jointClosureArrayNode.isNull() || !jointClosureArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'jointClosure' array and map each item to DigitalizationResponseDTO.JointClosureDto
        for (JsonNode jointClosureNode : jointClosureArrayNode) {
            DigitalizationResponseDTO.JointClosureDto responseDto = new DigitalizationResponseDTO.JointClosureDto();

            responseDto.setPublicId(UUID.fromString(jointClosureNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(jointClosureNode.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(jointClosureNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = jointClosureNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.JointClosureDto.Geometry.class));

//            responseDto.setCapacity(jointClosureNode.path("capacity").isMissingNode() ? null : jointClosureNode.path("capacity").asInt());
//            responseDto.setJointType(jointClosureNode.path("jointType").isMissingNode() ? null : jointClosureNode.path("jointType").asText());
//            responseDto.setStatus(jointClosureNode.path("status").isMissingNode() ? null : jointClosureNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * manhole
     */
    public static List<PolygonResponseDTO.ManholeDto> convertManholeJsonToResponseDTO(JsonNode manholeArrayNode) {
        List<PolygonResponseDTO.ManholeDto> responseDtoList = new ArrayList<>();

        if (manholeArrayNode == null || manholeArrayNode.isNull() || !manholeArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'manhole' array and map each item to PolygonResponseDTO.ManholeDto
        for (JsonNode manholeNode : manholeArrayNode) {
            PolygonResponseDTO.ManholeDto responseDto = new PolygonResponseDTO.ManholeDto();

            responseDto.setName(manholeNode.path("name").asText());
            responseDto.setManholeSize(manholeNode.path("manholeSize").asText());
            responseDto.setDepthCm(manholeNode.path("depthCm").isMissingNode() ? null : manholeNode.path("depthCm").asInt());
            responseDto.setCoverType(manholeNode.path("coverType").asText());
            responseDto.setStatus(manholeNode.path("status").isMissingNode() ? null : manholeNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * pole
     */
    public static List<PolygonResponseDTO.PoleDto> convertPoleJsonToResponseDTO(JsonNode poleArrayNode) {
        List<PolygonResponseDTO.PoleDto> responseDtoList = new ArrayList<>();

        if (poleArrayNode == null || poleArrayNode.isNull() || !poleArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'pole' array and map each item to PolygonResponseDTO.PoleDto
        for (JsonNode poleNode : poleArrayNode) {
            PolygonResponseDTO.PoleDto responseDto = new PolygonResponseDTO.PoleDto();

            responseDto.setName(poleNode.path("name").asText());
            responseDto.setPoleType(poleNode.path("poleType").asText());
            responseDto.setStatus(poleNode.path("status").isMissingNode() ? null : poleNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * pop
     */
    public static List<PolygonResponseDTO.PopDto> convertPopJsonToResponseDTO(JsonNode popArrayNode) {
        List<PolygonResponseDTO.PopDto> responseDtoList = new ArrayList<>();

        if (popArrayNode == null || popArrayNode.isNull() || !popArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'pop' array and map each item to PolygonResponseDTO.PopDto
        for (JsonNode popNode : popArrayNode) {
            PolygonResponseDTO.PopDto responseDto = new PolygonResponseDTO.PopDto();

            responseDto.setName(popNode.path("name").asText());
            responseDto.setAddress(popNode.path("address").isMissingNode() ? null : popNode.path("address").asText());
            responseDto.setCategory(popNode.path("category").asText());
            responseDto.setStatus(popNode.path("status").isMissingNode() ? null : popNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * olt
     */
    public static List<DigitalizationResponseDTO.OltDto> convertOltJsonToResponseDTO(JsonNode oltArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.OltDto> responseDtoList = new ArrayList<>();

        if (oltArrayNode == null || oltArrayNode.isNull() || !oltArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'olt' array and map each item to DigitalizationResponseDTO.OltDto
        for (JsonNode oltNode : oltArrayNode) {
            DigitalizationResponseDTO.OltDto responseDto = new DigitalizationResponseDTO.OltDto();

            responseDto.setPublicId(UUID.fromString(oltNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(oltNode.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(oltNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = oltNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.OltDto.Geometry.class));

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * splitter
     */
    public static List<DigitalizationResponseDTO.SplitterDto> convertSplitterJsonToResponseDTO(JsonNode splitterArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.SplitterDto> responseDtoList = new ArrayList<>();

        if (splitterArrayNode == null || splitterArrayNode.isNull() || !splitterArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'splitter' array and map each item to DigitalizationResponseDTO.SplitterDto
        for (JsonNode splitterNode : splitterArrayNode) {
            DigitalizationResponseDTO.SplitterDto responseDto = new DigitalizationResponseDTO.SplitterDto();

            responseDto.setPublicId(UUID.fromString(splitterNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(splitterNode.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(splitterNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = splitterNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.SplitterDto.Geometry.class));

//            responseDto.setStatus(splitterNode.path("status").isMissingNode() ? null : splitterNode.path("status").asText());
//
//            //  Specification
//            DigitalizationResponseDTO.SplitterDto.SplitterSpecificationDto splitterSpecObj = new DigitalizationResponseDTO.SplitterDto.SplitterSpecificationDto();
//            splitterSpecObj.setSpecificationName(splitterNode.path("specificationName").asText());
//            splitterSpecObj.setSpecificationPortRatio(splitterNode.path("portRatio").asText());
//            splitterSpecObj.setSpecificationDescription(splitterNode.path("specificationDescription").isMissingNode() ? null : splitterNode.path("specificationDescription").asText());
//            responseDto.setSplitterSpecificationDto(splitterSpecObj);

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * sdu
     */
    public static List<DigitalizationResponseDTO.SduDto> convertSduJsonToResponseDTO(JsonNode sduArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.SduDto> responseDtoList = new ArrayList<>();

        if (sduArrayNode == null || sduArrayNode.isNull() || !sduArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'sdu' array and map each item to SduDto
        for (JsonNode sduNode : sduArrayNode) {
            DigitalizationResponseDTO.SduDto responseDto = objectMapper.treeToValue(sduNode, DigitalizationResponseDTO.SduDto.class);
            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * For method : com.keyanna.gis.core.service.impl.CommonServiceImpl#getSurveyDataForDigitalization(java.util.UUID, java.lang.Integer)
     * cable
     */
    public static List<DigitalizationResponseDTO.CableDto> convertCableJsonToResponseDTO(JsonNode cableArrayNode, ObjectMapper objectMapper) throws IOException {
        List<DigitalizationResponseDTO.CableDto> responseDtoList = new ArrayList<>();

        if (cableArrayNode == null || cableArrayNode.isNull() || !cableArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'cable' array and map each item to PolygonResponseDTO.CableDto
        for (JsonNode cableNode : cableArrayNode) {
            DigitalizationResponseDTO.CableDto responseDto = new DigitalizationResponseDTO.CableDto();

            responseDto.setPublicId(UUID.fromString(cableNode.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setCableId(cableNode.path("cable_id").asText());
            responseDto.setType(cableNode.path("type").asText());
            responseDto.setStatus(cableNode.path("status").asText());
            responseDto.setLengthInMeters(cableNode.path("length_in_meters").isMissingNode() ? null : cableNode.path("length_in_meters").asDouble());
            responseDto.setLayerName(cableNode.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = cableNode.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, DigitalizationResponseDTO.CableDto.Geometry.class));

//            //  Type
//            PolygonResponseDTO.CableDto.LookupCableTypeDto lookUpCableTypeObj = new PolygonResponseDTO.CableDto.LookupCableTypeDto();
//            lookUpCableTypeObj.setTypeName(cableNode.path("cableTypeName").asText());
//            lookUpCableTypeObj.setTypeDescription(cableNode.path("cableTypeDescription").asText());
//            responseDto.setCableTypeDto(lookUpCableTypeObj);
//
//            responseDto.setMountingType(cableNode.path("mountingType").isMissingNode() ? null : cableNode.path("mountingType").asText());
//            responseDto.setStatus(cableNode.path("status").isMissingNode() ? null : cableNode.path("status").asText());
//
//            //  Specification
//            PolygonResponseDTO.CableDto.CableSpecificationDto cableSpecObj = new PolygonResponseDTO.CableDto.CableSpecificationDto();
//            cableSpecObj.setSpecificationName(cableNode.path("specificationName").asText());
//            cableSpecObj.setNumberOfCores(cableNode.path("numberOfCores").asInt());
//            cableSpecObj.setSpecificationDescription(cableNode.path("specificationDescription").isMissingNode() ? null : cableNode.path("specificationDescription").asText());
//            responseDto.setCableSpecificationDto(cableSpecObj);
//
//            responseDto.setGisLengthM(cableNode.path("gisLengthM").isMissingNode() ? null : cableNode.path("gisLengthM").asDouble());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * duct
     */
    public static List<PolygonResponseDTO.DuctDto> convertDuctJsonToResponseDTO(JsonNode ductArrayNode) {
        List<PolygonResponseDTO.DuctDto> responseDtoList = new ArrayList<>();

        if (ductArrayNode == null || ductArrayNode.isNull() || !ductArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'duct' array and map each item to PolygonResponseDTO.DuctDto
        for (JsonNode ductNode : ductArrayNode) {
            PolygonResponseDTO.DuctDto responseDto = new PolygonResponseDTO.DuctDto();

            responseDto.setName(ductNode.path("name").isMissingNode() ? null : ductNode.path("name").asText());
            responseDto.setLengthM(ductNode.path("lengthM").isMissingNode() ? null : ductNode.path("lengthM").asDouble());
            responseDto.setNetworkType(ductNode.path("networkType").isMissingNode() ? null : ductNode.path("networkType").asText());
            responseDto.setStatus(ductNode.path("status").isMissingNode() ? null : ductNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * trench
     */
    public static List<PolygonResponseDTO.TrenchDto> convertTrenchJsonToResponseDTO(JsonNode trenchArrayNode) {
        List<PolygonResponseDTO.TrenchDto> responseDtoList = new ArrayList<>();

        if (trenchArrayNode == null || trenchArrayNode.isNull() || !trenchArrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through the 'trench' array and map each item to PolygonResponseDTO.TrenchDto
        for (JsonNode trenchNode : trenchArrayNode) {
            PolygonResponseDTO.TrenchDto responseDto = new PolygonResponseDTO.TrenchDto();

            responseDto.setName(trenchNode.path("name").isMissingNode() ? null : trenchNode.path("name").asText());
            responseDto.setWidthM(trenchNode.path("widthM").isMissingNode() ? null : trenchNode.path("widthM").asDouble());
            responseDto.setDepthM(trenchNode.path("depthM").isMissingNode() ? null : trenchNode.path("depthM").asDouble());
            responseDto.setLengthM(trenchNode.path("lengthM").isMissingNode() ? null : trenchNode.path("lengthM").asDouble());
            responseDto.setStatus(trenchNode.path("status").isMissingNode() ? null : trenchNode.path("status").asText());

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * From method : CommonServiceImpl.getDataInSurveyArea : DISA
     * For : Point method
     */
    public static List<PolygonResponseDTO.PointDataInSurveyArea> convertPointJsonDataToResponseDtoDISA(JsonNode arrayNode, ObjectMapper objectMapper) throws IOException {
        List<PolygonResponseDTO.PointDataInSurveyArea> responseDtoList = new ArrayList<>();

        if (arrayNode == null || arrayNode.isNull() || !arrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through array and map each item to GeometryResponseDTO.PointDataInSurveyArea
        for (JsonNode node : arrayNode) {
            PolygonResponseDTO.PointDataInSurveyArea responseDto = new PolygonResponseDTO.PointDataInSurveyArea();

            responseDto.setPublicId(UUID.fromString(node.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(node.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(node.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = node.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, PolygonResponseDTO.PointDataInSurveyArea.Geometry.class));

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * From method : CommonServiceImpl.getDataInSurveyArea : DISA
     * For : LineString method
     */
    public static List<PolygonResponseDTO.LineStringDataInSurveyArea> convertLineStringJsonDataToResponseDtoDISA(JsonNode arrayNode, ObjectMapper objectMapper) throws IOException {
        List<PolygonResponseDTO.LineStringDataInSurveyArea> responseDtoList = new ArrayList<>();

        if (arrayNode == null || arrayNode.isNull() || !arrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through array and map each item to GeometryResponseDTO.LineStringDataInSurveyArea
        for (JsonNode node : arrayNode) {
            PolygonResponseDTO.LineStringDataInSurveyArea responseDto = new PolygonResponseDTO.LineStringDataInSurveyArea();

            responseDto.setPublicId(UUID.fromString(node.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(node.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(node.path(ApiConstants.LAYER_NAME).asText());

            // Handle the geometry
            String geoJsonString = node.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, PolygonResponseDTO.LineStringDataInSurveyArea.Geometry.class));

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    /**
     * From method : CommonServiceImpl.getDataInSurveyArea : DISA
     * For : Polygon method
     */
    public static List<PolygonResponseDTO.PolygonDataInSurveyArea> convertPolygonJsonDataToResponseDtoDISA(JsonNode arrayNode, ObjectMapper objectMapper) throws IOException {
        List<PolygonResponseDTO.PolygonDataInSurveyArea> responseDtoList = new ArrayList<>();

        if (arrayNode == null || arrayNode.isNull() || !arrayNode.isArray()) {
            return responseDtoList; // return empty list if null or not an array
        }

        // Iterate through array and map each item to GeometryResponseDTO.PolygonDataInSurveyArea
        for (JsonNode node : arrayNode) {
            PolygonResponseDTO.PolygonDataInSurveyArea responseDto = new PolygonResponseDTO.PolygonDataInSurveyArea();

            responseDto.setPublicId(UUID.fromString(node.path(ApiConstants.PUBLIC_ID).asText()));
            responseDto.setName(node.path(ApiConstants.NAME).asText());
            responseDto.setLayerName(node.path(ApiConstants.LAYER_NAME).asText());
            responseDto.setIsActive(node.path("isActive").asText());
            responseDto.setSurveyEndDate(node.path("surveyEndDate").asText());
            responseDto.setSurveyStartDate(node.path("surveyStartDate").asText());
            responseDto.setSurveyStatusId(node.path("surveyStatusId").asText());

            // Handle the geometry
            String geoJsonString = node.path(ApiConstants.GEOM).asText();
            responseDto.setGeom(objectMapper.readValue(geoJsonString, PolygonResponseDTO.PolygonDataInSurveyArea.Geometry.class));

            responseDtoList.add(responseDto);
        }
        return responseDtoList;
    }

    public static ZonedDateTime convertToUserTimeZone0(Instant originalTime, String userTimeZone) {
        ZonedDateTime dateInInIST = originalTime.atZone(ZoneId.of(userTimeZone));
        return dateInInIST;
    }

    //  TODO : remove below if not in use
//    public static ZonedDateTime convertToUserTimeZone(ZonedDateTime originalTime, String userTimeZone){
//        ZoneId userZoneId = ZoneId.of(userTimeZone);    //  "Asia/Kolkata"
//        ZonedDateTime convertedUserTimeZone =  originalTime.withZoneSameInstant(userZoneId);
//        return convertedUserTimeZone;
//    }

    //  TODO : remove below if not in use
//    public static ZonedDateTime convertToUserTimeZone1(ZonedDateTime originalTime, String userTimeZone){
//        ZoneId userZoneId = ZoneId.of("Africa/Nairobi");
//
//        ZonedDateTime convertedUserTimeZone =  originalTime.withZoneSameInstant(userZoneId);
//        System.out.println("Before converted time originalTime : " + originalTime);
//        System.out.println("::::::::: " + userTimeZone + "::::::::: ");
//        System.out.println("Converted time convertedUserTimeZone : " + convertedUserTimeZone);
//
//        //////
//        ZoneId userZoneId1 = ZoneId.of("Asia/Kolkata");
//        ZonedDateTime convertedUserTimeZone1 =  originalTime.withZoneSameInstant(userZoneId1);
//
//        System.out.println("::::::::: static = Asia/Kolkata ::::::::: ");
//        System.out.println("Converted time convertedUserTimeZone1 : " + convertedUserTimeZone1);
//        System.out.println("========================================");
//        //////
//        return convertedUserTimeZone1;
//    }

    //  TODO : remove below if not in use
//    public static ZonedDateTime convertToUserTimeZone2(Instant originalTime, String userTimeZone){
//        ZonedDateTime createdOnInIST = originalTime.atZone(ZoneId.of(userTimeZone));
//
//        System.out.println("Before converted time originalTime : " + originalTime);
//        System.out.println("::::::::: " + userTimeZone + "::::::::: ");
//        System.out.println("Converted time createdOnInIST : " + createdOnInIST);
//
//        //////
////        ZonedDateTime createdOnInIST2 = originalTime.atZone(ZoneId.of("Asia/Kolkata"));
////
////        System.out.println("::::::::: static = Asia/Kolkata ::::::::: ");
////        System.out.println("Converted time createdOnInIST : " + createdOnInIST2);
////        System.out.println("========================================");
//        //////
//        return createdOnInIST;
//    }

    /**
     * This can be used while need to use mapper with getAllCableSpecifications
     * Not in use
     */
    public static Map<String, Object> mapCableSpecificationResponseFromList(List<CableSpecification> cableSpecificationList) {

        try {
            if (cableSpecificationList == null) {
                throw new IllegalArgumentException("Cable Specification list is null. Cannot generate GeoJSON.");
            }

            if (cableSpecificationList.isEmpty()) {
                throw new EntityNotFoundException("No cable Specification found for the provided criteria.");
            }
            Map<String, Object> mappedResponse = new HashMap<>();

            for (CableSpecification cableSpecification : cableSpecificationList) {
                mappedResponse.put("id", cableSpecification.getId());
                mappedResponse.put("name", cableSpecification.getName());
                mappedResponse.put("numberOfCores", cableSpecification.getNumberOfCores());
                mappedResponse.put("description", cableSpecification.getDescription());
                mappedResponse.put("isActive", cableSpecification.getIsActive());
                mappedResponse.put("createdOn", cableSpecification.getCreatedOn()); //  TODO check null if required here as per UI configurations/validations
                mappedResponse.put("modifiedOn", cableSpecification.getModifiedOn()); //  TODO check null if required here as per UI configurations/validations
            }

            return mappedResponse;
        } catch (Exception ex) {
            throw new RuntimeException("Unexpected error during GeoJSON conversion: " + ex.getMessage(), ex);
        }
    }

    /**
     * For method : buildResponseMapForDigitalization
     */
    public static Map<String, Object> buildResponseMapForDigitalization(JsonNode rootNode, ObjectMapper objectMapper) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();

        //  cable
        JsonNode cableArrayNode = rootNode.path(ApiConstants.LAYER_CODE_CABLE);
        List<DigitalizationResponseDTO.CableDto> cableResponseList = convertCableJsonToResponseDTO(cableArrayNode, objectMapper);
        responseMap.put(ApiConstants.LAYER_CODE_CABLE, cableResponseList);

        //  splitter
        JsonNode splitterArrayNode = rootNode.path(ApiConstants.LAYER_CODE_SPLITTER);
        List<DigitalizationResponseDTO.SplitterDto> splitterResponseList = convertSplitterJsonToResponseDTO(splitterArrayNode, objectMapper);
        responseMap.put(ApiConstants.LAYER_CODE_SPLITTER, splitterResponseList);

        //  sdu
        JsonNode sduArrayNode = rootNode.path(ApiConstants.LAYER_CODE_SDU);
        List<DigitalizationResponseDTO.SduDto> sduResponseList = convertSduJsonToResponseDTO(sduArrayNode, objectMapper);
        responseMap.put(ApiConstants.LAYER_CODE_SDU, sduResponseList);

        //  fat
        JsonNode fatArrayNode = rootNode.path(ApiConstants.LAYER_CODE_FAT);
        List<DigitalizationResponseDTO.FatDto> fatResponseList = convertFatJsonToResponseDTO(fatArrayNode, objectMapper);
        responseMap.put(ApiConstants.LAYER_CODE_FAT, fatResponseList);

        //        //  customer
//        JsonNode customerArrayNode = rootNode.path(ApiConstants.LAYER_CODE_CUSTOMER);
//        List<PolygonResponseDTO.CustomerDto> customerResponseList = convertCustomerJsonToResponseDTO(customerArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_CUSTOMER, customerResponseList);

        //  fdt
//        JsonNode fdtArrayNode = rootNode.path(ApiConstants.LAYER_CODE_FDT);
//        List<DigitalizationResponseDTO.FdtDto> fdtResponseList = convertFdtJsonToResponseDTO(fdtArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LAYER_CODE_FDT, fdtResponseList);

        //  jointClosure
//        JsonNode jointClosureArrayNode = rootNode.path(ApiConstants.LAYER_CODE_JOINT_CLOSURE);
//        List<DigitalizationResponseDTO.JointClosureDto> jointClosureResponseList = convertJointClosureJsonToResponseDTO(jointClosureArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LAYER_CODE_JOINT_CLOSURE, jointClosureResponseList);

        //  olt
//        JsonNode oltArrayNode = rootNode.path(ApiConstants.LAYER_CODE_OLT);
//        List<DigitalizationResponseDTO.OltDto> oltResponseList = convertOltJsonToResponseDTO(oltArrayNode, objectMapper);
//        responseMap.put(ApiConstants.LAYER_CODE_OLT, oltResponseList);

//        //  fdp
//        JsonNode fdpArrayNode = rootNode.path(ApiConstants.LAYER_CODE_FDP);
//        List<PolygonResponseDTO.FdpDto> fdpResponseList = convertFdpJsonToResponseDTO(fdpArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_FDP, fdpResponseList);
//
//        //  handhole
//        JsonNode handholeArrayNode = rootNode.path(ApiConstants.LAYER_CODE_HANDHOLE);
//        List<PolygonResponseDTO.HandholeDto> handholeResponseList = convertHandholeJsonToResponseDTO(handholeArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_HANDHOLE, handholeResponseList);
//
//        //  manhole
//        JsonNode manholeArrayNode = rootNode.path(ApiConstants.LAYER_CODE_MANHOLE);
//        List<PolygonResponseDTO.ManholeDto> manholeResponseList = convertManholeJsonToResponseDTO(manholeArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_MANHOLE, manholeResponseList);
//
//        //  pole
//        JsonNode poleArrayNode = rootNode.path(ApiConstants.LAYER_CODE_POLE);
//        List<PolygonResponseDTO.PoleDto> poleResponseList = convertPoleJsonToResponseDTO(poleArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_POLE, poleResponseList);
//
//        //  pop
//        JsonNode popArrayNode = rootNode.path(ApiConstants.LAYER_CODE_POP);
//        List<PolygonResponseDTO.PopDto> popResponseList = convertPopJsonToResponseDTO(popArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_POP, popResponseList);
//
//        //  duct
//        JsonNode ductArrayNode = rootNode.path(ApiConstants.LAYER_CODE_DUCT);
//        List<PolygonResponseDTO.DuctDto> ductResponseList = convertDuctJsonToResponseDTO(ductArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_DUCT, ductResponseList);
//
//        //  trench
//        JsonNode trenchArrayNode = rootNode.path(ApiConstants.LAYER_CODE_TRENCH);
//        List<PolygonResponseDTO.TrenchDto> trenchResponseList = convertTrenchJsonToResponseDTO(trenchArrayNode);
//        responseMap.put(ApiConstants.LAYER_CODE_TRENCH, trenchResponseList);

        return responseMap;
    }
}
