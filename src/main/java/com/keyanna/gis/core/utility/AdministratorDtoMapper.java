package com.keyanna.gis.core.utility;
// src/main/java/com/example/mapper/AdministratorDtoMapper.java


import com.keyanna.gis.core.dto.response.Administrator00DTO;

import org.locationtech.jts.geom.Geometry;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Single point of truth for converting raw JDBC rows into DTOs.
 * You can reuse these two methods anywhere.
 */
public class AdministratorDtoMapper {

    /**
     * Map one row (Object[]) into your DTO.
     */



        public static Administrator00DTO map(Object[] row) {
            Administrator00DTO dto = new Administrator00DTO();
            dto.setId(((Number)row[0]).longValue());
            dto.setAdm0Name((String)row[1]);
            dto.setAdm0Code((String)row[2]);

            // ▶︎ Directly assign the GeoJSON returned by ST_AsGeoJSON()
            dto.setGeom((String)row[3]);
            return dto;
        }

        /**
     * Map a list of rows into a list of DTOs.
     */
    public static List<Administrator00DTO> mapList(List<Object[]> rows) {
        return rows.stream()
                .map(AdministratorDtoMapper::map)
                .collect(Collectors.toList());
    }
}
