package com.keyanna.gis.core.utility.component;

import com.keyanna.gis.core.repository.SurveyAreaRepository;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKTReader;
import org.springframework.stereotype.Component;

@Component
public class SpatialValidator {

    private static final GeometryFactory geometryFactory = new GeometryFactory();
    private final SurveyAreaRepository surveyAreaRepository;

    public SpatialValidator(SurveyAreaRepository surveyAreaRepository) {
        this.surveyAreaRepository = surveyAreaRepository;
    }

    public void validatePointWithinSurveyArea(Integer surveyAreaId, double longitude, double latitude) {
        Point point = geometryFactory.createPoint(new Coordinate(longitude, latitude));
        point.setSRID(4326);

        boolean isInside = surveyAreaRepository.isPointInsideSurveyArea(surveyAreaId, longitude, latitude);

        if (!isInside) {
            throw new RuntimeException("Point is outside the survey area.");
        }
    }

    public boolean isValidGeometry(String wkt) {
        try {
            WKTReader reader = new WKTReader(geometryFactory);
            Geometry geometry = reader.read(wkt);
            return geometry.isValid();
        } catch (Exception e) {
            // Handle parsing errors (e.g., malformed WKT)
            return false;
        }
    }

}
