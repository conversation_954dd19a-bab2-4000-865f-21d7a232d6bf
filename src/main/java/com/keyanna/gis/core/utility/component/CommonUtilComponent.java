package com.keyanna.gis.core.utility.component;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.Inventory;
import com.keyanna.gis.core.dto.request.LayerInventoryMappingRequestDTO;
import com.keyanna.gis.core.model.LayerImageMapping;
import com.keyanna.gis.core.repository.LayerImageMappingRepository;
import com.keyanna.gis.core.service.LayerInventoryMappingService;
import com.keyanna.gis.core.service.NetworkPortService;
import com.keyanna.gis.core.service.OltTrayService;
import com.keyanna.gis.core.service.impl.ImageServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class CommonUtilComponent {

    private static final Logger logger = LoggerFactory.getLogger(CommonUtilComponent.class);
    private final LayerInventoryMappingService layerInventoryMappingService;
    private final LayerImageMappingRepository layerImageMappingRepository;
    private final ImageServiceImpl imageServiceImpl;
    private final OltTrayService oltTrayService;
    private final NetworkPortService networkPortService;

    public CommonUtilComponent(LayerInventoryMappingService layerInventoryMappingService, LayerImageMappingRepository layerImageMappingRepository, ImageServiceImpl imageServiceImpl, OltTrayService oltTrayService, NetworkPortService networkPortService) {
        this.layerInventoryMappingService = layerInventoryMappingService;
        this.layerImageMappingRepository = layerImageMappingRepository;
        this.imageServiceImpl = imageServiceImpl;
        this.oltTrayService = oltTrayService;
        this.networkPortService = networkPortService;
    }

    /**
     * This is Common method Can use in other layers create api
     *
     * @param inventoryList
     * @param layerId
     * @param layerCode
     */
    public void saveLayerInventoryMappingData(List<Inventory> inventoryList, Integer layerId, String layerCode,String status) {
        List<LayerInventoryMappingRequestDTO> mappingsDtoList = inventoryList.stream().map(inventoryObj -> {

            LayerInventoryMappingRequestDTO mappingDto = new LayerInventoryMappingRequestDTO();
            mappingDto.setLayerId(layerId);
            mappingDto.setLayerCode(layerCode);
            mappingDto.setParentParamId(inventoryObj.getParentParamId());
            mappingDto.setParamId(inventoryObj.getParamId());
            mappingDto.setParamName(inventoryObj.getParamName());
            mappingDto.setParamValue(inventoryObj.getParamValue());
            mappingDto.setIsMandatory(inventoryObj.getIsMandatory());
            mappingDto.setIsAccessory(inventoryObj.getIsAccessory());
            mappingDto.setQuantity(inventoryObj.getQuantity() != null ? inventoryObj.getQuantity() : 0);
            mappingDto.setCreatedOn(LocalDateTime.now());
            mappingDto.setIsConfiguration(inventoryObj.getIsConfiguration());
            mappingDto.setStatus(status);
            return mappingDto;
        }).toList();

        // Save all mappings
        layerInventoryMappingService.createMultipleLayerInventoryMappings(mappingsDtoList);

        //  TODO : TODOR change here for olt tray and olt tray port entries
        // Filter mappings with isConfiguration = true
        List<LayerInventoryMappingRequestDTO> configurationMappings = mappingsDtoList.stream()
                .filter(dto -> Boolean.TRUE.equals(dto.getIsConfiguration()))
                .toList();

        // Call another method with the filtered list for olt
        if (!configurationMappings.isEmpty()) {
            if (layerCode.equals(ApiConstants.LAYER_CODE_OLT)) {
                createOltTrayAndPortMappingsUtilComponent(configurationMappings, layerId);
                createOltTrayNetworkPortsEntryUtilComponent(configurationMappings, layerId, layerCode);
            }
            //  TODO add below code when required
//            if (layerCode.equals(ApiConstants.LAYER_CODE_ODF)) {
//            }
        }
    }

    private void createOltTrayAndPortMappingsUtilComponent(List<LayerInventoryMappingRequestDTO> layerInventoryMappingRequestDTOList, Integer layerId) {
        // Find the mapping with paramName "Olt Tray Capacity"
        Optional<LayerInventoryMappingRequestDTO> trayCapacityOpt = layerInventoryMappingRequestDTOList.stream()
                .filter(dto -> ApiConstants.OLT_TRAY_CAPACITY.equalsIgnoreCase(dto.getParamName()))
                .findFirst();

        trayCapacityOpt.ifPresent(layerInventoryMappingRequestDTO -> oltTrayService.createOltTrayAndPortMappings(layerInventoryMappingRequestDTO, layerId));
    }

    private void createOltTrayNetworkPortsEntryUtilComponent(List<LayerInventoryMappingRequestDTO> layerInventoryMappingRequestDTOList, Integer oltId, String layerName) {
        // Find the mapping with paramName "Olt Tray Port Capacity"
        Optional<LayerInventoryMappingRequestDTO> trayCapacityOpt = layerInventoryMappingRequestDTOList.stream()
                .filter(dto -> ApiConstants.OLT_TRAY_PORT_CAPACITY.equalsIgnoreCase(dto.getParamName()))
                .findFirst();

        trayCapacityOpt.ifPresent(layerInventoryMappingRequestDTO -> networkPortService.createOltTrayNetworkPortEntry(layerInventoryMappingRequestDTO, oltId));
    }

    /**
     * Deletes accessory mappings for a given layer (e.g., Pole, Fat, etc).
     *
     * @param layerId the primary key of the layer (e.g., ID)
     */
    public void deleteLayerInventoryMappingData(Integer layerId, String layerName) {
        try {
            layerInventoryMappingService.deleteByLayerIdAndLayerName(layerId, layerName);
        } catch (Exception e) {
            logger.warn("No mappings found or error during mapping deletion for layerId: ");
        }
    }

    public void removeMappingsUsingLayerIdAndLayerName(Integer layerId, String layerName) {
        layerInventoryMappingService.removeMappingsUsingLayerIdAndLayerName(layerId, layerName);
    }

    public void deleteAllAssociatedImageWithNetworkElement(Integer neId, String layerType) {
        try {
            List<LayerImageMapping> dataList = layerImageMappingRepository.findAllByLayerIdAndLayerCode(neId, layerType);
            if (!dataList.isEmpty()) {
                layerImageMappingRepository.deleteAll(dataList);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public void saveLayerImageMappingData(List<MultipartFile> imageFiles, String layerName, Integer layerId) {
        try {
            List<LayerImageMapping> mappingList = new ArrayList<>();
            if (imageFiles != null && !imageFiles.isEmpty()) {
                for (MultipartFile imageFile : imageFiles) {
                    if (imageFile != null && !imageFile.isEmpty()) {
                        String filePath = imageServiceImpl.uploadImage(imageFile, layerName);
                        LayerImageMapping mapping = new LayerImageMapping();
                        mapping.setFileName(filePath);
                        mapping.setLayerCode(layerName);
                        mapping.setLayerId(layerId);
                        mappingList.add(mapping);
                    }
                }
            }
            layerImageMappingRepository.saveAll(mappingList);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

}
