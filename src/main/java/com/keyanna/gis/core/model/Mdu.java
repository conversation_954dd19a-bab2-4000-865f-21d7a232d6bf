package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Setter
@Getter
@Table(name = "ne_mdu")
public class Mdu {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    public String name;
    public String address;
    public int home_passes;
    public int floors;
    public int towers;

    public String category = ApiConstants.DEFAULT_STATUS_RESIDENTIAL;
    public String status;
    public String tenancy = ApiConstants.DEFAULT_STATUS_OWNED;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "custom_id", unique = true)
    private String custom_id;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "fat_no")
    private String fatNo;

    @Column(name = "fdt_no")
    private String fdtNo;

    @Column(name = "optical_level")
    private String opticalLevel;

//    @Column(name = "photo")
//    private String photo;

    @Column(name = "street_name")
    private String streetName;

    @Column(name = "riser")
    private String riser;

    @Column(name = "olt_name")
    private String oltName;
}
