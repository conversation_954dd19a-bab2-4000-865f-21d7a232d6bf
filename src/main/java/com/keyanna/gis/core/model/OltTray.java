package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "olt_tray")
public class OltTray {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "olt_id", nullable = false)
    private Integer oltId;

    @Column(name = "tray_name")
    private String trayName;

    @Column(name = "tray_status", nullable = false)
    private String trayStatus;

    @Column(name = "port_capacity")
    private Integer portCapacity;

    @Column(name = "description")
    private String description;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

}
