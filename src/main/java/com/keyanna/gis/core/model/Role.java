package com.keyanna.gis.core.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.keyanna.gis.core.data.Auditable;
import com.keyanna.gis.core.data.IBaseData;
import jakarta.persistence.*;

import lombok.*;

//import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tblmroles")
public class Role extends Auditable implements IBaseData<Long> {

    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "roleid")
    private Long id; // not roleId

    @Column(nullable = false, length = 40)
    private String rolename;


    @Column(name = "rstatus", nullable = false, length = 100)
    private String status;

    @Column(name= "sysrole", columnDefinition = "Boolean default false")
    private Boolean sysRole = false;

//We are Having those property already in Auditable
//    @CreationTimestamp
//    @DiffIgnore
//    @Column(name = "created_on", nullable = false, updatable = false)
//    private LocalDateTime createdate;
//
//    @UpdateTimestamp
//    @DiffIgnore
//    @Column(name = "lastmodified_on")
//    private LocalDateTime updatedate;

   /* @JsonBackReference
    @DiffIgnore
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, mappedBy = "roles")
    @ToString.Exclude
    private Set<StaffUser> staffusers = new HashSet<>();*/


//    @DiffIgnore
//    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, orphanRemoval = true)
//    @OrderBy("id asc")
//    private List<CustomACLEntry> aclEntry = new ArrayList<>();

    @JsonManagedReference
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("id asc")
    private List<RoleACLEntry> roleAclEntry = new ArrayList<>();

    @Column(name = "is_delete", columnDefinition = "Boolean default false", nullable = false)
    private Boolean isDelete;

    @Column(name = "MVNOID", nullable = false, length = 40, updatable = false)
    private Integer mvnoId;

    @Column(name = "lcoid", nullable = false, length = 40, updatable = false)
    private Integer lcoId;

    @Column(name = "product")
    private String product;

    public Role(String name, LocalDateTime createdate, LocalDateTime updatedate, String status, String product) {
        super();
        this.rolename = name;
        this.status = status;
        this.product = product;

    }


    public Role(Long id) {
        this.id = id;
    }

    @JsonIgnore
    @Override
    public Long getPrimaryKey() {
        return id;
    }

    @JsonIgnore
    @Override
    public void setDeleteFlag(boolean deleteFlag) {
        this.isDelete = deleteFlag;
    }

    @JsonIgnore
    @Override
    public boolean getDeleteFlag() {
        return isDelete;
    }

    @Override
    public void setBuId(Long buId) {

    }
    public Role(Role role) {

        this.rolename = role.getRolename();
        this.status = role.getStatus();
        this.sysRole =role.getSysRole();
        this.id = role.getId();
        this.product=role.getProduct();

    }
}
