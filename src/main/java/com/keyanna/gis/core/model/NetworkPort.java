package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "network_port")
public class NetworkPort {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "layer_id", nullable = false)
    private Integer layerId;

    @Column(name = "layer_type", nullable = false)
    private String layerType;

    @Column(name = "port_number", nullable = false)
    private Integer portNumber;

    @Column(name = "port_type", nullable = false)
    private String portType;

    @Column(name = "port_status", nullable = false)
    private String portStatus;

    @Column(name = "description")
    private String description;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

}
