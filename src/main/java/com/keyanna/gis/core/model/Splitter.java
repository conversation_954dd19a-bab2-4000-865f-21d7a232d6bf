package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "ne_splitter", schema = "public")
public class Splitter extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToOne
    @JoinColumn(name = "specification_id", referencedColumnName = "id")
    private SplitterSpecification splitterSpecification;

    /**
     * Check if this JsonSerialize and JsonDeserialize affects create update or get api
     */
    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @Column(name = "status")
    private String status;

    @Column(name = "parent_ne_id")
    private Integer parentNeId;

    @Column(name = "parent_ne_type")
    private String parentNeType;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;


}
