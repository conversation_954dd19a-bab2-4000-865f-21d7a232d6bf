package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "network_element_connection")
public class NetworkElementConnection {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "cable_id")
    private Integer cableId;

    @Column(name = "core_number")
    private Integer coreNumber;

    @Column(name = "from_layer_id")
    private Integer fromLayerId;

    @Column(name = "from_layer_type")
    private String fromLayerType;

    @Column(name = "from_port_info")
    private String fromPortInfo;

    @Column(name = "to_layer_id")
    private Integer toLayerId;

    @Column(name = "to_layer_type")
    private String toLayerType;

    @Column(name = "to_port_info")
    private String toPortInfo;

    @Column(name = "closure_id")
    private Integer closureId;

    @Column(name = "closure_type")
    private String closureType;

    @Column(name = "connection_type")
    private String connectionType;

    @Column(name = "description")
    private String description;

    @Column(name = "created_on")
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "survey_area_id")
    private Integer surveyAreaId;

    @Column(name = "mvno_id")
    private Integer mvnoId;

}