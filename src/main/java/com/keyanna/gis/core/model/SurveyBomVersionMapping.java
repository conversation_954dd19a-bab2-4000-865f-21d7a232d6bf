package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "survey_bom_version_mapping")
public class SurveyBomVersionMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    // Foreign keys
    @Column(name = "survey_id", nullable = false)
    private Integer surveyId;

    @Column(name = "bom_version_id", nullable = false)
    private Integer bomVersionId;

    @Column(name = "layer_inventory_mapping_id", nullable = false)
    private Integer layerInventoryMappingId;

    // Optional foreign keys (nullable = true by default)
    @Column(name = "ne_cable_id")
    private Integer neCableId;

    @Column(name = "ne_cdu_id")
    private Integer neCduId;

    @Column(name = "ne_customer_id")
    private Integer neCustomerId;

    @Column(name = "ne_duct_id")
    private Integer neDuctId;

    @Column(name = "ne_fat_id")
    private Integer neFatId;

    @Column(name = "ne_fdp_id")
    private Integer neFdpId;

    @Column(name = "ne_fdt_id")
    private Integer neFdtId;

    @Column(name = "ne_handhole_id")
    private Integer neHandholeId;

    @Column(name = "ne_joint_closure_id")
    private Integer neJointClosureId;

    @Column(name = "ne_manhole_id")
    private Integer neManholeId;

    @Column(name = "ne_mdu_id")
    private Integer neMduId;

    @Column(name = "ne_pole_id")
    private Integer nePoleId;

    @Column(name = "ne_pop_id")
    private Integer nePopId;

    @Column(name = "ne_sdu_id")
    private Integer neSduId;

    @Column(name = "ne_splitter_id")
    private Integer neSplitterId;

    @Column(name = "ne_street_id")
    private Integer neStreetId;

    @Column(name = "ne_trench_id")
    private Integer neTrenchId;

    @Column(name = "ne_olt_id")
    private Integer neOltId;

    @Column(name = "ne_odf_id")
    private Integer neOdfId;

    // Audit fields
    @Column(name = "created_by", length = 100)
    private String createdBy;

    @Column(name = "created_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdAt;

    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    @Column(name = "updated_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime updatedAt;

    @Column(name = "status", length = 50)
    private String status;

    // Getters and Setters (can be generated via Lombok if preferred)
}
