package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.deserialize.LineStringDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryLineStringSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.LineString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "ne_cable", schema = "public")
public class Cable extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * Optionally, annotate with @Column(insertable = false) if you never want Java to set it:
     *
     * @Column(insertable = false)
     */
    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "mounting_type", nullable = false)
    private String mountingType;

    @Column(name = "status")
    private String status ;

    @ManyToOne
    @JoinColumn(name = "cable_type_id", referencedColumnName = "id")
    private LookupCable lookupCableType;

    @ManyToOne
    @JoinColumn(name = "specification_id", referencedColumnName = "id")
    private CableSpecification cableSpecification;

    @Column(name = "measured_length_m", precision = 10, scale = 2)
    private BigDecimal measuredLengthM;

    @Column(name = "gis_length_m", precision = 10, scale = 2)
    private BigDecimal gisLengthM;

    @Column(name = "installation_date")
    private LocalDate installationDate;

    @Column(name = "parent_ne_id")
    private Integer parentNeId;

    @Column(name = "parent_ne_type")
    private String parentNeType;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @JsonSerialize(using = GeometryLineStringSerializer.class)
    @JsonDeserialize(using = LineStringDeserializer.class)
    @Column(name = "geom", nullable = false)
    private LineString geom;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;
}
