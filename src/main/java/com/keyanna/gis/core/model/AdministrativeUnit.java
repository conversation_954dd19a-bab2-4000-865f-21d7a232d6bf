package com.keyanna.gis.core.model;

import jakarta.persistence.*;

import java.util.List;

@Entity
@Table(name = "administrative_02")
public class AdministrativeUnit {

    @Id
    private Long adm2Code;

    private String name;

    @ManyToOne
    @JoinColumn(name = "adm1_code")
    private AdministrativeUnit parent;

    @OneToMany(mappedBy = "parent")
    private List<AdministrativeUnit> children;

    public Long getAdm2Code() {
        return adm2Code;
    }

    public void setAdm2Code(Long adm2Code) {
        this.adm2Code = adm2Code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AdministrativeUnit getParent() {
        return parent;
    }

    public void setParent(AdministrativeUnit parent) {
        this.parent = parent;
    }

    public List<AdministrativeUnit> getChildren() {
        return children;
    }

    public void setChildren(List<AdministrativeUnit> children) {
        this.children = children;
    }
// Getters and setters
}

