package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.LineStringDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryLineStringSerializer;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.locationtech.jts.geom.LineString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Setter
@Getter
@Entity
@NoArgsConstructor  // <- This adds a no-argument constructor
@AllArgsConstructor
@Table(name = "ne_street")
public class Street {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    public String name;

    @Column(name = "type")
    public String type;

    @Column(name = "code")
    public String code;

    @Column(name = "length_m")
    public BigDecimal lengthM;

    @JsonSerialize(using = GeometryLineStringSerializer.class)
    @JsonDeserialize(using = LineStringDeserializer.class)
    @Column(name = "geom", nullable = false)
    public LineString geom;

    @Column(name = "one_way")
    public Boolean oneWay;

    @Column(name = "surface")
    public String surface;

    @Column(name = "status")
    public String status;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @CreationTimestamp
    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

}
