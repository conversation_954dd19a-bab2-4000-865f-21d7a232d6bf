package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Setter
@Getter
@Table(name = "ne_pole")
public class Pole {

//    public String status = ApiConstants.POLE_STATUS; // commented as not needed for now
//    public String material = ApiConstants.POLE_MATERIAL; // commented as not needed for now
//    public String poleType = ApiConstants.POLE_TYPE; // commented as not needed for now
//    public String ownership = ApiConstants.POLE_ownership; // commented as not needed for now

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;


    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(nullable = false)
    private String name;

    @Column(name = "height_m", precision = 10, scale = 2)
    private BigDecimal heightM;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "survey_area_id", referencedColumnName = "id", nullable = false)
//    private SurveyArea surveyArea;

    @Column(name = "survey_area_id")
    private Integer surveyAreaId;

    @Column(name = "status")
    private String status;


    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "adss")
    private String adss;

    @Column(name = "upb")
    private String upb;

    @Column(name = "j_hook")
    private String jHook;

    @Column(name = "anchor")
    private String anchor;

    @Column(name = "cable")
    private String cable;

    @Column(name = "slack")
    private String slack;

    @Column(name = "jb")
    private String jb;

    @Column(name = "fat")
    private String fat;

    @Column(name = "guy_grip")
    private String guyGrip;

    @Column(name = "photo")
    private String photo;

    @Column(name = "photo2")
    private String photo2;

    @Column(name = "photo3")
    private String photo3;

    @Column(name = "pole_size_type")
    private String poleSizeType;
}