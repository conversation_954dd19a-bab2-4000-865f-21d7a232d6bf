package com.keyanna.gis.core.model.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LinearRing;
import org.locationtech.jts.geom.Polygon;

import java.io.IOException;

public class GeometryPolygonSerializer extends StdSerializer<Polygon> {

    public GeometryPolygonSerializer() {
        super(Polygon.class);
    }

    @Override
    public void serialize(Polygon polygon, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("type", "Polygon");
        gen.writeFieldName("coordinates");
        gen.writeStartArray(); // Outer array for the polygon (can have holes)

        // Serialize exterior ring
        writeCoordinatesArray(gen, polygon.getExteriorRing().getCoordinates());

        // Serialize holes (if any)
        for (int i = 0; i < polygon.getNumInteriorRing(); i++) {
            writeCoordinatesArray(gen, polygon.getInteriorRingN(i).getCoordinates());
        }

        gen.writeEndArray(); // End of "coordinates"
        gen.writeEndObject(); // End of polygon object
    }

    private void writeCoordinatesArray(JsonGenerator gen, Coordinate[] coords) throws IOException {
        gen.writeStartArray();
        for (Coordinate coord : coords) {
            gen.writeStartArray();
            gen.writeNumber(coord.getX());
            gen.writeNumber(coord.getY());
            gen.writeEndArray();
        }
        gen.writeEndArray();
    }
}
