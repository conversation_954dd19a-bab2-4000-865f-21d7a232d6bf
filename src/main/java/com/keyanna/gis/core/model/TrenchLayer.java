package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.LineStringDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryLineStringSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.LineString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Setter
@Getter
@Table(name = "ne_trench")
public class TrenchLayer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    public String name;
    public BigDecimal width_m;
    public BigDecimal depth_m;

    @Column(name = "length_m", precision = 10, scale = 2)
    public BigDecimal measuredLengthM;

    public String status;
    public String owner;
    public String contractor;
    public String related_assets;
    public String remarks;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @JsonSerialize(using = GeometryLineStringSerializer.class)
    @JsonDeserialize(using = LineStringDeserializer.class)
    @Column(name = "geom", nullable = false)
    private LineString geom;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

}
