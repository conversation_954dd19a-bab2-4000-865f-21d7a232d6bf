package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "ne_joint_closure")
public class JointClosure {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false, unique = true)
    private UUID publicId;

    @Column(name = "custom_id", unique = true)
    private String custom_id;

    private String name;

    @Column(name = "joint_type")
    private String joint_type;

    private Integer capacity;

    @Column(name = "mounted_in")
    private String mounted_in;

    @Column(name = "adm1_id")
    private Integer adm1_id;

    @Column(name = "adm2_id")
    private Integer adm2_id;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    private String status;

    @Column(name = "created_by")
    private Long created_by;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "parent_ne_id")
    private Long parentNeId;

    @Column(name = "parent_ne_type")
    private String parentNeType;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;
}
