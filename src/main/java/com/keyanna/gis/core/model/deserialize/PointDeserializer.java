package com.keyanna.gis.core.model.deserialize;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKTReader;

import java.io.IOException;

public class PointDeserializer extends JsonDeserializer<Point> {

    private static final GeometryFactory geometryFactory = new GeometryFactory();

    @Override
    public Point deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String wkt = p.getText();
        try {
            return (Point) new WKTReader(geometryFactory).read(wkt);
        } catch (Exception e) {
            throw new IOException("Invalid WKT geometry: " + wkt, e);
        }
    }
}
