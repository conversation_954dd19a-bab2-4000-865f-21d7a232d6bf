package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tbltstaffrolerel")
@Getter
@Setter
@NoArgsConstructor
public class StaffRoleRel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "staffrolerelid")
    private Long staffrolerelid;

    @Column(name = "staffid", nullable = false)
    private Integer staffid;

    @Column(name = "roleid", nullable = false)
    private Long roleid;

    @Column(name = "mvnoid", nullable = false)
    private Integer mvnoId;

    // Optionally: Add constructors or toString() if needed
}
