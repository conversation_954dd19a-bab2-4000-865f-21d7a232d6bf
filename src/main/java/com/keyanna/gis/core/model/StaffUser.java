package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Entity
@Table(name = "tblmstaffuser")
@Data
public class StaffUser {

    @Id
    @Column(name = "staffid")
    private Integer staffid;

    @Column(name = "username")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "firstname")
    private String firstname;

    @Column(name = "lastname")
    private String lastname;

    @Column(name = "sstatus")
    private String status;

    @Column(name = "last_login_time")
    private LocalDateTime last_login_time;

    @Column(name = "partnerid")
    private Integer partnerid;

    @Column(name = "is_delete")
    private Boolean isDelete;

    @Column(name = "mvnoid")
    private Integer mvnoId;

    @Column(name = "branchid")
    private Integer branchId;

    @Column(name = "createbyname")
    private String createbyname;

    @Column(name = "updatebyname")
    private String updatebyname;

    @Column(name = "createdbystaffid")
    private Integer createdbystaffid;

    @Column(name = "lastmodifiedbystaffid")
    private Integer lastmodifiedbystaffid;

    @Column(name = "createdate")
    private LocalDateTime createdate;

    @Column(name = "lastmodifieddate")
    private LocalDateTime lastModifiedDate;

//    @Column(name = "roleid")
//    private String roleid; // You may later change to @ManyToMany

    @Column(name = "roleid")
    private Long roleid;


    @Column(name = "service_area_id")
    private Integer serviceAreaId;

    @Column(name = "parent_staff_id")
    private Integer parentStaffId;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "total_collected")
    private Double totalCollected;

    @Column(name = "total_transferred")
    private Double totalTransferred;

    @Column(name = "available_amount")
    private Double availableAmount;

    @Column(name = "lcoid")
    private Integer lcoId;

    @Column(name = "hrms_id")
    private String hrmsId;

    @Column(name = "profile_image")
    private byte[] profileImage;

    @Column(name = "department")
    private String department;

    @Column(name = "oldpassword1")
    private String oldpassword1;

    @Column(name = "oldpassword2")
    private String oldpassword2;

    @Column(name = "oldpassword3")
    private String oldpassword3;

    @Column(name = "otp")
    private String otp;

    @Column(name = "otpvalidate")
    private LocalDateTime otpvalidate;

    @Column(name = "sysstaff")
    private Boolean sysstaff;

    @Column(name = "businessunitid")
    private Integer businessunitid;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "failcount")
    private Integer failcount;

    @Column(name = "access_level_group_name")
    private String tacacsAccessLevelGroup;

    @Column(name = "mvno_deactivation_flag")
    private Boolean mvnoDeactivationFlag;

    @Column(name = "uuid")
    private String uuid;

    @Column(name = "is_password_expired")
    private Boolean isPasswordExpired;

    @Column(name = "password_date")
    private LocalDateTime passwordDate;
}
