package com.keyanna.gis.core.model.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.locationtech.jts.geom.Point;

import java.io.IOException;

public class GeometryPointSerializer extends StdSerializer<Point> {
    public GeometryPointSerializer() {
        super(Point.class);
    }


    @Override
    public void serialize(Point value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeFieldName("type");
        gen.writeString("Point");
        gen.writeFieldName("coordinates");
        gen.writeArray(new double[]{value.getX(), value.getY()}, 0, 2);
        gen.writeEndObject();
    }


}

