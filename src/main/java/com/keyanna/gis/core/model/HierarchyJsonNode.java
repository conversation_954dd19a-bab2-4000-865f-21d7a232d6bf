package com.keyanna.gis.core.model;

import java.util.ArrayList;
import java.util.List;

public class HierarchyJsonNode {

    private Integer id;             // The entity ID (from any table)
    private String type;         // E.g., "olt", "odf", "fdc", etc.
    private List<HierarchyJsonNode> children = new ArrayList<>(); // Recursively holds all child nodes

    public HierarchyJsonNode(Integer id, String type) {
        this.id = id;
        this.type = type;
    }

    // Add a child node
    public void addChild(HierarchyJsonNode child) {
        this.children.add(child);
    }

    // Getters & setters (required for JSON serialization)
    public Integer getId() { return id; }
    public String getType() { return type; }
    public List<HierarchyJsonNode> getChildren() { return children; }

    public void setId(Integer id) { this.id = id; }
    public void setType(String type) { this.type = type; }
    public void setChildren(List<HierarchyJsonNode> children) { this.children = children; }
}
