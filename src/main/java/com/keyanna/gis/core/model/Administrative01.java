package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

@Setter
@Getter
@Entity
@NoArgsConstructor  // <- This adds a no-argument constructor
@Table(name = "administrative_01")
public class Administrative01 {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "adm1_name")
    public String adm1Name;
    @Column(name = "adm1_code")
    public String adm1Code;
    @Column(name = "geom", columnDefinition = "geometry(MULTIPOLYGON,4326)")
    private MultiPolygon geom;



}
