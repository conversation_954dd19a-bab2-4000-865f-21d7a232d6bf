package com.keyanna.gis.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Setter
@Getter
@Table(name = "ne_handhole")
public class Handhole {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Integer id;

    @Column(name = "public_id", nullable = false)
    @JsonProperty("publicId")
    private UUID publicId;

    @Column(name = "custom_id", unique = true)
    @JsonProperty("customId")
    private String customId;

    @Column(name = "name", nullable = false)
    @JsonProperty("name")
    private String name;

    @Column(name = "hole_size")
    @JsonProperty("holeSize")
    private String holeSize;

    @Column(name = "access_type", nullable = false)
    @JsonProperty("accessType")
    private String accessType;

    @Column(name = "material")
    @JsonProperty("material")
    private String material;

    @Column(name = "adm1_id")
    @JsonProperty("adm1Id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    @JsonProperty("adm2Id")
    private Integer adm2Id;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    @JsonProperty("geom")
    private Point geom;

    @Column(name = "status")
    @JsonProperty("status")
    private String status;

    @Column(name = "created_by")
    @JsonProperty("createdBy")
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    @JsonProperty("createdOn")
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    @JsonProperty("modifiedOn")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    @JsonProperty("modifiedBy")
    private Long modifiedBy;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;
}