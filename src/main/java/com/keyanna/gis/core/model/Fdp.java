package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "ne_fdp", schema = "public")
public class Fdp extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * Optionally, annotate with @Column(insertable = false) if you never want Java to set it:
     *
     * @Column(insertable = false)
     */
    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "custom_id", unique = true)
    private String customId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "port", nullable = false)
    private Integer port;

    @ManyToOne
    @JoinColumn(name = "fdp_type_id", referencedColumnName = "id")
    private LookupFdpTypes lookupFdpType;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    /**
     * Check if this JsonSerialize and JsonDeserialize affects create update or get api
     */
    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @Column(name = "status")
    private String status ;

    @Column(name = "pop_id")
    private Integer popId;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;


@Column(name = "survey_area_id", nullable = false)
private Integer surveyAreaId;
}
