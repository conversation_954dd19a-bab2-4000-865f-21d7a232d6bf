package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.locationtech.jts.geom.MultiPolygon;

@Entity
@NoArgsConstructor  // <- This adds a no-argument constructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "administrative_02")
public class Administrative02 {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "adm2_name", nullable = false)
    public String adm2Name;
    @Column(name = "adm2_code", nullable = false)
    public String adm2Code;
    @Column(name = "geom", columnDefinition = "geometry(MULTIPOLYGON,4326)")
    private MultiPolygon geom;

}
