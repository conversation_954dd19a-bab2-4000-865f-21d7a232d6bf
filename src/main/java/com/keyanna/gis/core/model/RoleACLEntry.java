package com.keyanna.gis.core.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.keyanna.gis.core.model.Role;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Setter
@Getter
@Table(name = "tblmaclentry")
@NoArgsConstructor


public class RoleACLEntry {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "roleid")
    @ToString.Exclude
    public com.keyanna.gis.core.model.Role role;

    @Column(nullable = false)
    public String code;

    @Column(nullable = false)
    public int menuid;

    @Transient
    public Long roleId;

    @Column
    public String product;

    public RoleACLEntry(Role role, String code, int menuid, Long id, String product) {
        this.id = id;
        this.role = role;
        this.code = code;
        this.menuid = menuid;
        this.product = product;
    }
}
