package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "ne_olt")
public class Olt {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false, columnDefinition = "uuid DEFAULT gen_random_uuid()")
    private UUID publicId;

    @Column(name = "custom_id")
    private String customId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "optical_level", nullable = false)
    private String opticalLevel;

    @Column(name = "up_link_protection", nullable = false)
    private String upLinkProtection;

    @Column(name = "power_backup", nullable = false)
    private String powerBackup;

    @Column(name = "vendor")
    private String vendor;

    @Column(name = "model")
    private String model;

    @Column(name = "slots")
    private Integer slots;

    @Column(name = "active_ports")
    private Integer activePorts;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom; // Requires JTS and Hibernate Spatial

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;


}
