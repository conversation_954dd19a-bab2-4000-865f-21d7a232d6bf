package com.keyanna.gis.core.model.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;

import java.io.IOException;

public class GeometryLineStringSerializer extends StdSerializer<LineString> {

    public GeometryLineStringSerializer() {
        super(LineString.class);
    }

    @Override
    public void serialize(LineString lineString, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("type", "LineString");
        gen.writeFieldName("coordinates");

        // Write coordinates as an array of [x, y]
        gen.writeStartArray();
        for (Coordinate coord : lineString.getCoordinates()) {
            gen.writeStartArray();
            gen.writeNumber(coord.getX());
            gen.writeNumber(coord.getY());
            gen.writeEndArray();
        }
        gen.writeEndArray();

        gen.writeEndObject();
    }
}
