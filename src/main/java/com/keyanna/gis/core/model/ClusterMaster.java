package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PolygonDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPolygonSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Polygon;

import java.time.Instant;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "cluster_master", schema = "public")
public class ClusterMaster extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "status")
    private String status;

    /**
     * Check if this JsonSerialize and JsonDeserialize affects create update or get api
     */
    @JsonSerialize(using = GeometryPolygonSerializer.class)
    @JsonDeserialize(using = PolygonDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Polygon geom;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;
}
