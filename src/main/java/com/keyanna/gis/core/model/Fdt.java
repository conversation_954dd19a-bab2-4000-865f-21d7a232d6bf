package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PointDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPointSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Setter
@Getter
@Table(name = "ne_fdt")
public class Fdt {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    public String name;
    public String address;
    public Long capacity;
    public String status;

    @JsonSerialize(using = GeometryPointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Point geom;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "custom_id", unique = true)
    private String custom_id;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @Column(name = "parent_ne_id")
    private Long parentNeId;

    @Column(name = "parent_ne_type")
    private String parentNeType;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "survey_area_id", referencedColumnName = "id", nullable = false)
//    private SurveyArea surveyArea;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "core_number", nullable = false)
    private Integer coreNumber;

    @Column(name = "card_slot_number", nullable = false)
    private Integer cardSlotNumber;

    @Column(name = "pon_port", nullable = false)
    private Integer ponPort;
}
