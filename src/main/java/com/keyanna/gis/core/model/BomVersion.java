package com.keyanna.gis.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

@Entity
@Table(name = "bom_version",
        uniqueConstraints = @UniqueConstraint(columnNames = {"bom_master_id", "version_no"}))
@Data
@NoArgsConstructor
public class BomVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // ManyToOne mapping to bom_master
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bom_master_id", nullable = false)
    private BomMaster bomMaster;

    @Column(name = "version_no", nullable = false)
    private Integer versionNo;

    @Column(name = "file_name", columnDefinition = "TEXT")
    private String fileName;

    @Column(name = "file_path", columnDefinition = "TEXT")
    private String filePath;

    @Column(name = "created_by")
    private Long createdBy;

    @Schema(hidden = true)
    @CreatedDate
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Schema(hidden = true)
    @LastModifiedDate
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "is_active")
    private Boolean isActive ;

    @Column(name = "mvno_id")
    private Integer mvnoId;
}
