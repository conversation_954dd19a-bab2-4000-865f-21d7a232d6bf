package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.PolygonDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryPolygonSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Polygon;

import java.time.LocalDate;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "survey_area", schema = "public")
public class SurveyArea extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "public_id", nullable = false)
    private UUID publicId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @ManyToOne
    @JoinColumn(name = "survey_status_id", referencedColumnName = "id")
    private LookupSurveyStatus lookupSurveyStatus;

    @ManyToOne
    @JoinColumn(name = "survey_stage_id", referencedColumnName = "id")
    private LookupSurveyStage lookupSurveyStage;

    /**
     * Check if this JsonSerialize and JsonDeserialize affects create update or get api
     */
    @JsonSerialize(using = GeometryPolygonSerializer.class)
    @JsonDeserialize(using = PolygonDeserializer.class)
    @Column(name = "geom", nullable = false)
    private Polygon geom;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "survey_start_date")
    private LocalDate surveyStartDate;

    @Column(name = "survey_end_date")
    private LocalDate surveyEndDate;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;
}
