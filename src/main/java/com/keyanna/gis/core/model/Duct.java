package com.keyanna.gis.core.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.keyanna.gis.core.model.deserialize.LineStringDeserializer;
import com.keyanna.gis.core.model.serialize.GeometryLineStringSerializer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.annotations.UpdateTimestamp;
import org.locationtech.jts.geom.LineString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "ne_duct")
public class Duct {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private UUID publicId;

    private String customId;

    private String  name;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    private String material;

    @Column(name = "diameter_mm", precision = 10, scale = 2)
    private BigDecimal diameterMm;

    @Column(name = "length_m", precision = 10, scale = 2)
    private BigDecimal measuredLengthM;

    private String status;

    private LocalDate installDate;

    private String owner;

    @JsonSerialize(using = GeometryLineStringSerializer.class)
    @JsonDeserialize(using = LineStringDeserializer.class)
    private LineString geom;

    private Long startNodeId;
    private Long endNodeId;

    private Integer numSubducts;

    private Integer usedSubducts;

   @Generated(GenerationTime.ALWAYS)
    private Integer availableSubducts;

    private String networkType;

    private String remarks;

    @Column(name = "adm1_id")
    private Integer adm1Id;

    @Column(name = "adm2_id")
    private Integer adm2Id;

    @Column(name = "mvno_id", nullable = false)
    private Integer mvnoId;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Column(name = "parent_ne_id")
    public Integer parentNeId;

    @Column(name = "parent_ne_type")
    public String parentNeType;
}
