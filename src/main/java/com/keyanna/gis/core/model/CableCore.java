package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "cable_core")
public class CableCore {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "cable_id", nullable = false)
    private Integer cableId;

    @Column(name = "core_number")
    private Integer coreNumber;

    @Column(name = "core_color")
    private String coreColor;

    @Column(name = "core_status")
    private String coreStatus;

    @Column(name = "tube_number")
    private Integer tubeNumber;

    @Column(name = "splice_type")
    private String spliceType;

    @Column(name = "splice_status")
    private String spliceStatus;
}
