package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lookup_fdp_types")
public class LookupFdpTypes  {


        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Integer id;

        private String name;

        private String description;

        private boolean isActive;

        private LocalDateTime createdOn;

        private LocalDateTime modifiedOn;



}
