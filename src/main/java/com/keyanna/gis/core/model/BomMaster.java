package com.keyanna.gis.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "bom_master")
@SQLDelete(sql = "UPDATE bom_master SET status = 'Inactive' WHERE id = ?")
@Where(clause = "status = 'Active'")
@Data
@NoArgsConstructor
public class BomMaster {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(name = "survey_area_id", nullable = false)
    private Integer surveyAreaId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @Column(name = "created_by")
    private Long createdBy;

    @Schema(hidden = true)
    @CreatedDate
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_on", updatable = false)
    private LocalDateTime createdOn;

    @Schema(hidden = true)
    @LastModifiedDate
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "modified_on")
    private LocalDateTime modifiedOn;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "mvno_id")
    private Integer mvnoId;

    // Optional: Bidirectional relationship
    @OneToMany(mappedBy = "bomMaster", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<BomVersion> versions;

    // Status Enum
    public enum Status {
        Active, Inactive
    }


}

