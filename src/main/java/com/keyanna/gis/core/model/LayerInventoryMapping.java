package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "layer_inventory_mapping", schema = "public")
public class LayerInventoryMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "layer_id", nullable = false)
    private Integer layerId;

    @Column(name = "layer_code", nullable = false)
    private String layerCode;

    @Column(name = "parent_param_id")
    private Integer parentParamId;

    @Column(name = "param_id")
    private Integer paramId;

    @Column(name = "param_name")
    private String paramName;

    @Column(name = "param_value")
    private String paramValue;

    @Column(name = "is_mandatory")
    private Boolean isMandatory;

    @Column(name = "status")
    private String status;

    @Column(name = "is_accessory")
    private Boolean isAccessory;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "created_on")
    private LocalDateTime createdOn;

    @Column(name = "is_configuration")
    private Boolean isConfiguration;

}
