package com.keyanna.gis.core.model;

import jakarta.persistence.*;

import java.time.Instant;

@Entity
@Table(name = "layer_mapping")
public class LayerMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    private Integer mappingId;

    @Column(name = "parent_layer_id", nullable = false)
    private Integer parentLayerId;

    @Column(name = "child_layer_id", nullable = false)
    private Integer childLayerId;

    @Column(name = "created_on")
    private Instant createdOn = Instant.now();

    @Column(name = "modified_on")
    private Instant modifiedOn;

    // Constructors
    public LayerMapping() {}

    public LayerMapping(Integer parentLayerId, Integer childLayerId) {
        this.parentLayerId = parentLayerId;
        this.childLayerId = childLayerId;
        this.createdOn = Instant.now();
    }

    // Getters and Setters
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public Integer getParentLayerId() {
        return parentLayerId;
    }

    public void setParentLayerId(Integer parentLayerId) {
        this.parentLayerId = parentLayerId;
    }

    public Integer getChildLayerId() {
        return childLayerId;
    }

    public void setChildLayerId(Integer childLayerId) {
        this.childLayerId = childLayerId;
    }

    public Instant getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public Instant getModifiedOn() {
        return modifiedOn;
    }

    public void setModifiedOn(Instant modifiedOn) {
        this.modifiedOn = modifiedOn;
    }
}
