package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@Table(name = "layer_master")
public class LayerMaster {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "status")
    private String status = "Active";

    @Column(name = "icon")
    private String icon;

    @Column(name = "code")
    private String code;

    @Column(name = "mvno_id")
    private Integer mvnoId;
}
