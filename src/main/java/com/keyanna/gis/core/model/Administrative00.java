package com.keyanna.gis.core.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.locationtech.jts.geom.MultiPolygon;

@Setter
@Getter
@Entity
@NoArgsConstructor  // <- This adds a no-argument constructor
@Table(name = "administrative_00")
public class Administrative00 {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "adm0_name", nullable = false)
    public String adm1Name;
    @Column(name = "adm0_code", nullable = false)
    public String adm1Code;
    @Column(name = "geom", columnDefinition = "geometry(MULTIPOLYGON,4326)")
    private MultiPolygon geom;
}
