package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.TrenchDTO;
import com.keyanna.gis.core.dto.response.PolygonResponseDTO;
import com.keyanna.gis.core.model.Splitter;
import com.keyanna.gis.core.model.TrenchLayer;
import com.keyanna.gis.core.service.TrenchLayerService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.TRENCH)
public class TrenchController {

    @Autowired
    private TrenchLayerService service;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<TrenchLayer>> create(@Valid @RequestBody TrenchDTO dto) throws ParseException {
        try {
            TrenchLayer trenchLayer = service.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Trench Layer created successfully",null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllTrench/{mvnoId}")
    public ResponseEntity<ApiResponse<List<TrenchLayer>>> getAllTrench(@PathVariable Integer mvnoId) {
        try {
            List<TrenchLayer> trenchLayer = service.getAllTrench(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Trench fetched successfully", trenchLayer));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }
    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getNeTrenchById(@PathVariable String publicId,
                                                          @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            TrenchLayer trenchLayer = service.getNeTrenchById(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("TrenchLayer fetched Successfully", trenchLayer));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting trench details: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deletefdc(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId ) {
        try {
            UUID uuid = UUID.fromString(publicId);

            service.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Trench Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting trench: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<TrenchDTO>> updateTrench(
            @PathVariable UUID publicId,
            @Valid @RequestBody TrenchDTO trenchDTO) {
        try {
            service.updateTrench(publicId, trenchDTO);
            return ResponseEntity.ok(ApiResponse.success("Trench updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }


}
