package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.CduDTO;
import com.keyanna.gis.core.dto.response.CduWithImageDTO;
import com.keyanna.gis.core.model.Cdu;
import com.keyanna.gis.core.service.CduService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.CDU)
@CrossOrigin(origins = "*")
public class CduController {


    private final CduService cduService;

    public CduController(CduService cduService) {
        this.cduService = cduService;
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Cdu>> create(@Valid @RequestBody CduDTO dto) throws ParseException {
        try {
            Cdu cdu = cduService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("cdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllCdus/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Cdu>>> getAllCdu(@PathVariable Integer mvnoId) {
        try {
            List<Cdu> fatPointList = cduService.getAllCdu(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("all cdus fetched successfully", fatPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getCduByPublicId(@PathVariable String publicId,
                                                           @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            CduWithImageDTO cdu = cduService.getCduByPublicIdAndMvnoId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Cdu fetched Successfully", cdu));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting cdu details: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<CduDTO>> updateCdu(
            @PathVariable UUID publicId,
            @Valid @RequestBody CduDTO cduDTO
            ) {
        try {
            cduService.updateCdu(publicId, cduDTO);
            return ResponseEntity.ok(ApiResponse.success("Cdu updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteCdu(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            cduService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cdu Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting cdu: " + ex.getMessage()));
        }
    }

    @PostMapping(value = "/createWithImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Cdu>> createWithImage(
            @RequestPart("cduDto") @Valid String cduDto,
            @RequestPart(value = "images", required = false) List<MultipartFile> imageFiles
    ) throws ParseException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            CduDTO dto = mapper.readValue(cduDto, CduDTO.class);
            cduService.createWithImage(dto, imageFiles);
            return ResponseEntity.ok(ApiResponse.success("cdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/previewImage")
    public ResponseEntity<?> previewImage(
            @RequestParam("fileName") String fileName ) {
        try {
            Map<String, Object> map = cduService.previewImageByName(fileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentTypes")))
                    .body(map.get("resources"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

}
