package com.keyanna.gis.core.controller;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.JointClosureDTO;
import com.keyanna.gis.core.model.JointClosure;
import com.keyanna.gis.core.service.JointClosureService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@RestController
@CrossOrigin(origins = "*")
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.JOINT_CLOSURE)
public class JointClosureController {



    private final JointClosureService jointClosureService;

    public JointClosureController(JointClosureService jointClosureService) {
        this.jointClosureService = jointClosureService;
    }


    @PostMapping("/create")
    public ResponseEntity<ApiResponse<JointClosure>> create(@Valid @RequestBody JointClosureDTO dto) throws ParseException {
        try {
            JointClosure jointClosure = jointClosureService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Joint Closure created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllJointClosures/{mvnoId}")
    public ResponseEntity<ApiResponse<List<JointClosure>>> getAllJointClosures(@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            List<JointClosure> closures = jointClosureService.getAllJointClosures(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Joint Closures fetched successfully", closures));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getJointClosureByPublicId(@PathVariable String publicId,
                                                                    @PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            JointClosure jointClosure = jointClosureService.getJointClosureByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("JointClosure fetched Successfully", jointClosure));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting jointClosure details: " + ex.getMessage()));
        }
    }



    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deletefdc(@PathVariable String publicId,@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            jointClosureService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("JointClosure Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting jointClosure: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<JointClosureDTO>> updateJointClosure(
            @PathVariable UUID publicId,
            @Valid @RequestBody JointClosureDTO dto) {
        try {
            jointClosureService.updateJointClosure(publicId, dto);
            return ResponseEntity.ok(ApiResponse.success("Joint Closure updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }
}
