package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.MduDTO;
import com.keyanna.gis.core.dto.response.MduWithImageDTO;
import com.keyanna.gis.core.model.Mdu;
import com.keyanna.gis.core.service.MduService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.MDU)
@CrossOrigin(origins = "*")
public class MduController {


    private final MduService mduService;

    public MduController(MduService mduService) {
        this.mduService = mduService;
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Mdu>> create(@Valid @RequestBody MduDTO dto) throws ParseException {
        try {
            Mdu mdu = mduService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("mdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllMdus/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Mdu>>> getAllMdu(@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            List<Mdu> fatPointList = mduService.getAllMdu(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("all mdus fetched successfully", fatPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getMduByPublicId(@PathVariable String publicId,
                                                           @PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            MduWithImageDTO mdu = mduService.getMduByPublicIdAndMvnoId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Mdu fetched Successfully", mdu));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting mdu details: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<MduDTO>> updateMdu(
            @PathVariable UUID publicId,
            @Valid @RequestBody MduDTO mduDTO) {
        try {
            mduService.updateMdu(publicId, mduDTO);
            return ResponseEntity.ok(ApiResponse.success("Mdu updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteMdu(@PathVariable String publicId, @PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            mduService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Mdu Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting mdu: " + ex.getMessage()));
        }
    }

    @PostMapping(value = "/createWithImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Mdu>> createWithImage(
            @RequestPart("mduDto") @Valid String mduDto,
            @RequestPart(value = "images", required = false) List<MultipartFile> imageFiles
    ) throws ParseException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            MduDTO dto = mapper.readValue(mduDto, MduDTO.class);
            mduService.createWithImage(dto, imageFiles);
            return ResponseEntity.ok(ApiResponse.success("mdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/previewImage")
    public ResponseEntity<?> previewImage(
            @RequestParam("fileName") String fileName) {
        try {
            Map<String, Object> map = mduService.previewImageByName(fileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentTypes")))
                    .body(map.get("resources"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

}
