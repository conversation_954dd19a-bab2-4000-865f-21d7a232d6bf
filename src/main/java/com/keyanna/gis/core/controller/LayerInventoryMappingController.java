package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.LAYER_INVENTORY_MAPPING)
@CrossOrigin(origins = "*")
public class LayerInventoryMappingController {

//    private final LayerInventoryMappingService layerInventoryMappingService;
//
//    public LayerInventoryMappingController(LayerInventoryMappingService layerInventoryMappingService) {
//        this.layerInventoryMappingService = layerInventoryMappingService;
//    }
//
//    @PostMapping(value = "/create")
//    public ResponseEntity<ApiResponse<?>> createMultipleLayerInventoryMappings(@RequestBody(required = true) List<LayerInventoryMappingRequestDTO> requestDTOList) {
//        try {
//            if (requestDTOList == null || requestDTOList.isEmpty()) {
//                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Request body cannot be empty."));
//            }
//
//            layerInventoryMappingService.createMultipleLayerInventoryMappings(requestDTOList);
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Layer Inventory Mapped successfully", null));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

}
