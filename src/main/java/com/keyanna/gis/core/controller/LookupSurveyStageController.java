package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.LookupSurveyStageRequestDTO;
import com.keyanna.gis.core.dto.response.LookupSurveyStageResponseDTO;
import com.keyanna.gis.core.service.LookupSurveyStageService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SURVEY_STAGE)
@CrossOrigin(origins = "*") // Allow requests from any origin
public class LookupSurveyStageController {

    private final LookupSurveyStageService lookupSurveyStageService;

    public LookupSurveyStageController(LookupSurveyStageService lookupSurveyStageService) {
        this.lookupSurveyStageService = lookupSurveyStageService;
    }

    @GetMapping(value = "/getAllLookupSurveyStage/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getAllLookupSurveyStage(@PathVariable Integer mvnoId) {
        try {
            List<LookupSurveyStageResponseDTO> responseDtoList = lookupSurveyStageService.getAllLookupSurveyStage(mvnoId);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Lookup Survey Stage data fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

//    @PostMapping("/create")
//    public ResponseEntity<ApiResponse<?>> createSurveyStatus(@Valid @RequestBody LookupSurveyStageRequestDTO requestDTO) {
//        try {
//            lookupSurveyStageService.createLookupSurveyStage(requestDTO);
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Survey Stage saved successfully", null));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error(ex.getMessage()));
//        }
//    }
//
//    @PutMapping("/update/{id}")
//    public ResponseEntity<ApiResponse<?>> updateSurveyStatus(@PathVariable Integer id,
//                                                             @Valid @RequestBody LookupSurveyStageRequestDTO updatedDTO) {
//        try {
//            lookupSurveyStageService.updateLookupSurveyStage(id, updatedDTO);
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Survey Stage updated successfully", null));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error(ex.getMessage()));
//        }
//    }
//
//    @DeleteMapping("/delete/{id}")
//    public ResponseEntity<ApiResponse<?>> deleteSurveyStatus(@PathVariable Integer id) {
//        try {
//            lookupSurveyStageService.deleteLookupSurveyStageById(id);
//            Map<String, Object> responseMap = new HashMap<>();
//            responseMap.put("id", id);
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Survey Stage deleted successfully", responseMap));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error("Error deleting Survey Stage: " + ex.getMessage()));
//        }
//    }
//
//    @GetMapping("/get/{id}")
//    public ResponseEntity<ApiResponse<?>> getSurveyStatusById(@PathVariable Integer id) {
//        try {
//            LookupSurveyStageResponseDTO response = lookupSurveyStageService.getLookupSurveyStageById(id);
//            return ResponseEntity.ok(ApiResponse.success("Survey Stage fetched successfully", response));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.NOT_FOUND)
//                    .body(ApiResponse.error("Survey Stage not found: " + ex.getMessage()));
//        }
//    }
}
