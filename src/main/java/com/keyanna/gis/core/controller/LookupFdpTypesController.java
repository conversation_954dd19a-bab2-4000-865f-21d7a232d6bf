package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.LookupFdpTypesDTO;
import com.keyanna.gis.core.model.LookupFdpTypes;
import com.keyanna.gis.core.service.LookupFdpTypesService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.FDP_TYPE)
public class LookupFdpTypesController {

    @Autowired
    private LookupFdpTypesService lookupFdpTypesService;

    @GetMapping("/getAllLookupFdps")
    public ResponseEntity<ApiResponse<List<LookupFdpTypesDTO>>> lookupFdpTypes() {


        try {
            List<LookupFdpTypesDTO> responseDtoList = lookupFdpTypesService.getallLookupFdpTypes();


            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Lookup Fdp Types  fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }

    }

    @GetMapping("/getById/{id}")
    public  ResponseEntity<ApiResponse<LookupFdpTypes>> getLookupFdpTypesById( @PathVariable Integer id) {

        try{
            LookupFdpTypes lookupFdpTypes = lookupFdpTypesService.getbyId(id);
            return ResponseEntity.ok(ApiResponse.success("Lookup Fdp Types Id fetched successfully", lookupFdpTypes));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }

    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<?>> createFdpType(@Valid @RequestBody LookupFdpTypesDTO requestDTO) {
        try {
            lookupFdpTypesService.createLookupFdpType(requestDTO);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("FDP Type saved successfully", null));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<ApiResponse<?>> updateFdpType(@PathVariable Integer id,
                                                        @Valid @RequestBody LookupFdpTypesDTO updatedDTO) {
        try {
            lookupFdpTypesService.updateLookupFdpType(id, updatedDTO);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("FDP Type updated successfully", null));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(ex.getMessage()));
        }
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<ApiResponse<?>> deleteFdpType(@PathVariable Integer id) {
        try {
            lookupFdpTypesService.deleteLookupFdpTypeById(id);
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("id", id);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("FDP Type deleted successfully", responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting FDP Type: " + ex.getMessage()));
        }
    }
}




