package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.LayerMappingDTO;
import com.keyanna.gis.core.service.impl.LayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.LAYER_MAPPING)
public class LayerMappingController {

    @Autowired
    private LayerService layerService;

    @GetMapping("/parent-layers-by-code/{code}")
    public HttpEntity<List<LayerMappingDTO>> getParentLayers(@PathVariable String code) {
        List<LayerMappingDTO> parentLayers = layerService.getParentLayers(code);
        return ResponseEntity.ok(parentLayers);
    }
}
