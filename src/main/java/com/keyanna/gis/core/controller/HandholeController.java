package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.HandholeRequestDTO;
import com.keyanna.gis.core.dto.response.HandholeResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Handhole;
import com.keyanna.gis.core.service.HandholeService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.HANDHOLE)
@CrossOrigin(origins = "*")
public class HandholeController {

    private final HandholeService handholeService;

    public HandholeController(HandholeService handholeService) {
        this.handholeService = handholeService;
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<HandholeResponseDTO>> create(
            @Valid @RequestBody HandholeRequestDTO dto) throws ParseException {
        try {
            HandholeResponseDTO response = handholeService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Handhole created successfully", response));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllHandholes/{mvnoId}")
    public ResponseEntity<ApiResponse<List<HandholeResponseDTO>>> getAllHandholes(@PathVariable Integer mvnoId) {
        try {
            List<HandholeResponseDTO> handholeList = handholeService.getAllHandholes(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All Handholes fetched successfully", handholeList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getHandholeByPublicId(@PathVariable String publicId,
                                                                @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Handhole handhole = handholeService.getHandholeByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Handhole fetched Successfully", handhole));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error getting handhole details: " + ex.getMessage()));
        }
    }


    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateHandhole(@PathVariable String publicId, @Valid @RequestBody HandholeResponseDTO dto) {
        try {
            UUID uuid = UUID.fromString(publicId);

            handholeService.updateHandholes(uuid, dto);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Handhole updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating fdp: " + ex.getMessage()));
        }
    }

    @DeleteMapping("/delete/{publicId}/{mvnoId}")
    public ResponseEntity<?> deleteHandholeByPublic(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);
            handholeService.deleteHandholeByPublicId(uuid,mvnoId);
            return ResponseEntity.ok("Handholes deleted successfully");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Handhole not found with ID: " + publicId);
        } catch (DataIntegrityViolationException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body("Cannot delete Handhole — it is referenced by other tables");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Unexpected error occurred while deleting Handhole");
        }
    }
}