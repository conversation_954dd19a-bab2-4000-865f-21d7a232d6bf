package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.SduDTO;
import com.keyanna.gis.core.dto.request.SplitterRequestDTO;
import com.keyanna.gis.core.dto.response.FatDTO;
import com.keyanna.gis.core.dto.response.NetworkPortDTO;
import com.keyanna.gis.core.dto.response.NetworkPortResponseDTO;
import com.keyanna.gis.core.dto.response.SduResponseDTO;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.Splitter;
import com.keyanna.gis.core.service.SplitterService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SPLITTER)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class SplitterController {

    private final SplitterService splitterService;

    public SplitterController(SplitterService splitterService) {
        this.splitterService = splitterService;
    }



    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createFat(@Valid @RequestBody SplitterRequestDTO dto) throws ParseException {
        try {
            Splitter createdSplitter = splitterService.createSplitter(dto);

            // Only include required fields in response
            Map<String, Object> response = new HashMap<>();
            response.put("id", createdSplitter.getId());
            response.put("name", createdSplitter.getName());

            return ResponseEntity.ok(ApiResponse.success("Splitter created successfully", response));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateSplitter(@PathVariable String publicId, @Valid @RequestBody SplitterRequestDTO splitterRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            splitterService.updateSplitter(uuid, splitterRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating splitter: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getNeSplitterById(@PathVariable String publicId,
                                                            @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Splitter splitter = splitterService.getNeSplitterById(uuid, mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Splitter fetched Successfully", splitter));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting splitter details: " + ex.getMessage()));
        }
    }

//    @GetMapping(value = "/getAllSplitters")
//    public ResponseEntity<ApiResponse<?>> getAllSplitters(@RequestParam(name = "withGeometry", required = false, defaultValue = "false") boolean withGeometry) {
//        try {
//            /**
//             * TODO : below parameter comes form UI (HEADER)
//             * ApiConstants.INDIA_TIME_ZONE_STATIC
//             * angular :
//             *   // Get the current time zone of the browser
//             *   getBrowserTimeZone(): string {
//             *     return Intl.DateTimeFormat().resolvedOptions().timeZone;
//             *   }
//             */
//            String userTimeZone = ApiConstants.INDIA_TIME_ZONE_STATIC;
//            List<SplitterResponseDTO> responseDtoList = splitterService.getAllSplitters(withGeometry, userTimeZone);
//
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Splitters data fetch Successfully", responseDtoList));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteSplitter(@PathVariable String publicId,
                                                         @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            splitterService.deleteSplitterByPublicId(uuid, mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));

        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting splitter: " + ex.getMessage()));
        }
    }

    /**
     * Used @RequestParam for fetching filtered lists.
     */
    @GetMapping(value = "/available-ports")
    public ResponseEntity<ApiResponse<List<NetworkPortResponseDTO>>> getAvailablePortsForSplitter(@RequestParam Integer splitterId, @RequestParam Integer mvnoId) {
        try {

            List<NetworkPortResponseDTO> ports = splitterService.getAvailablePortsForSplitter(splitterId, mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Available ports fetched successfully", ports));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/nearby-sdus")
    public ResponseEntity<ApiResponse<List<SduResponseDTO>>> getNearbySDUsForSplitter(@RequestParam Integer splitterId, @RequestParam Integer mvnoId) {
        try {
            List<SduResponseDTO> sduList = splitterService.getNearbySDUsForSplitter(splitterId, mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Nearby SDUs fetched successfully", sduList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

}
