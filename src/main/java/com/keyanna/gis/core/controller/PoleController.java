package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.PoleDTO;
import com.keyanna.gis.core.dto.response.PoleWithImageDTO;
import com.keyanna.gis.core.model.Pole;
import com.keyanna.gis.core.service.PoleService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.POLE)
@CrossOrigin(origins = "*")
public class PoleController {

    private static final Logger logger = LoggerFactory.getLogger(PoleController.class);

    @Autowired
    private PoleService poleService;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Pole>> create(@Valid @RequestBody PoleDTO dto) throws ParseException {
        logger.info("Received request to create pole: {}", dto);
        try {
            Pole pole = poleService.create(dto);
            logger.info("Pole created successfully with publicId: {}", pole.getPublicId());
            return ResponseEntity.ok(ApiResponse.success("Pole created successfully", null));
        } catch (Throwable e) {
            logger.error("Error creating pole: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllPoles/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Pole>>> getAllPoles(@PathVariable Integer mvnoId) {
        try {
            List<Pole> poles = poleService.getAllPoles(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All poles fetched successfully", poles));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getPoleByPublicId(@PathVariable String publicId,
                                                            @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            PoleWithImageDTO pole = poleService.getPoleByPublicIdAndMvnoId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("pole fetched Successfully", pole));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting pole details: " + ex.getMessage()));
        }
    }

    @DeleteMapping("/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deletePole(@PathVariable String publicId,
                                                     @PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);
            poleService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.ok(ApiResponse.success("Pole deleted successfully", responseMap));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting pole: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<Pole>> updatePole(
            @PathVariable UUID publicId,
            @Valid @RequestBody PoleDTO poleDTO) {
        logger.info("Received request to update pole with publicId: {}, data: {}", publicId, poleDTO);
        try {
            poleService.updatePole(publicId, poleDTO);
            logger.info("Pole updated successfully for publicId: {}", publicId);
            return ResponseEntity.ok(ApiResponse.success("Pole updated successfully", null));
        } catch (Throwable e) {
            logger.error("Error updating pole with publicId {}: {}", publicId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error(e.getMessage()));
        }
    }

    @PostMapping(value = "/createWithImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Pole>> createWithImage(
            @RequestPart("poleDto") @Valid String poleDto,
            @RequestPart(value = "images", required = false) List<MultipartFile> imageFiles
    ) throws ParseException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            PoleDTO dto = mapper.readValue(poleDto, PoleDTO.class);
            poleService.createWithImage(dto, imageFiles);
            return ResponseEntity.ok(ApiResponse.success("Pole layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/previewImage")
    public ResponseEntity<?> previewImage(
            @RequestParam("fileName") String fileName) {
        try {
            Map<String, Object> map = poleService.previewImageByName(fileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentTypes")))
                    .body(map.get("resources"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }
}
