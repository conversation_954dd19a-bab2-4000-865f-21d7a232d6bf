package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.OdfDTO;
import com.keyanna.gis.core.model.Odf;
import com.keyanna.gis.core.service.OdfService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.ODF)
@CrossOrigin(origins = "*")
public class OdfController {

    private static final Logger logger = LoggerFactory.getLogger(OdfController.class);

    @Autowired
    private OdfService odfService;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Odf>> create(@Valid @RequestBody OdfDTO dto) throws ParseException {
        logger.info("Received request to create ODF: {}", dto);
        try {
            Odf odf = odfService.createOdf(dto);
            logger.info("ODF created successfully with publicId: {}", odf.getPublicId());
            return ResponseEntity.ok(ApiResponse.success("ODF created successfully", null));
        } catch (Throwable e) {
            logger.error("Error creating ODF: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAll/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Odf>>> getAllOdfs(Integer mvnoId) {
        try {
            List<Odf> odfs = odfService.getAllOdfs(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All ODFs fetched successfully", odfs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getByPublicId(@PathVariable String publicId,
                                                        @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);
            Odf odf = odfService.getOdfByPublicId(uuid,mvnoId);
            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("ODF fetched successfully", odf));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting ODF details: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<Odf>> update(
            @PathVariable UUID publicId,
            @Valid @RequestBody OdfDTO dto) {
        logger.info("Received request to update ODF with publicId: {}, data: {}", publicId, dto);
        try {
            odfService.updateOdf(publicId, dto);
            logger.info("ODF updated successfully for publicId: {}", publicId);
            return ResponseEntity.ok(ApiResponse.success("ODF updated successfully", null));
        } catch (Throwable e) {
            logger.error("Error updating ODF with publicId {}: {}", publicId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping("/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> delete(@PathVariable String publicId,
                                                 @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);
            odfService.deleteByPublicId(uuid,mvnoId);
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);
            return ResponseEntity.ok(ApiResponse.success("ODF deleted successfully", responseMap));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting ODF: " + ex.getMessage()));
        }
    }
}
