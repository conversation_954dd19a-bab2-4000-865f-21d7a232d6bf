package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.LookupCableDTO;
import com.keyanna.gis.core.model.LookupCable;
import com.keyanna.gis.core.service.LookupCableService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.LOOKUP_CABLE)
@CrossOrigin(origins = "*")
public class LookupCableController {

    @Autowired
    private LookupCableService service;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<LookupCable>> create(@Valid @RequestBody LookupCableDTO dto) throws ParseException {
        try {
            LookupCable lookupCable = service.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Lookupcable type created successfully",null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping("/getAllLookupCables")
    public ResponseEntity<ApiResponse<List<LookupCable>>> getAllLookupCables() {
        try {
            List<LookupCable> lookupCableListList = service.getAllActive();
            return ResponseEntity.ok(ApiResponse.success("Lookupcable type fetched successfully", lookupCableListList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping("/get/{id}")
    public ResponseEntity<ApiResponse<Object>> getLookupCableId(@PathVariable Integer id) {
        try {
            LookupCable lookupCable = service.getLookupCableById(id);

            return ResponseEntity.ok(ApiResponse.success("Lookupcable type fetched successfully", lookupCable));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping("/delete/{id}")
    public HttpEntity<ApiResponse<String>> deleteLookupCableById(@PathVariable Integer id) {
        try {
            service.deleteLookupCableById(id);
            return ResponseEntity.ok(ApiResponse.success("Lookupcable type deleted successfully with ID: " + id, null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }


    @PutMapping("/update/{Id}")
    public ResponseEntity<ApiResponse<LookupCableDTO>> updateLookupCable(
            @PathVariable Integer Id,
            @Valid @RequestBody LookupCableDTO lookupCableDTO) {
        try {
            service.updateLookupCable(Id, lookupCableDTO);
            return ResponseEntity.ok(ApiResponse.success("Lookupcable type updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }
}
