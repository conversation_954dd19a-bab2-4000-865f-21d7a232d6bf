package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.ClusterMasterRequestDTO;
import com.keyanna.gis.core.model.ClusterMaster;
import com.keyanna.gis.core.service.ClusterMasterService;
import com.keyanna.gis.core.utility.ApiResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.CLUSTER_MASTER)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class ClusterMasterController {

    private final ClusterMasterService clusterMasterService;

    public ClusterMasterController(ClusterMasterService clusterMasterService) {
        this.clusterMasterService = clusterMasterService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createClusterMaster(@RequestBody ClusterMasterRequestDTO clusterMasterRequestDTO) {
        try {
            clusterMasterService.createClusterMaster(clusterMasterRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("ClusterMaster data saved Successfully", null /*TODO add here if needed*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateClusterMaster(@PathVariable String publicId, @RequestBody ClusterMasterRequestDTO clusterMasterRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            clusterMasterService.updateClusterMaster(uuid, clusterMasterRequestDTO, clusterMasterRequestDTO.getMvnoId());

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("ClusterMaster updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating clusterMaster: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteClusterMaster(@PathVariable String publicId,
                                                              @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            clusterMasterService.deleteClusterMasterByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("ClusterMaster Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting clusterMaster: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}")
    public ResponseEntity<ApiResponse<?>> getClusterMasterByPublicId(@PathVariable String publicId,
                                                                     @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            ClusterMaster clusterMaster = clusterMasterService.getClusterMasterByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("ClusterMaster fetched Successfully", clusterMaster));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting clusterMaster details: " + ex.getMessage()));
        }
    }
}
