package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.SplitterSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.SplitterSpecificationResponseDTO;
import com.keyanna.gis.core.model.SplitterSpecification;
import com.keyanna.gis.core.service.SplitterSpecificationService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SPLITTER_SPECIFICATION)
@CrossOrigin(origins = "*")  // Allow requests from any origin
public class SplitterSpecificationController {

    private final SplitterSpecificationService splitterSpecificationService;

    public SplitterSpecificationController(SplitterSpecificationService splitterSpecificationService) {
        this.splitterSpecificationService = splitterSpecificationService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createSplitterSpecification(@Valid @RequestBody SplitterSpecificationRequestDTO splitterSpecificationRequestDTO) {
        try {
            splitterSpecificationService.createSplitterSpecification(splitterSpecificationRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter Specification data saved Successfully", null /*TODO add here*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{id}")
    public ResponseEntity<ApiResponse<?>> updateSplitterSpecification(@PathVariable Integer id, @Valid @RequestBody SplitterSpecificationRequestDTO updatedSplitterSpecificationRequestDTO) {
        try {
            SplitterSpecification updatedSplitterSpec = splitterSpecificationService.updateSplitterSpecification(id, updatedSplitterSpecificationRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter Specification updated Successfully", updatedSplitterSpec /*TODO change here if needed*/));

        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @GetMapping(value = "/getAllSplitterSpecifications")
    public ResponseEntity<ApiResponse<?>> getAllSplitterSpecifications() {
        try {
            List<SplitterSpecificationResponseDTO> responseDtoList = splitterSpecificationService.getAllSplitterSpecifications();

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter Specifications data fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{id}")
    public ResponseEntity<ApiResponse<?>> deleteSplitterSpecification(@PathVariable Integer id) {
        try {
            splitterSpecificationService.deleteSplitterSpecificationById(id);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("id", id);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Splitter Specification Deleted Successfully ", responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error deleting FDP: " + ex.getMessage()));
        }
    }

}
