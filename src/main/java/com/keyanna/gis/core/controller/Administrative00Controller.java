package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.Administrator00DTO;
import com.keyanna.gis.core.service.impl.Administrative00ServiceImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.ADM00)
@CrossOrigin(origins = "*")
public class Administrative00Controller {

    private final Administrative00ServiceImpl service;

    public Administrative00Controller(Administrative00ServiceImpl service) {
        this.service = service;
    }

    @GetMapping(value = "/{adm0Code}", produces = "application/json")
    public ResponseEntity<Administrator00DTO> getByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        Administrator00DTO dto = service.getSingleByAdm0Code(adm0Code);
        if (dto == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(dto);
    }

    @GetMapping(value = "/getAllAdm00/{adm0Code}", produces = "application/json")  // ▶︎ Change: “getall” segment
    public ResponseEntity<List<Administrator00DTO>> getAllByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        List<Administrator00DTO> list = service.getAllByAdm0Code(adm0Code);
        return ResponseEntity.ok(list);
    }
}



