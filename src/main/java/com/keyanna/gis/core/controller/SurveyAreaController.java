package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.SurveyAreaRequestDTO;
import com.keyanna.gis.core.dto.response.SurveyAreaResponseDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.SurveyArea;
import com.keyanna.gis.core.repository.SurveyAreaRepository;
import com.keyanna.gis.core.service.SurveyAreaService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SURVEY_AREA)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class SurveyAreaController {

    private final SurveyAreaService surveyAreaService;

    @Autowired
    SurveyAreaRepository surveyAreaRepository;

    public SurveyAreaController(SurveyAreaService surveyAreaService) {
        this.surveyAreaService = surveyAreaService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createSurveyArea(@Valid @RequestBody SurveyAreaRequestDTO surveyAreaRequestDTO) {
        try {
            surveyAreaService.createSurveyArea(surveyAreaRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("SurveyArea data saved Successfully", null /*TODO add here if needed*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateSurveyArea(@PathVariable String publicId, @Valid @RequestBody SurveyAreaRequestDTO surveyAreaRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            surveyAreaService.updateSurveyArea(uuid, surveyAreaRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("SurveyArea updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating surveyArea: " + ex.getMessage()));
        }
    }

    @PostMapping(value = "/updateStatus/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateSurveyAreaStatus(
            @Valid @RequestBody SurveyAreaRequestDTO.SurveyAreaUpdateStatusDTO requestUpdateDto
            ) {
        try {
            UUID uuid = UUID.fromString(requestUpdateDto.getPublicId());

            surveyAreaService.updateSurveyAreaStatus(uuid, requestUpdateDto.getSurveyStatusName(), requestUpdateDto.getUserId(), requestUpdateDto.getMvnoId());

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Survey area status updated successfully", null /*TODO add here*/));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Survey Area not found with publicId"));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error updating surveyArea: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteSurveyArea(@PathVariable String publicId,
                                                           @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            surveyAreaService.deleteSurveyAreaByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("SurveyArea Deleted Successfully", responseMap));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Survey Area not found with publicId"));
        } catch (DataIntegrityViolationException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting surveyArea: " + "Cannot delete survey area — it has associated network elements."));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting surveyArea: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getAllSurveys/{mvnoId}")
    public HttpEntity<ApiResponse<List<SurveyArea>>> getAllFat(@PathVariable Integer mvnoId) {
        try {
            List<SurveyArea> surveyList = surveyAreaService.getAllSurveys(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All Survey fetched successfully", surveyList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping(value = "/getById/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getSurveyAreaByPublicId(@PathVariable String publicId,
                                                                  @RequestParam Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            SurveyArea surveyArea = surveyAreaService.getSurveyAreaByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Survey Area fetched Successfully", surveyArea));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting surveyArea: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getByUserId/{userId}/{mvnoId}/{isAdmin}")
    public ResponseEntity<ApiResponse<?>> getSurveyAreaByUserId(@PathVariable Integer userId,
                                                                @PathVariable Integer mvnoId,
                                                                @PathVariable Boolean isAdmin) {
        try {
            List<SurveyAreaResponseDTO> responseMapList = surveyAreaService.getSurveyAreaByUserId(userId,mvnoId,isAdmin);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Survey Area details fetched Successfully", responseMapList));

        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting surveyArea: " + ex.getMessage()));
        }
    }

    /**
     * Note : This api will use while loading survey Area view only on bottom right (Not on Assign/update status pop)
     *
     * @param userId
     * @return
     */
    @GetMapping(value = "/getByUserIdAtLayer/{userId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getSurveyAreaByUserIdAtLayer(@PathVariable Integer userId,
                                                                     @PathVariable Integer mvnoId  ) {
        try {
            List<SurveyAreaResponseDTO> responseMapList = surveyAreaService.getSurveyAreaByUserIdAtLayer(userId,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Survey Area details fetched Successfully", responseMapList));

        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting surveyArea: " + ex.getMessage()));
        }
    }

//    @GetMapping("/getAllPlanned")
//    public ResponseEntity<List<SurveyArea>> getBySurveyStatusId() {
//        List<SurveyArea> result = new ArrayList<>();
//        try {
//

    /// /            List<SurveyArea> list = surveyAreaRepository.findByLookupSurveyStatus(lookupSurveyStatusRepository.findById(2).get());
//            List<SurveyArea> list = surveyAreaService.findByLookupSurveyStatus("Initiated");
//            return ResponseEntity.ok(list);
//        } catch (Exception e) {
//            throw new RuntimeException("Failed to fetch survey areas", e);
//        }
//    }

//@GetMapping("/admin-initiated")
//public ResponseEntity<List<SurveyAreaRequestDTO>> getAdminInitiatedSurveys() {
//    List<SurveyArea> surveys = surveyAreaRepository.findInitiatedSurveysByAdminRole();
//
//    List<SurveyAreaRequestDTO> dtos = surveys.stream()
//            .map(s -> new SurveyAreaRequestDTO(s.getIsActive(), s.getName(),s.getId()))
//            .collect(Collectors.toList());
//
//    return ResponseEntity.ok(dtos);
//}
//    @GetMapping("/admin-initiated/{mvnoId}")
//    public ResponseEntity<List<SurveyAreaRequestDTO>> getAdminInitiatedSurveys(@PathVariable(name = "mvnoId",required = true) Integer mvnoId) {
//        List<Object[]> surveys = surveyAreaRepository.findInitiatedSurveyDataByAdminRole(mvnoId);
//
//        List<SurveyAreaRequestDTO> dtos = surveys.stream().map(s -> {
//            Boolean isActive = s[5] != null ? Boolean.valueOf(s[5].toString()) : null;
//            String name = s[1] != null ? s[1].toString() : null;
//            Integer mvnoId = s[9] != null ? Integer.valueOf(s[9].toString()) : null;
//
//            SurveyAreaRequestDTO dto = new SurveyAreaRequestDTO(isActive, name, mvnoId);
//            dto.setDescription(s[2] != null ? s[2].toString() : null);
//            dto.setSurveyStatusId(s[3] != null ? Integer.valueOf(s[3].toString()) : null);
//
//            // Parse and set geom
//            if (s[4] != null) {
//                try {
//                    ObjectMapper mapper = new ObjectMapper();
//                    SurveyAreaRequestDTO.GeometryPolygonDto geomDto = mapper.readValue(s[4].toString(), SurveyAreaRequestDTO.GeometryPolygonDto.class);
//                    dto.setGeom(geomDto);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//            // Parse and set survey start date
//            if (s[6] != null) {
//                try {
//                    dto.setSurveyStartDate(java.time.OffsetDateTime.parse(s[6].toString()).toLocalDate());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    // fallback or handle error
//                }
//            }
//
//            // Parse and set survey end date
//            if (s[7] != null) {
//                try {
//                    dto.setSurveyEndDate(java.time.OffsetDateTime.parse(s[7].toString()).toLocalDate());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    // fallback or handle error
//                }
//            }
//
//            // Set userId from created_by
//            if (s[8] != null) {
//                dto.setUserId(Long.valueOf(s[8].toString()));
//            }
//
//            return dto;
//        }).collect(Collectors.toList());
//
//        return ResponseEntity.ok(dtos);
//    }

}
