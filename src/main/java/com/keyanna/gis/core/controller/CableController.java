package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.CableRequestDTO;
import com.keyanna.gis.core.dto.response.CableResponseDTO;
import com.keyanna.gis.core.model.Cable;
import com.keyanna.gis.core.service.CableService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.CABLE)
@CrossOrigin(origins = "*")  // Allow requests from any origin
public class CableController {

    private final CableService cableService;

    public CableController(CableService cableService) {
        this.cableService = cableService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createCable(@Valid @RequestBody CableRequestDTO cableRequestDTO) {
        try {
            cableService.createCable(cableRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cables data saved Successfully", null /*TODO add here*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateCable(@PathVariable String publicId, @Valid @RequestBody CableRequestDTO cableRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            cableService.updateCable(uuid, cableRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating cable: " + ex.getMessage()));
        }
    }

    /**
     * Below api is need to change cable_type field in future if you want to use this api.
     */
//    @GetMapping(value = "/getAllCables")
//    public ResponseEntity<ApiResponse<?>> getAllCables(@RequestParam(name = "withGeometry", required = false, defaultValue = "false") boolean withGeometry) {
//        try {
//            /**
//             * TODO : below parameter comes form UI (HEADER)
//             * ApiConstants.INDIA_TIME_ZONE_STATIC
//             * angular :
//             *   // Get the current time zone of the browser
//             *   getBrowserTimeZone(): string {
//             *     return Intl.DateTimeFormat().resolvedOptions().timeZone;
//             *   }
//             */
//            String userTimeZone = ApiConstants.INDIA_TIME_ZONE_STATIC;
//            List<CableResponseDTO> responseDtoList = cableService.getAllCables(withGeometry, userTimeZone);
//
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Cables data fetched Successfully", responseDtoList));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

//    @GetMapping(value = "/getAllCables")
//    public ResponseEntity<ApiResponse<?>> getAllCables0() {
//        try {
//            List<CableResponseDTO> responseDtoList = cableService.getAllCables();
//
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Cables data fetch Successfully", responseDtoList));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteCable(@PathVariable String publicId,
                                                      @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            cableService.deleteCableByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting cable: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getCableByPublicId(@PathVariable String publicId,
                                                             @PathVariable Integer mvnoId  ) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Cable cable = cableService.getCableByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Cable fetched Successfully", cable));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting cable details: " + ex.getMessage()));
        }
    }
}
