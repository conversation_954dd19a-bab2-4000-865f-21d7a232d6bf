package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.model.LayerMaster;
import com.keyanna.gis.core.service.LayerMasterService;
import com.keyanna.gis.core.utility.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.LAYERS)
@CrossOrigin(origins = "*")  // Allow
public class LayerMasterController {

    @Autowired
    private LayerMasterService layerMasterService;

    @GetMapping(value = "/getAllLayers/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getAllCables(@RequestParam(name = "withGeometry", required = false, defaultValue = "false") boolean withGeometry,
                                                       Integer mvnoId) {
        try {

            List<LayerMaster> dataList = layerMasterService.getAllLayers(mvnoId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Layers data fetched successfully", dataList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }
}
