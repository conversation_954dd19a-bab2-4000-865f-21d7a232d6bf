package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.StreetDTO;
import com.keyanna.gis.core.model.Street;
import com.keyanna.gis.core.service.StreetService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.STREET)
@CrossOrigin(origins = "*")
public class StreetController {

    @Autowired
    private StreetService service;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Street>> create(@Valid @RequestBody StreetDTO dto) throws ParseException {
        try {
            Street  street= service.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Street created successfully",null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllStreets/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Street>>> getAllStreet(@PathVariable Integer mvnoId) {
        try {
            List<Street> street = service.getAllStreet(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Street fetched successfully", street));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }
    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getStreetById(@PathVariable String publicId,
                                                        @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Street street = service.getStreetById(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Street fetched Successfully", street));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting street details: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteStreet(@PathVariable String publicId,
                                                       @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            service.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Street Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails-
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting street: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<StreetDTO>> updateStreet(
            @PathVariable UUID publicId,
            @Valid  @RequestBody StreetDTO streetDTO) {
        try {
            service.updateStreet(publicId, streetDTO);
            return ResponseEntity.ok(ApiResponse.success("Street updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

}
