package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.FdpRequestDTO;
import com.keyanna.gis.core.model.Fdp;
import com.keyanna.gis.core.service.FdpService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.FDP)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class FdpController {

    private final FdpService fdpService;

    public FdpController(FdpService fdpService) {
        this.fdpService = fdpService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createFdp(@Valid @RequestBody FdpRequestDTO fdpRequestDTO) {
        try {
            fdpService.createFdp(fdpRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Fdp data saved Successfully", null /*TODO add here if needed*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updateFdp(@PathVariable String publicId, @Valid @RequestBody FdpRequestDTO fdpRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            fdpService.updateFdp(uuid, fdpRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Fdp updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating fdp: " + ex.getMessage()));
        }
    }

//    @GetMapping(value = "/getAllFdps")
//    public ResponseEntity<ApiResponse<?>> getAllFdps(@RequestParam(name = "withGeometry", required = false, defaultValue = "false") boolean withGeometry) {
//        try {
//            /**
//             * TODO : below parameter comes form UI (HEADER)
//             * ApiConstants.INDIA_TIME_ZONE_STATIC
//             * angular :
//             *   // Get the current time zone of the browser
//             *   getBrowserTimeZone(): string {
//             *     return Intl.DateTimeFormat().resolvedOptions().timeZone;
//             *   }
//             */
//            String userTimeZone = ApiConstants.INDIA_TIME_ZONE_STATIC;
//            List<FdpResponseDTO> responseDtoList = fdpService.getAllFdps(withGeometry, userTimeZone);
//
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Fdps data fetch Successfully", responseDtoList));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getFdpByPublicId(@PathVariable String publicId,
                                                           @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Fdp fdp = fdpService.getFdpByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Fdp fetched Successfully", fdp));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error getting fdp details: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteFdp(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            fdpService.deleteFdpByPublicIdAndMvnoId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Fdp Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));

        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting FDP: " + ex.getMessage()));
        }
    }
}
