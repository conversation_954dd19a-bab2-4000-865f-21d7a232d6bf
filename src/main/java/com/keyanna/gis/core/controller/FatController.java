package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.FatDTO;
import com.keyanna.gis.core.dto.response.FatWithImageDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.Fat;
import com.keyanna.gis.core.model.TrenchLayer;
import com.keyanna.gis.core.service.FatService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.FAT)
@CrossOrigin(origins = "*")  // Allow requests from any origin
public class FatController {

    private final FatService fatService;

    public FatController(FatService fatService) {
        this.fatService = fatService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createFat(@Valid @RequestBody FatDTO dto) throws ParseException {
        try {
            Fat createdFat = fatService.create(dto);

            // Only include required fields in response
            Map<String, Object> response = new HashMap<>();
            response.put("id", createdFat.getId());
            response.put("name", createdFat.getName());

            return ResponseEntity.ok(ApiResponse.success("Fat created successfully", response));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping(value = "/getAllFats/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Fat>>> getAllFat(@PathVariable Integer mvnoId) {
        try {
            List<Fat> fatPointList = fatService.getAllFat(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All Fat fetched successfully", fatPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping("/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getBuildingByPublicAndMvnoId(
            @PathVariable String publicId,
            @PathVariable Integer mvnoId) {
        try {
              UUID uuid = UUID.fromString(publicId);
            FatWithImageDTO fat = fatService.findByPublicIdAndMvnoId(uuid,mvnoId);


            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Fat fetched successfully", fat));

        } catch (IllegalArgumentException e) {
            // Invalid UUID format
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + e.getMessage()));
        } catch (EntityNotFoundException ex) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Fat not found: " + ex.getMessage()));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting Fat details: " + ex.getMessage()));
        }
    }


    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteFat(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            fatService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Fat Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting fat: " + ex.getMessage()));
        }
    }



    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateFat(
            @PathVariable String publicId,
            @Valid @RequestBody FatDTO fatRequestDTO) {
        try {
            UUID uuId = UUID.fromString(publicId);

            Fat updatedFat = fatService.updateFat(uuId, fatRequestDTO);

            if (updatedFat == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Fat not found with ID: " + publicId));
            }

            Map<String, Object> response = new HashMap<>();
            response.put("id", updatedFat.getId());
            response.put("name", updatedFat.getName());

            return ResponseEntity.ok(ApiResponse.success("Fat updated successfully", response));

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating fat: " + ex.getMessage()));
        }
    }


    @PostMapping(value = "/createWithImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Fat>> createWithImage(
            @RequestPart("fatDto") @Valid String fatDto,
            @RequestPart(value = "images", required = false) List<MultipartFile> imageFiles
    ) throws ParseException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            FatDTO dto = mapper.readValue(fatDto, FatDTO.class);
            fatService.createWithImage(dto, imageFiles);
            return ResponseEntity.ok(ApiResponse.success("Fat layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/previewImage")
    public ResponseEntity<?> previewImage(
            @RequestParam("fileName") String fileName) {
        try {
            Map<String, Object> map = fatService.previewImageByName(fileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentTypes")))
                    .body(map.get("resources"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }
}