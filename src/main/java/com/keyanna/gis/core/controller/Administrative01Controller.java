package com.keyanna.gis.core.controller;


import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.Administrator01DTO;
import com.keyanna.gis.core.service.impl.Administrative01ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.ADM01)
@CrossOrigin(origins = "*")  // Allow requests from any origin
public class Administrative01Controller {


    private final Administrative01ServiceImpl administrative01Service;


    public Administrative01Controller(Administrative01ServiceImpl administrative01Service) {
        this.administrative01Service = administrative01Service;
    }
    @GetMapping(value = "/{adm0Code}", produces = "application/json")
    public ResponseEntity<Administrator01DTO> getByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        Administrator01DTO dto = administrative01Service.getSingleByAdm0Code(adm0Code);
        if (dto == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(dto);
    }

    @GetMapping(value = "/getAllAdm01/{adm0Code}", produces = "application/json")  // ▶︎ Change: “getall” segment
    public ResponseEntity<List<Administrator01DTO>> getAllByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        List<Administrator01DTO> list = administrative01Service.getAllByAdm0Code(adm0Code);
        return ResponseEntity.ok(list);
    }

}