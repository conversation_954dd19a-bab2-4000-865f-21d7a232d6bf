package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.ManholeDTO;
import com.keyanna.gis.core.exception.EntityNotFoundException;
import com.keyanna.gis.core.model.JointClosure;
import com.keyanna.gis.core.model.Manhole;
import com.keyanna.gis.core.service.ManholeService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.MANHOLE)
@CrossOrigin(origins = "*")
public class ManholeController {

    @Autowired
    private ManholeService service;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Manhole>> create(@Valid @RequestBody ManholeDTO dto) throws ParseException {
        try {
            Manhole manhole = service.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Manhole created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllManholes/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Manhole>>> getAllNeManholes(Integer mvnoId) {
        try {
            List<Manhole> manholeList = service.getAllManholes(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("All NeManholes fetched successfully", manholeList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getNeManholeById(@PathVariable String publicId,
                                                           @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Manhole manhole = service.getManholeById(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Cable fetched Successfully", manhole));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting cable details: " + ex.getMessage()));
        }
    }


    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<ManholeDTO>> updateNeManhole(
            @PathVariable UUID publicId,
            @Valid @RequestBody ManholeDTO dto) {
        try {
            service.updateManhole(publicId, dto);
            return ResponseEntity.ok(ApiResponse.success("Manhole updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping("/delete/{publicId}/{mvnoId}")
    public ResponseEntity<?> deleteManholeById(@PathVariable String publicId,
                                               @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);
            service.deleteMandholeByPublicId(uuid,mvnoId);
            return ResponseEntity.ok("Manhole deleted successfully");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Manhole not found with ID: " + publicId);
        } catch (DataIntegrityViolationException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body("Cannot delete Manhole — it is referenced by other tables");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error occurred while deleting Manhole");
        }
    }
}