package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.OltDTO;
import com.keyanna.gis.core.model.Olt;
import com.keyanna.gis.core.service.OltService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.OLT)
@CrossOrigin(origins = "*")
public class OltController {

    private final OltService oltService;

    public OltController(OltService oltService) {
        this.oltService = oltService;
    }


    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Olt>> create(@Valid @RequestBody OltDTO dto) throws ParseException {
        try {
            Olt olt = oltService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("olt  created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping("/getAllOlt/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Olt>>> getAllOlt(@PathVariable Integer mvnoId) {
        try {
            List<Olt> fatPointList = oltService.getAllOlt(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("all olt fetched successfully", fatPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }


    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getOltByPublicId(@PathVariable String publicId,
                                                           @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Olt olt = oltService.getOltByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("olt fetched Successfully", olt));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting olt details: " + ex.getMessage()));
        }
    }


    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<OltDTO>> updateOlt(
            @PathVariable UUID publicId,
            @Valid @RequestBody OltDTO oltDTO) {
        try {
            oltService.updateOlt(publicId, oltDTO);
            return ResponseEntity.ok(ApiResponse.success("olt updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteOlt(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            oltService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("olt Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting olt: " + ex.getMessage()));
        }
    }

}
