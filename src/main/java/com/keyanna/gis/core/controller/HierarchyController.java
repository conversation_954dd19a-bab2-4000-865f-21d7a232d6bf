package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.registry.HierarchyRegistry;
import com.keyanna.gis.core.service.HierarchyBuilderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/olt")
public class HierarchyController {

    @Autowired
    private HierarchyBuilderService hierarchyBuilderService;

    @Autowired
    private HierarchyRegistry hierarchyRegistry;

    /**
     * This endpoint builds the entire dynamic tree JSON
     * starting from the given OLT ID.
     *
     * Example: GET /api/olt/1/hierarchy
     */
    @GetMapping("/hierarchy/{id}")
    public ResponseEntity<Object> getHierarchy(@PathVariable Integer id) {
        return ResponseEntity.ok(hierarchyRegistry.findById(id));
    }
}
