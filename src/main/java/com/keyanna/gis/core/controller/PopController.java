package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.PopRequestDTO;
import com.keyanna.gis.core.dto.response.PopResponseDTO;
import com.keyanna.gis.core.model.Manhole;
import com.keyanna.gis.core.model.Pop;
import com.keyanna.gis.core.service.PopService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.POP)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class PopController {

    private final PopService popService;

    public PopController(PopService popService) {
        this.popService = popService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createPop(@Valid @RequestBody PopRequestDTO popRequestDTO) {
        try {
            popService.createPop(popRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Pop data saved Successfully", null /*TODO add here if needed*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{publicId}")
    public ResponseEntity<ApiResponse<?>> updatePop(@PathVariable String publicId, @Valid @RequestBody PopRequestDTO popRequestDTO) {
        try {
            UUID uuid = UUID.fromString(publicId);

            popService.updatePop(uuid, popRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Pop updated Successfully", null /*TODO add here*/));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating pop: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getNePopById(@PathVariable String publicId,
                                                       @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Pop pop = popService.getNePopById(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Cable fetched Successfully", pop));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting cable details: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getAllPops/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getAllPops(@RequestParam(name = "withGeometry", required = false, defaultValue = "false")
                                                     boolean withGeometry,@PathVariable Integer mvnoId) {
        try {
            /**
             * TODO : below parameter comes form UI (HEADER)
             * ApiConstants.INDIA_TIME_ZONE_STATIC
             * angular :
             *   // Get the current time zone of the browser
             *   getBrowserTimeZone(): string {
             *     return Intl.DateTimeFormat().resolvedOptions().timeZone;
             *   }
             */
            String userTimeZone = ApiConstants.INDIA_TIME_ZONE_STATIC;
            List<PopResponseDTO> responseDtoList = popService.getAllPops(withGeometry, userTimeZone, mvnoId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Pops data fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deletePop(@PathVariable String publicId,
                                                    @PathVariable Integer mvnoId ) {
        try {
            UUID uuid = UUID.fromString(publicId);

            popService.deletePopByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Pop Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting pop: " + ex.getMessage()));
        }
    }
}
