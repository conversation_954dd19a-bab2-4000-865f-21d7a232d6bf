package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.NETWORK_ELEMENT_CONNECTION)
@CrossOrigin(origins = "*")
public class NetworkElementConnectionController {

//    private final NetworkElementConnectionService networkElementConnectionService;
//
//    public NetworkElementConnectionController(NetworkElementConnectionService networkElementConnectionService) {
//        this.networkElementConnectionService = networkElementConnectionService;
//    }


}
