package com.keyanna.gis.core.controller;


import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.response.Administrator02DTO;
import com.keyanna.gis.core.service.impl.Administrative02ServiceImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.ADM02)
@CrossOrigin(origins = "*")
public class Administrative02Controller {

    private final Administrative02ServiceImpl service;


    public Administrative02Controller(Administrative02ServiceImpl service) {
        this.service = service;
    }

    @GetMapping(value = "/{adm0Code}", produces = "application/json")
    public ResponseEntity<Administrator02DTO> getByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        Administrator02DTO dto = service.getSingleByAdm0Code(adm0Code);
        if (dto == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(dto);
    }

    @GetMapping(value = "/getAllAdm02/{adm0Code}", produces = "application/json")  // ▶︎ Change: “getall” segment
    public ResponseEntity<List<Administrator02DTO>> getAllByAdm0Code(
            @PathVariable("adm0Code") String adm0Code) {
        List<Administrator02DTO> list = service.getAllByAdm0Code(adm0Code);
        return ResponseEntity.ok(list);
    }
}

