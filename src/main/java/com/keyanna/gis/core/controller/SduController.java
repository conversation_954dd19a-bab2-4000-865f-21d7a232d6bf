package com.keyanna.gis.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.SduDTO;
import com.keyanna.gis.core.dto.response.SduWithImageDTO;
import com.keyanna.gis.core.model.Sdu;
import com.keyanna.gis.core.service.SduService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SDU)
@CrossOrigin(origins = "*")
public class SduController {


    private final SduService sduService;

    public SduController(SduService sduService) {
        this.sduService = sduService;
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Sdu>> create(@Valid @RequestBody SduDTO dto) throws ParseException {
        try {
            Sdu sdu = sduService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("sdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllSdus/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Sdu>>> getAllSdu(@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            List<Sdu> fatPointList = sduService.getAllSdu(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("all sdus fetched successfully", fatPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getSduByPublicId(@PathVariable String publicId,@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            SduWithImageDTO sdu = sduService.getSduByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Sdu fetched Successfully", sdu));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting sdu details: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<SduDTO>> updateSdu(
            @PathVariable UUID publicId,
            @Valid @RequestBody SduDTO sduDTO) {
        try {
            sduService.updateSdu(publicId, sduDTO);
            return ResponseEntity.ok(ApiResponse.success("Sdu updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteSdu(@PathVariable String publicId,@PathVariable(name = "mvnoId", required = true) Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            sduService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Sdu Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting sdu: " + ex.getMessage()));
        }
    }

    @PostMapping(value = "/createWithImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Sdu>> createWithImage(
            @RequestPart("sduDto") @Valid String sduDto,
            @RequestPart(value = "images", required = false) List<MultipartFile> imageFiles
    ) throws ParseException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            SduDTO dto = mapper.readValue(sduDto, SduDTO.class);
            sduService.createWithImage(dto, imageFiles);
            return ResponseEntity.ok(ApiResponse.success("sdu layer created successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/previewImage")
    public ResponseEntity<?> previewImage(
            @RequestParam("fileName") String fileName) {
        try {
            Map<String, Object> map = sduService.previewImageByName(fileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentTypes")))
                    .body(map.get("resources"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

}
