package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.CustomerDTO;
import com.keyanna.gis.core.model.Customer;
import com.keyanna.gis.core.service.CustomerService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.locationtech.jts.io.ParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.CUSTOMER)
public class CustomerController {

    private final CustomerService customerService;

    public CustomerController(CustomerService customerService) {
        this.customerService = customerService;
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Customer>> create(@Valid @RequestBody CustomerDTO dto) throws ParseException {
        try {
            Customer custoerLayer = customerService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Customer point created successfully",null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/getAllCustomers/{mvnoId}")
    public ResponseEntity<ApiResponse<List<Customer>>> getAllCustomers(Integer mvnoId) {
        try {
            List<Customer> customerPointList = customerService.getAllCustomers(mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Customer point fetched successfully", customerPointList));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getCustomerByPublicId(@PathVariable String publicId,
                                                                @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Customer customer = customerService.getCustomerByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Customer fetched Successfully", customer));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting customer details: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deletecustomer(@PathVariable String publicId,
                                                         @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            customerService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Customer Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting customer: " + ex.getMessage()));
        }

    }

    @PutMapping("/update/{publicId}")
    public ResponseEntity<ApiResponse<CustomerDTO>> updateCustomer(
            @PathVariable UUID publicId,
            @Valid @RequestBody CustomerDTO customerDTO) {
        try {
            customerService.updateCustomer(publicId, customerDTO);
            return ResponseEntity.ok(ApiResponse.success("Customer point updated successfully", null));
        } catch (Throwable e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
        }
    }
}