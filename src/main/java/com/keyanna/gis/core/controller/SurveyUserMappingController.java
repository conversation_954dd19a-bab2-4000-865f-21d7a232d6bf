package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.SurveyUserMappingRequestDTO;
import com.keyanna.gis.core.service.SurveyUserMappingService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SURVEY_USER_MAPPING)
@CrossOrigin(origins = "*")
public class SurveyUserMappingController {

    private final SurveyUserMappingService surveyUserMappingService;

    public SurveyUserMappingController(SurveyUserMappingService surveyUserMappingService) {
        this.surveyUserMappingService = surveyUserMappingService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createMultipleSurveyUserMappings(@Valid @RequestBody(required = true) List<SurveyUserMappingRequestDTO> requestDTOList) {
        try {
            if (requestDTOList == null || requestDTOList.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Request body cannot be empty."));
            }

            surveyUserMappingService.createMultipleSurveyUserMappings(requestDTOList);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Survey Area assigned successfully", null));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }


    // Future endpoints like GET, DELETE, etc. can be added here similarly.
}
