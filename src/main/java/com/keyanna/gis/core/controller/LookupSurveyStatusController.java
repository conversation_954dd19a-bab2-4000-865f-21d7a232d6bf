package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.LookupSurveyStatusRequestDTO;
import com.keyanna.gis.core.dto.response.LookupSurveyStatusResponseDTO;
import com.keyanna.gis.core.service.LookupSurveyStatusService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.SURVEY_STATUS)
@CrossOrigin(origins = "*") // Allow requests from any origin
public class LookupSurveyStatusController {

    private final LookupSurveyStatusService lookupSurveyStatusService;

    public LookupSurveyStatusController(LookupSurveyStatusService lookupSurveyStatusService) {
        this.lookupSurveyStatusService = lookupSurveyStatusService;
    }

    @GetMapping(value = "/getAllLookupSurveyStatus/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getAllLookupSurveyStatus(@PathVariable Integer mvnoId) {
        try {
            List<LookupSurveyStatusResponseDTO> responseDtoList = lookupSurveyStatusService.getAllLookupSurveyStatus(mvnoId);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Lookup Survey Status data fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<?>> createSurveyStatus(@Valid @RequestBody LookupSurveyStatusRequestDTO requestDTO) {
        try {
            lookupSurveyStatusService.createLookupSurveyStatus(requestDTO);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Survey Status saved successfully", null));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<ApiResponse<?>> updateSurveyStatus(@PathVariable Integer id,
                                                             @Valid @RequestBody LookupSurveyStatusRequestDTO updatedDTO) {
        try {
            lookupSurveyStatusService.updateLookupSurveyStatus(id, updatedDTO);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Survey Status updated successfully", null));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(ex.getMessage()));
        }
    }

    @DeleteMapping("/delete/{id}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteSurveyStatus(@PathVariable Integer id,@PathVariable Integer mvnoId) {
        try {
            lookupSurveyStatusService.deleteLookupSurveyStatusById(id,mvnoId);
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("id", id);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Survey Status deleted successfully", responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting Survey Status: " + ex.getMessage()));
        }
    }

    @GetMapping("/get/{id}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getSurveyStatusById(@PathVariable Integer id,@PathVariable Integer mvnoId) {
        try {
            LookupSurveyStatusResponseDTO response = lookupSurveyStatusService.getLookupSurveyStatusById(id,mvnoId);
            return ResponseEntity.ok(ApiResponse.success("Survey Status fetched successfully", response));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Survey Status not found: " + ex.getMessage()));
        }
    }
}
