package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.DuctDTO;
import com.keyanna.gis.core.model.Duct;
import com.keyanna.gis.core.service.DuctService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.DUCT)
public class DuctController {

    private final DuctService ductService;

    public DuctController(DuctService ductService) {
        this.ductService = ductService;
    }

    @PostMapping("/create")
        public ResponseEntity< ApiResponse<Duct>> create(@Valid  @RequestBody DuctDTO dto) throws ParseException {

            try {

                Duct duct = ductService.create(dto);

                return ResponseEntity.ok(ApiResponse
                        .success("Duct Layer created successfully", null));

            } catch (Throwable e) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(e.getMessage()));
            }

        }

    @GetMapping("/getAllDucts/{mvnoId}")
        public ResponseEntity<ApiResponse<List<Duct>>> getAllDucts(@PathVariable Integer mvnoId) {
            try {
                List<Duct> ductList = ductService.getAllDuct(mvnoId);
                return ResponseEntity.ok(ApiResponse.success("all Ducts fetched successfully", ductList));
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(e.getMessage()));
            }
        }

    @GetMapping(value = "/getByPublicId/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getDuctByPublicId(@PathVariable String publicId,
                                                            @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            Duct duct = ductService.getDuctByPublicId(uuid,mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Duct fetched Successfully", duct));

        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting duct details: " + ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{publicId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> deleteDuct(@PathVariable String publicId,
                                                     @PathVariable Integer mvnoId) {
        try {
            UUID uuid = UUID.fromString(publicId);

            ductService.deleteByPublicId(uuid,mvnoId);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("publicId", publicId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Duct Deleted Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid UUID format: " + publicId));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting duct: " + ex.getMessage()));
        }
    }

    @PutMapping("/update/{publicId}")
        public ResponseEntity<ApiResponse<Duct>> updateDuct(@PathVariable UUID publicId, @Valid @RequestBody DuctDTO ductDTO) {
            try {
                 ductService.updateDuct(publicId, ductDTO);
                return ResponseEntity.ok(ApiResponse.success("Duct updated successfully", null));
            } catch (Throwable e) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ApiResponse.error(e.getMessage()));
            }
        }

}
