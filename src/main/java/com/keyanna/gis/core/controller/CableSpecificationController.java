package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.CableSpecificationRequestDTO;
import com.keyanna.gis.core.dto.response.CableSpecificationResponseDTO;
import com.keyanna.gis.core.model.CableSpecification;
import com.keyanna.gis.core.service.CableSpecificationService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.CABLE_SPECIFICATION)
@CrossOrigin(origins = "*")  // Allow requests from any origin
public class CableSpecificationController {

    private final CableSpecificationService cableSpecificationService;

    public CableSpecificationController(CableSpecificationService cableSpecificationService) {
        this.cableSpecificationService = cableSpecificationService;
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<?>> createCableSpecification(@Valid @RequestBody CableSpecificationRequestDTO cableSpecificationRequestDTO) {
        try {
            cableSpecificationService.createCableSpecification(cableSpecificationRequestDTO);

            //  TODO make proper response
//        GeoJsonPointDto geoJsonPointDto = GeoJsonMapper.mapToGeoJSON(poi);
//        Map<String, Object> response = new HashMap<>();
//        response.put("geoJson", geoJsonPointDto);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable Specification data saved Successfully", null /*TODO add here*/));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @PutMapping(value = "/update/{id}")
    public ResponseEntity<ApiResponse<?>> updateCableSpecification(@PathVariable Integer id, @Valid @RequestBody CableSpecificationRequestDTO updatedCableSpecificationRequestDTO) {
        try {
            CableSpecification updatedCableSpec = cableSpecificationService.updateCableSpecification(id, updatedCableSpecificationRequestDTO);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable Specification updated Successfully", updatedCableSpec /*TODO change here if needed*/));

        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @GetMapping(value = "/getAllCableSpecifications")
    public ResponseEntity<ApiResponse<?>> getAllCableSpecification() {
        try {
            List<CableSpecificationResponseDTO> responseDtoList = cableSpecificationService.getAllCableSpecification();

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable Specifications data fetched Successfully", responseDtoList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @DeleteMapping(value = "/delete/{id}")
    public ResponseEntity<ApiResponse<?>> deleteCableSpecification(@PathVariable Integer id) {
        try {
            cableSpecificationService.deleteCableSpecificationById(id);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("id", id);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Cable Specification Deleted Successfully ", responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

}
