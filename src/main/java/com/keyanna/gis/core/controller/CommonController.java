package com.keyanna.gis.core.controller;

import com.keyanna.gis.core.constants.ApiConstants;
import com.keyanna.gis.core.dto.request.CommonRequestDTO;
import com.keyanna.gis.core.service.CommonService;
import com.keyanna.gis.core.utility.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = ApiConstants.BASE_API_URL + ApiConstants.COMMON)
@CrossOrigin(origins = "*") //  Allow requests from any origin
public class CommonController {

    private final CommonService commonService;

    public CommonController(CommonService commonService) {
        this.commonService = commonService;
    }

//    /**
//     * Fetch geoms based within Polygon
//     *
//     * @param polygonRequestDTO
//     * @return
//     */
    //  TODO DO not delete below method code, Currently not in use
//    @PostMapping(value = "/getGeomsWithinPolygon")
//    public ResponseEntity<ApiResponse<?>> getGeometriesWithinPolygon(@RequestBody(required = true) PolygonRequestDTO polygonRequestDTO) {
//        try {
//            Map<String, Object> responseMap = commonService.getGeometriesWithinPolygon(polygonRequestDTO);
//
//            return ResponseEntity.status(HttpStatus.OK)
//                    .body(ApiResponse.success("Geom within polygon data fetched Successfully", responseMap));
//        } catch (Throwable ex) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
//        }
//    }

    @GetMapping(value = "/getDataInSurveyArea/{surveyAreaId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getDataInSurveyArea(@PathVariable Integer surveyAreaId, @PathVariable Integer mvnoId) {
        try {
            Map<String, Object> responseMap = commonService.getDataInSurveyArea(surveyAreaId, mvnoId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Data In SurveyArea fetched Successfully", responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    //  TODO add mvnoId in condition in future, if we use this api in future

    /**
     * Used to show BOM (Bill of material), return data without Geom
     *
     * @param surveyAreaId
     * @return
     */
    @GetMapping(value = "/getDataInSurveyAreaBom/{surveyAreaId}")
    public ResponseEntity<?> getDataInSurveyAreaBom(@PathVariable Integer surveyAreaId) {
        try {
            CommonService.ExcelExportResult result = commonService.getDataInSurveyAreaBom(surveyAreaId);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=" + result.fileName())
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(result.content().length)
                    .body(new ByteArrayResource(result.content()));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    /**
     * TODO add mvnoId in condition in future, if we use this api in future
     */
    @PostMapping(value = "/findParentNearChildGeom")
    public ResponseEntity<ApiResponse<?>> findParentNearChildGeom(@Valid @RequestBody(required = true) CommonRequestDTO parentDataRequestDTO) {
        try {
            Map<String, Object> responseMap = commonService.findParentNearChildGeom(parentDataRequestDTO);

            String message = responseMap.isEmpty()
                    ? "No geom data found within configured radius"
                    : "Near By Parent Geom fetched Successfully";

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success(message, responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    /**
     * TODO add mvnoId in condition in future, if we use this api in future
     */
    @PostMapping(value = "/findNearByGeom")
    public ResponseEntity<ApiResponse<?>> findNearByGeom(@Valid @RequestBody(required = true) CommonRequestDTO dataRequestDTO) {
        try {
            Map<String, Object> responseMap = commonService.findNearByGeom(dataRequestDTO);

            String message = responseMap.isEmpty()
                    ? "No geom data found within configured radius"
                    : "Near By Geom fetched Successfully";

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success(message, responseMap));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    /**
     * TODO add mvnoId in condition in future, if we use this api in future
     */
    @PostMapping(value = "/getNetworkElementDataById")
    public ResponseEntity<ApiResponse<?>> getNetworkElementDataById(@Valid @RequestBody(required = true) Map<String, Object> body) {
        try {
            String publicIdStr = (String) body.get("publicId");
            String layerName = (String) body.get("layerName");

            UUID publicId = UUID.fromString(publicIdStr);

            Object responseEntity = commonService.getNetworkElementDataById(publicId, layerName);

            String message = (responseEntity == null)
                    ? "No data found within id"
                    : "Data fetched Successfully";

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success(message, responseEntity));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Invalid UUID format"));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error getting data: " + ex.getMessage()));
        }
    }

    @GetMapping(value = "/getStaffDetails/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getStaffDetails(@PathVariable Integer mvnoId) {
        try {
            List<Map<String, Object>> responseMapList = commonService.getStaffDetails(mvnoId);

            return ResponseEntity.status(HttpStatus.OK)
                    .body(ApiResponse.success("Get Staff Details data fetched Successfully", responseMapList));
        } catch (Throwable ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error(ex.getMessage()));
        }
    }

    @GetMapping("/preview/{directory}/{filename}")
    public ResponseEntity<?> previewImage(
            @PathVariable String directory,
            @PathVariable String fileName) {

        try {
            Map<String, Object> map = commonService.previewUploadedImage(directory, fileName);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType((String) map.get("contentType")))
                    .body(map.get("resource"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        } catch (Throwable e) {
            return ResponseEntity.internalServerError().body(Map.of(
                    "status", "error",
                    "message", "Failed to retrieve image: " + e.getMessage()
            ));
        }
    }

    @GetMapping(value = "/getSurveyDataForDigitalization/{surveyAreaId}/{mvnoId}")
    public ResponseEntity<ApiResponse<?>> getSurveyDataForDigitalization(@PathVariable Integer surveyAreaId, @PathVariable Integer mvnoId) {
        try {
            Map<String, Object> responseMap = commonService.getSurveyDataForDigitalization(surveyAreaId, mvnoId);

            return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success("Data In SurveyArea fetched Successfully", responseMap));
        } catch (IllegalArgumentException e) {
            // Thrown when UUID.fromString fails
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResponse.error("Invalid UUID format: "));
        } catch (Exception ex) {
            // Other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Error getting details: " + ex.getMessage()));
        }
    }
}
