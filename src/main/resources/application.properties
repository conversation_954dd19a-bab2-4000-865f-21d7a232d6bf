spring.datasource.url=*************************************************************************
#spring.datasource.url=******************************************************************
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=postgres
spring.datasource.password=root

spring.jpa.hibernate.database=postgresql
spring.jpa.hibernate.database-platform=org.hibernate.spatial.dialect.postgis.PostgisDialect
spring.jpa.properties.hibernate.dialect=org.hibernate.spatial.dialect.postgis.PostgisPG95Dialect

spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# 32-byte key (256 bits) -> base64-encoded version
jwt.secret=3Sv0TrcnbNYqpeXKiqstjsRi89rL+pIdjeRmChYbGkM=
#30 minutes
jwt.token.validity=30

#logging.level.org.springframework.cloud.gateway=DEBUG
#logging.level.reactor.netty.http.server=DEBUG

logging.level.root=INFO
logging.file.name=logs/app.log

# For api (/api/filter/findParentNearChild)
search.radius.meter=3

# For api (/api/filter/findNearByGeom)
radius.length.meters=3

# For api (/splitter/nearby-sdus)
splitter.nearby.sdus.radius.length.meters=300

upload.directory=src/main/resources/static/images/
upload.file.size=5

kafka-url=164.52.212.187:30084

# BOM Configuration
bom.data.directory=src/main/resources/bom_data
logging.level.com.keyanna.gis.core.service.impl.FilterServiceImpl=DEBUG
logging.level.org.apache.kafka.clients.NetworkClient=ERROR
eureka.client.enabled=false

spring.kafka.bootstrap-servers: broker1:9092,broker2:9092,broker3:9092
spring.kafka.consumer.request-timeout-ms: 30000
spring.kafka.consumer.session-timeout-ms: 15000
spring.kafka.consumer.heartbeat-interval-ms: 3000
spring.kafka.consumer.max-poll-interval-ms: 300000