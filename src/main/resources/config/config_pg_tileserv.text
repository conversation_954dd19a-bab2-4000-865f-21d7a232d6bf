Windows : 
========== ========== Option 1 : 
	Open cmd to location of pg_tileserv : C:\pg_tileserv

	SET DATABASE_URL=postgresql://gisappdb:postgres@gisapp@localhost:5432/gisapp

	pg_tileserv.exe


	After run  : 

	Browse : http://localhost:7800/

========== ========== Option 2 : 

Create a file named pg_tileserv.toml in the same directory with content like:
File name : pg_tileserv.toml

DbConnection = "user=your_user password=your_password host=localhost dbname=your_database sslmode=disable"
HttpHost = "0.0.0.0"
HttpPort = 7800
AssetsPath = "./assets"
DefaultResolution = 4096
DefaultBuffer = 256
MaxFeaturesPerTile = 50000
DefaultMinZoom = 0
DefaultMaxZoom = 22
CORSOrigins = "*"
Debug = true

Run  : 
pg_tileserv.exe --config pg_tileserv.toml
