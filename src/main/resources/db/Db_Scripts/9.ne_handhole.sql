CREATE TABLE ne_handhole (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    hole_size TEXT,  -- e.g., 'Small', 'Medium', 'Large'
    access_type TEXT CHECK (access_type IN ('TopOpen', 'SideAccess')) NOT NULL,
    material TEXT,  -- e.g., 'Plastic', 'Concrete', 'Steel'

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

    adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);


CREATE TABLE audit_ne_handhole (
    id SERIAL PRIMARY KEY,
    ne_handhole_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

CREATE TABLE audit_ne_handhole_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_handhole(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


CREATE OR REPLACE FUNCTION audit_ne_handhole()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_handhole (
        ne_handhole_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        affected_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO affected_id;

    INSERT INTO audit_ne_handhole_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;


-- Main Audit Trigger
CREATE TRIGGER trg_audit_ne_handhole
AFTER INSERT OR UPDATE OR DELETE ON ne_handhole
FOR EACH ROW
EXECUTE FUNCTION audit_ne_handhole();

-- Optional: Update custom_id on INSERT
DROP TRIGGER IF EXISTS trg_ne_handhole_bi_row_custom_id ON ne_handhole;
CREATE TRIGGER trg_ne_handhole_bi_row_custom_id
BEFORE INSERT ON ne_handhole
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Optional: Update custom_id on geom UPDATE
DROP TRIGGER IF EXISTS trg_ne_handhole_bu_geom_custom_id ON ne_handhole;
CREATE TRIGGER trg_ne_handhole_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_handhole
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Optional: Update adm ids on INSERT
DROP TRIGGER IF EXISTS trg_ne_handhole_bi_row_adm_ids ON ne_handhole;
CREATE TRIGGER trg_ne_handhole_bi_row_adm_ids
BEFORE INSERT ON ne_handhole
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Optional: Update adm ids on geom UPDATE
DROP TRIGGER IF EXISTS trg_ne_handhole_bu_geom_adm_ids ON ne_handhole;
CREATE TRIGGER trg_ne_handhole_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_handhole
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();


CREATE INDEX idx_ne_handhole_geom ON ne_handhole USING GIST (geom);
CREATE INDEX idx_ne_handhole_public_id ON ne_handhole(public_id);
CREATE INDEX idx_ne_handhole_custom_id ON ne_handhole(custom_id);
