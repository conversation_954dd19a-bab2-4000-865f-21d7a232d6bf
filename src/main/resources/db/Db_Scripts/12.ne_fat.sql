CREATE TABLE ne_fat (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,
    name TEXT NOT NULL,
    capacity INT NOT NULL,
    address TEXT,
	
    geom GEOMETRY(Point, 4326) NOT NULL,
	
	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	
	--It can be Handhold, Manhole, Pole, Building
	parent_ne_id INT,
	parent_ne_type TEXT, 
	
	adm1_id INT REFERENCES administrative_01(id),
	adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

-- 1. Audit Table for fat

CREATE TABLE audit_ne_fat (
    id SERIAL PRIMARY KEY,
    ne_fat_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_fat_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_fat(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_fat()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    new_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_fat (ne_fat_id, operation, operated_by, snapshot)
    VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        snapshot_data
    )
    RETURNING id INTO new_audit_id;

    INSERT INTO audit_ne_fat_geom (audit_id, geom)
    VALUES (
        new_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_fat
AFTER INSERT OR UPDATE OR DELETE ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION audit_ne_fat();

-- Insert trigger (ne_fat) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_custom_id ON ne_fat;
CREATE TRIGGER trg_ne_fat_bi_row_custom_id
BEFORE INSERT ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_fat) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_custom_id ON ne_fat;
CREATE TRIGGER trg_ne_fat_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_fat) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_adm_ids ON ne_fat;
CREATE TRIGGER trg_ne_fat_bi_row_adm_ids
BEFORE INSERT ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_fat) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_adm_ids ON ne_fat;
CREATE TRIGGER trg_ne_fat_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_fat_geom ON ne_fat USING GIST (geom);
CREATE INDEX idx_ne_fat_public_id ON ne_fat(public_id);
CREATE INDEX idx_ne_fat_custom_id ON ne_fat(custom_id);