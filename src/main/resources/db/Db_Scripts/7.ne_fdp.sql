-- Lookup table for FDP types
CREATE TABLE lookup_fdp_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,    -- e.g. 'Indoor', 'Outdoor', 'Wall-Mounted' etc.
	description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

INSERT INTO lookup_fdp_types (name, description)
VALUES
('Indoor', 'Installed within buildings or protective enclosures'),
('Outdoor', 'Designed for external environments with weatherproofing'),
('Wall-Mounted', 'Fixed on walls; can be indoor or outdoor'),
('Pole-Mounted', 'Mounted on utility poles; used in aerial deployments'),
('Rack-Mounted', 'Fits into telecom racks in equipment rooms');


-- FDP: name, port (integer), type, pop_id
CREATE TABLE ne_fdp (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    port INT NOT NULL,

    fdp_type_id INT NOT NULL REFERENCES lookup_fdp_types(id),   -- FK to dynamic lookup_fdp_types

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

	pop_id INT NULL REFERENCES ne_pop(id),
	adm1_id INT REFERENCES administrative_01(id),
	adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

-- 1. Audit Table for fdp

CREATE TABLE audit_ne_fdp (
  id SERIAL PRIMARY KEY,
  ne_fdp_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_fdp_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_fdp(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_fdp()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_fdp (
        ne_fdp_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO audit_id;

    INSERT INTO audit_ne_fdp_geom (
        audit_id,
        geom
    ) VALUES (
        audit_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_fdp
AFTER INSERT OR UPDATE OR DELETE ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION audit_ne_fdp();

-- Insert trigger (ne_fdp) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_custom_id ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bi_row_custom_id
BEFORE INSERT ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_fdp) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_custom_id ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_fdp) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_adm_ids ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bi_row_adm_ids
BEFORE INSERT ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_fdp) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_adm_ids ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_fdp_geom ON ne_fdp USING GIST (geom);
CREATE INDEX idx_ne_fdp_public_id ON ne_fdp(public_id);
CREATE INDEX idx_ne_fdp_custom_id ON ne_fdp(custom_id);
CREATE INDEX idx_ne_fdp_type_id ON ne_fdp(fdp_type_id);


INSERT INTO ne_fdp (
    name, port, fdp_type_id, geom, status, created_by, mvno_id
) VALUES
-- FDP 1
('FDP-001', 16, 1,
 ST_SetSRID(ST_MakePoint(35.1886542640988, 0.11673302113241846), 4326),
 'Planned', 1, 1),

-- FDP 2
('FDP-002', 24, 2,
 ST_SetSRID(ST_MakePoint(35.19188624523329, 0.10963058480555787), 4326),
 'InService', 1, 1),

-- FDP 3
('FDP-003', 8, 3,
 ST_SetSRID(ST_MakePoint(35.18904448073948, 0.10212414211143539), 4326),
 'Planned', 1, 1),

-- FDP 4
('FDP-004', 12, 4,
 ST_SetSRID(ST_MakePoint(35.19046529091864, 0.0978632482916737), 4326),
 'Inactive', 1, 1),

-- FDP 5
('FDP-005', 32, 5,
 ST_SetSRID(ST_MakePoint(35.1957632243317, 0.09460394298565689), 4326),
 'InService', 1, 1);
