CREATE TABLE public.tblmstaffuser (
	staffid bigserial NOT NULL,
	username varchar(250) NULL,
	"password" varchar(500) NULL,
	firstname varchar(250) NULL,
	lastname varchar(250) NULL,
	sstatus varchar(100) NULL,
	last_login_time timestamp NULL,
	partnerid int8 NULL,
	is_delete bool DEFAULT false NULL,
	mvnoid int8 NULL,
	branchid int8 NULL,
	createbyname varchar(100) NULL,
	updatebyname varchar(100) NULL,
	createdbystaffid int8 NULL,
	lastmodifiedbystaffid int8 NULL,
	createdate timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	lastmodifieddate timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	service_area_id int8 NULL,
	parent_staff_id int8 NULL,
	country_code varchar(10) NULL,
	total_collected numeric(20, 4) NULL,
	total_transferred numeric(20, 4) NULL,
	available_amount numeric(20, 4) NULL,
	lcoid int8 NULL,
	hrms_id varchar(50) NULL,
	profile_image bytea NULL,
	department varchar(100) NULL,
	oldpassword1 varchar(100) NULL,
	oldpassword2 varchar(100) NULL,
	oldpassword3 varchar(100) NULL,
	otp varchar(10) NULL,
	otpvalidate timestamp NULL,
	sysstaff bool DEFAULT false NOT NULL,
	businessunitid int8 NULL,
	email varchar(100) NULL,
	phone varchar(20) NULL,
	failcount int8 NULL,
	access_level_group_name varchar(255) NULL,
	mvno_deactivation_flag bool NULL,
	"uuid" varchar(255) NULL,
	is_password_expired bool DEFAULT false NULL,
	password_date timestamp NULL,
	roleid int8 NULL,
	CONSTRAINT tblmstaffuser_staffid_unq PRIMARY KEY (staffid)
);

INSERT INTO tblmstaffuser (
  staffid, username, password, firstname, lastname, sstatus,
  last_login_time, partnerid, is_delete, mvnoid, branchid,
  createbyname, updatebyname, createdbystaffid, lastmodifiedbystaffid,
  createdate, lastmodifieddate, service_area_id, parent_staff_id,
  country_code, total_collected, total_transferred, available_amount,
  lcoid, hrms_id, profile_image, department, oldpassword1,
  oldpassword2, oldpassword3, otp, otpvalidate, sysstaff,
  businessunitid, email, phone, failcount,
  access_level_group_name, mvno_deactivation_flag, uuid,
  is_password_expired, password_date, roleid
)
VALUES
(1003, 'nilesh', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO', 'Nilesh', 'Patel', 'ACTIVE',
 NULL, 1, FALSE, 2, NULL,
 'admin admin', 'admin admin', 1, 1,
 '2024-06-17 12:00:00', '2024-06-17 12:00:00',
 NULL, NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, TRUE,
 NULL, '<EMAIL>', '9000000001', 0,
 NULL, NULL, NULL,
 FALSE, NULL, 3),
(1004, 'mehul', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO', 'Mehul', 'Shah', 'ACTIVE',
 NULL, 1, FALSE, 2, NULL,
 'admin admin', 'admin admin', 1, 1,
 '2024-06-17 12:00:00', '2024-06-17 12:00:00',
 NULL, NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, TRUE,
 NULL, '<EMAIL>', '9000000002', 0,
 NULL, NULL, NULL,
 FALSE, NULL, 3),
(1005, 'snehal', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO', 'Snehal', 'Desai', 'ACTIVE',
 NULL, 1, FALSE, 2, NULL,
 'admin admin', 'admin admin', 1, 1,
 '2024-06-17 12:00:00', '2024-06-17 12:00:00',
 NULL, NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, TRUE,
 NULL, '<EMAIL>', '9000000003', 0,
 NULL, NULL, NULL,
 FALSE, NULL, 3),
(1006, 'ravi', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO', 'Ravi', 'Mehta', 'ACTIVE',
 NULL, 1, FALSE, 2, NULL,
 'admin admin', 'admin admin', 1, 1,
 '2024-06-17 12:00:00', '2024-06-17 12:00:00',
 NULL, NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, TRUE,
 NULL, '<EMAIL>', '9000000004', 0,
 NULL, NULL, NULL,
 FALSE, NULL, 3);

INSERT INTO tblmstaffuser (
  staffid, username, password, firstname, lastname, sstatus,
  last_login_time, partnerid, is_delete, mvnoid, branchid,
  createbyname, updatebyname, createdbystaffid, lastmodifiedbystaffid,
  createdate, lastmodifieddate, service_area_id, parent_staff_id,
  country_code, total_collected, total_transferred, available_amount,
  lcoid, hrms_id, profile_image, department, oldpassword1,
  oldpassword2, oldpassword3, otp, otpvalidate, sysstaff,
  businessunitid, email, phone, failcount,
  access_level_group_name, mvno_deactivation_flag, uuid,
  is_password_expired, password_date, roleid
)
VALUES (
  1007, 'darshit', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO',
  'Darshit', 'Darshit', 'ACTIVE',
  NULL, 1, FALSE, 2, NULL,
  'admin admin', 'admin admin', 1, 1,
  '2021-10-03 18:53:48', '2021-10-03 18:53:48',
  NULL, NULL, NULL, NULL, NULL, NULL,
  NULL, NULL, NULL, NULL, NULL,
  NULL, NULL, NULL, NULL, TRUE,
  NULL, '<EMAIL>', '8888888888', 0,
  NULL, NULL, NULL,
  FALSE, NULL, 1
);

INSERT INTO tblmstaffuser (
  staffid, username, password, firstname, lastname, sstatus,
  last_login_time, partnerid, is_delete, mvnoid, branchid,
  createbyname, updatebyname, createdbystaffid, lastmodifiedbystaffid,
  createdate, lastmodifieddate, service_area_id, parent_staff_id,
  country_code, total_collected, total_transferred, available_amount,
  lcoid, hrms_id, profile_image, department, oldpassword1,
  oldpassword2, oldpassword3, otp, otpvalidate, sysstaff,
  businessunitid, email, phone, failcount,
  access_level_group_name, mvno_deactivation_flag, uuid,
  is_password_expired, password_date, roleid
)
VALUES (
  1001, 'vishal', '$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO',
  'Vishal', 'Vishal', 'ACTIVE',
  NULL, 1, FALSE, 2, NULL,
  'admin admin', 'admin admin', 1, 1,
  '2024-01-23 16:05:47', '2024-01-23 16:05:47',
  NULL, NULL, NULL, NULL, NULL, NULL,
  NULL, NULL, NULL, NULL, NULL,
  NULL, NULL, NULL, NULL, TRUE,
  NULL, '<EMAIL>', '9999999999', 0,
  NULL, NULL, NULL,
  FALSE, NULL, 1
);
