CREATE TABLE ne_trench (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,
    name TEXT,
    width_m NUMERIC CHECK (width_m > 0),
    depth_m NUMERIC CHECK (depth_m > 0),
    length_m NUMERIC CHECK (length_m > 0),
	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive','Active', 'In Progress', 'Completed', 'Backfilled')),
    
    owner TEX<PERSON>,
    contractor TEXT,
    geom GEOMETRY(LineString, 4326) NOT NULL,
    related_assets TEXT,
    remarks TEXT,
    
	created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
    
	adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);


CREATE TABLE audit_ne_trench (
    id SERIAL PRIMARY KEY,
    trench_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by TEXT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

CREATE TABLE audit_ne_trench_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_trench(id) ON DELETE CASCADE,
    geom GEOMETRY(LineString, 4326)
);


CREATE OR REPLACE FUNCTION audit_ne_trench()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id TEXT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_trench (trench_id, operation, operated_by, operated_at, snapshot)
    VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
    RETURNING id INTO affected_id;

    INSERT INTO audit_ne_trench_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

--======================= TRIGGER :
CREATE TRIGGER trg_audit_ne_trench
AFTER INSERT OR UPDATE OR DELETE ON ne_trench
FOR EACH ROW
EXECUTE FUNCTION audit_ne_trench();

-- Create custom_id automatically
-- Insert trigger (ne_trench) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_trench_bi_row_custom_id ON ne_trench;
CREATE TRIGGER trg_ne_trench_bi_row_custom_id
BEFORE INSERT ON ne_trench
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

-- Update trigger (ne_trench) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_trench_bu_geom_custom_id ON ne_trench;
CREATE TRIGGER trg_ne_trench_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_trench
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

-- Create adm1_id, adm2_id automatically
-- Insert trigger (ne_trench) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_trench_bi_row_adm_ids ON ne_trench;
CREATE TRIGGER trg_ne_trench_bi_row_adm_ids
BEFORE INSERT ON ne_trench
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();

-- Update trigger (ne_trench) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_trench_bu_geom_adm_ids ON ne_trench;
CREATE TRIGGER trg_ne_trench_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_trench
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();


CREATE INDEX idx_ne_trench_geom ON ne_trench USING GIST (geom);
CREATE INDEX idx_ne_trench_public_id ON ne_trench(public_id);
CREATE INDEX idx_ne_trench_custom_id ON ne_trench(custom_id);


