--Building: name, address, home_passes(int), floors(int), towers(int), survey_status, tenancy, category
CREATE TABLE layer_building (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    home_passes INT,
    floors INT,
    towers INT,
    survey_status TEXT CHECK (survey_status IN ('Planned', 'Completed', 'Rejected')) DEFAULT 'Planned',
    tenancy TEXT CHECK (tenancy IN ('Owned', 'Rented')) NOT NULL,
    category TEXT CHECK (category IN ('Residential', 'Commercial', 'MixedUse')) NOT NULL,

	 geom GEOMETRY(Polygon, 4326) NOT NULL,
	
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for building

CREATE TABLE audit_layer_building (
    id SERIAL PRIMARY KEY,
    layer_building_id INT NOT NULL REFERENCES layer_building(id) ON DELETE CASCADE,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_building_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_layer_building(id) ON DELETE CASCADE,
    geom GEOMETRY(Polygon, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_building()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_building (
        layer_building_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO affected_audit_id;

    INSERT INTO audit_layer_building_geom (
        audit_id,
        geom
    ) VALUES (
        affected_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_building
AFTER INSERT OR UPDATE OR DELETE ON layer_building
FOR EACH ROW
EXECUTE FUNCTION audit_layer_building();

-- 5.Index

CREATE INDEX idx_layer_building_geom ON layer_building USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_building_public_id ON layer_building(public_id);
CREATE UNIQUE INDEX idx_layer_building_custom_id ON layer_building(custom_id);