----------------------------------------------------Network Element Tables--------------------------------------------------------------
----------------------------------------------------1. ne_cable--------------------------------------------------------------
CREATE TABLE cable_specification (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,           -- e.g., '12F', '24F', '48F'
    number_of_cores INTEGER NOT NULL,    -- e.g., 12, 24, 48
    description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

INSERT INTO cable_specification (name, number_of_cores, description)
VALUES
('12F', 12, 'Standard 12 core fiber'),
('24F', 24, 'Standard 24 core fiber'),
('48F', 48, 'High-capacity feeder cable'),
('96F', 96, 'Used for large distribution areas');

CREATE TABLE ne_cable (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),           -- External identifier
    custom_id TEXT UNIQUE,                                       -- Optional human-readable ID

    name TEXT NOT NULL,

    cable_type TEXT CHECK (cable_type IN ('Feeder', 'Distribution', 'Drop')) NOT NULL,
    mounting_type TEXT CHECK (mounting_type IN ('Underground', 'Overhead')) NOT NULL,
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',

    specification_id INT REFERENCES cable_specification(id),

    measured_length_m NUMERIC(10,2),
    gis_length_m NUMERIC(10,2),

    installation_date TIMESTAMPTZ,

    origin_node TEXT,
    origin_node_type TEXT,
    destination_node TEXT,
    destination_node_type TEXT,

    trench_id TEXT,
    duct_id TEXT,

    remarks TEXT,

	adm1_id INT,
	adm2_id INT,

    geom GEOMETRY(LineString, 4326) NOT NULL,

	created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES administrative_02(id)
);
-----------------------------ne_cable table add constraint --------------------------------------------
ALTER TABLE ne_cable
DROP COLUMN cable_type;

ALTER TABLE ne_cable
ADD COLUMN cable_id VARCHAR(50);

ALTER TABLE ne_cable
ADD CONSTRAINT fk_cable_type
FOREIGN KEY (cable_id)
REFERENCES lookup_cable_types(id);

INSERT INTO ne_cable (cable_id)
VALUES
  ('Feeder'),
  ('Distribution'),
  ('Drop');

----------------------------------------------------------------------------
-- gis_length
CREATE OR REPLACE FUNCTION update_ne_cable_gis_length()
RETURNS TRIGGER AS $$
BEGIN
  NEW.gis_length_m := ST_Length(NEW.geom::geography);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Insert trigger (ne_cable) : On insert row to update gis length
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_gis_length ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_gis_length
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_gis_length();

-- Update trigger (ne_cable) : On update geom to update gis length
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_gis_length ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_gis_length
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_gis_length();

--======================= Function: custom_id
-- Get First point from LineString and locate point into administrative_02

CREATE OR REPLACE FUNCTION update_ne_cable_custom_id()
RETURNS TRIGGER AS $$
DECLARE
    start_point geometry;
    adm1 TEXT;
    adm2 TEXT;
    layer_code TEXT;
    last_seq INT;
    next_seq TEXT;
BEGIN
    -- 1. Get the start point of the LineString
    start_point := ST_StartPoint(NEW.geom);

    -- 2. Get adm1_code and adm2_code from administrative_02 where the point is contained
    SELECT a.adm1_code, a.adm2_code
    INTO adm1, adm2
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    -- 3. Get the layer code from layer_master
    SELECT UPPER(lm.code)
    INTO layer_code
    FROM layer_master lm
    WHERE lm.table_name = 'ne_cable'
    LIMIT 1;

    -- 4. Get the last sequence used for this adm2
    SELECT COALESCE(MAX(SPLIT_PART(custom_id, '-', 4)::INT), 0)
    INTO last_seq
    FROM ne_cable
    WHERE custom_id LIKE '%' || adm2 || '%';

    -- 5. Generate the next sequence number
    next_seq := LPAD((last_seq + 1)::TEXT, 3, '0');

    -- 6. Set the final custom_id
    NEW.custom_id := layer_code || '-' || adm1 || '-' || adm2 || '-' || next_seq;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

--======================= TRIGGER :
-- Create custom_id automatically

-- Insert trigger (ne_cable) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_custom_id ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_custom_id
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_custom_id();

-- Update trigger (ne_cable) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_custom_id ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_custom_id();

--======================= Function: adm1_id, adm2_id
-- Get administrative_01(id) and administrative_02(id) based on first point of LineString

CREATE OR REPLACE FUNCTION update_ne_cable_adm_ids()
RETURNS TRIGGER AS $$
DECLARE
    start_point geometry;
    adm1_id INTEGER; -- Primary key for adm1
    adm2_id INTEGER; -- Primary key for adm2 (from the 'id' column of administrative_02)
BEGIN
    -- 1. Get the start point of the LineString (assuming geometry type is LineString)
    start_point := ST_StartPoint(NEW.geom);

    -- 2. Get adm1_id and the administrative_02 id (which will be used as adm2_id) where the point is contained
    SELECT a.adm1_id, a.id -- Using 'a.id' instead of 'a.adm2_id'
    INTO adm1_id, adm2_id
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    -- 3. Set the adm1_id and adm2_id in the new ne_cable record
    NEW.adm1_id := adm1_id;
    NEW.adm2_id := adm2_id;  -- Now using the 'id' from administrative_02 as adm2_id

    -- Return the new record
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

--======================= TRIGGER :
-- Create adm1_id, adm2_id automatically

-- Insert trigger (ne_cable) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_adm_ids ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_adm_ids
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_adm_ids();

-- Update trigger (ne_cable) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_adm_ids ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_adm_ids();

-- Geometry Index
CREATE INDEX idx_ne_cable_geom ON ne_cable USING GIST (geom);

-- Lookup and Join Indexes
CREATE INDEX idx_ne_cable_specification_id ON ne_cable (specification_id);
CREATE INDEX idx_ne_cable_trench_id ON ne_cable (trench_id);
CREATE INDEX idx_ne_cable_duct_id ON ne_cable (duct_id);
CREATE INDEX idx_ne_cable_custom_id ON ne_cable (custom_id);
CREATE INDEX idx_ne_cable_public_id ON ne_cable (public_id);

-- audit
CREATE TABLE audit_ne_cable (
  id SERIAL PRIMARY KEY,
  ne_cable_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

CREATE TABLE audit_ne_cable_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_cable(id) ON DELETE CASCADE,
  geom GEOMETRY(LineString, 4326)
);

-- Trigger Function
CREATE OR REPLACE FUNCTION trg_audit_ne_cable()
RETURNS TRIGGER AS $$
DECLARE
  snapshot_data JSONB;
  inserted_id INT;
  user_id BIGINT;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    snapshot_data := to_jsonb(OLD) - 'geom';
	user_id := COALESCE(OLD.modified_by, OLD.created_by);
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      OLD.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, OLD.geom);

    RETURN OLD;

  ELSIF (TG_OP = 'UPDATE') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := COALESCE(NEW.modified_by, NEW.created_by);
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;

  ELSIF (TG_OP = 'INSERT') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := NEW.created_by;
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Attach Trigger
DROP TRIGGER IF EXISTS trg_audit_ne_cable ON ne_cable;
CREATE TRIGGER trg_audit_ne_cable
AFTER INSERT OR UPDATE OR DELETE ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION trg_audit_ne_cable();

-- select * from ne_cable;

------------------Insert data----------------------------
------------------Kenya data

INSERT INTO public.ne_cable (
	public_id, name, cable_type_id, mounting_type, status,
	specification_id, measured_length_m, gis_length_m, installation_date,
	--origin_node_id, origin_node_type, destination_node_id, destination_node_type, trench_id, duct_id, 
	remarks,
	geom, created_by, created_on
) VALUES
-- Cable 1
(gen_random_uuid(), 'Feeder Cable E', 1, 'Underground', 'Planned',
1, 100.00, 500.00, '2025-01-10',
--'POP-03', 'POP', 'FDC-08', 'FDC', 'TRN-21', 'DCT-11', 
'Route section 1',
ST_GeomFromText('LINESTRING(36.70870590840411 0.26321914232845245, 36.90982323729378 0.20936388091051583)', 4326), 101, now()),

-- Cable 2
(gen_random_uuid(), 'Distribution Cable F', 2, 'Overhead', 'InService',
2, 80.00, 420.00, '2024-11-05',
--'FDC-08', 'FDC', 'FAT-12', 'FAT', 'TRN-22', 'DCT-12', 
'Route section 2',
ST_GeomFromText('LINESTRING(37.07898525334082 -0.2721150873351803, 37.355531420854305 -0.06348214959845677)', 4326), 102, now()),

-- Cable 3
(gen_random_uuid(), 'Drop Cable G', 3, 'Underground', 'Inactive',
3, 60.00, 310.00, '2024-06-18',
--'FAT-13', 'FAT', 'Building-25', 'Building', 'TRN-23', 'DCT-13', 
'Route section 3',
ST_GeomFromText('LINESTRING(36.90995996891036 -0.5702608177886077, 37.5060749925006 -0.4624679166533383)', 4326), 103, now()),

-- Cable 4
(gen_random_uuid(), 'Feeder Cable H', 1, 'Underground', 'Planned',
1, 140.00, 600.00, '2025-02-15',
--'POP-04', 'POP', 'FDC-09', 'FDC', 'TRN-24', 'DCT-14', 
'Route section 4',
ST_GeomFromText('LINESTRING(38.30833401130394 0.10027405684628832, 38.14973636414325 -0.1528631616980789)', 4326), 101, now());

----------------------------------------------------

----------------------------------------------------2. ne_fat--------------------------------------------------------------

CREATE TABLE ne_fat (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    capacity INT NOT NULL,
    address TEXT,

	adm1_id INT,
	adm2_id INT,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- 1. Audit Table for fat

CREATE TABLE audit_ne_fat (
    id SERIAL PRIMARY KEY,
    ne_fat_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_fat_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_fat(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_fat()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    new_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_fat (ne_fat_id, operation, operated_by, snapshot)
    VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        snapshot_data
    )
    RETURNING id INTO new_audit_id;

    INSERT INTO audit_ne_fat_geom (audit_id, geom)
    VALUES (
        new_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_fat
AFTER INSERT OR UPDATE OR DELETE ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION audit_ne_fat();

-- Insert trigger (ne_fat) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_custom_id ON ne_fat;
CREATE TRIGGER trg_ne_fat_bi_row_custom_id
BEFORE INSERT ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_fat) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_custom_id ON ne_fat;
CREATE TRIGGER trg_ne_fat_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_fat) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_adm_ids ON ne_fat;
CREATE TRIGGER trg_ne_fat_bi_row_adm_ids
BEFORE INSERT ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_fat) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_adm_ids ON ne_fat;
CREATE TRIGGER trg_ne_fat_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_fat
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_fat_geom ON ne_fat USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_fat_public_id ON ne_fat(public_id);

---------------Insert data---------------------------

INSERT INTO ne_fat (
    public_id,
    name,
    capacity,
    address,
    geom,
    status,
    created_by,
    created_on,
	adm1_id,
	adm2_id
) VALUES
-- Record 1
(gen_random_uuid(), 'FAT - Alpha Zone', 16, '12 First Avenue, Alpha City',
 ST_GeomFromText('POINT(35.19148257092772 0.11348546845972862)', 4326), 'InService', 1, NOW(),31,93),

-- Record 2
(gen_random_uuid(), 'FAT - Beta Sector', 24, '45 Beta Street, Beta Town',
 ST_GeomFromText('POINT(35.19433749279838 0.10617731147048914)', 4326), 'Planned', 2, NOW(),31,93),

-- Record 3
(gen_random_uuid(), 'FAT - Gamma Node', 32, '78 Gamma Lane, Gamma Area',
 ST_GeomFromText('POINT(35.18843159646519 0.10050324501398222)', 4326), 'InService', 3, NOW(),31,93),

-- Record 4
(gen_random_uuid(), 'FAT - Delta Unit', 16, '22 Delta Road, Delta Colony',
 ST_GeomFromText('POINT(35.1797179333993 0.11450002068390575)', 4326), 'Inactive', 1, NOW(),31,93),

-- Record 5
(gen_random_uuid(), 'FAT - Epsilon Point', 48, '99 Epsilon Blvd, Epsilon District',
 ST_GeomFromText('POINT(35.1776485567847 0.10457937559519337)', 4326), 'InService', 2, NOW(),31,93);

 ----------------------------------------------------3. ne_pop--------------------------------------------------------------

CREATE TABLE ne_pop (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    category TEXT CHECK (category IN ('Tier1', 'Tier2', 'Tier3')) NOT NULL,

	adm1_id INT,
	adm2_id INT,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL,
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- 1. Audit Table for pop

CREATE TABLE audit_ne_pop (
  id SERIAL PRIMARY KEY,
  ne_pop_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_pop_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_pop(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_pop()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_pop (
        ne_pop_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_id;

    INSERT INTO audit_ne_pop_geom (audit_id, geom)
    VALUES (
        audit_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_pop
AFTER INSERT OR UPDATE OR DELETE ON ne_pop
FOR EACH ROW
EXECUTE FUNCTION audit_ne_pop();

-- Insert trigger (ne_pop) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_pop_bi_row_custom_id ON ne_pop;
CREATE TRIGGER trg_ne_pop_bi_row_custom_id
BEFORE INSERT ON ne_pop
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_pop) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_pop_bu_geom_custom_id ON ne_pop;
CREATE TRIGGER trg_ne_pop_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_pop
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_pop) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_pop_bi_row_adm_ids ON ne_pop;
CREATE TRIGGER trg_ne_pop_bi_row_adm_ids
BEFORE INSERT ON ne_pop
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_pop) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_pop_bu_geom_adm_ids ON ne_pop;
CREATE TRIGGER trg_ne_pop_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_pop
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_pop_geom ON ne_pop USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_pop_public_id ON ne_pop(public_id);
CREATE UNIQUE INDEX idx_ne_pop_custom_id ON ne_pop(custom_id);

----------Insert data-------------------------

INSERT INTO ne_pop (
    public_id,
    name,
    address,
    category,
    geom,
    status,
    created_by,
    created_on
) VALUES
-- Record 1
(gen_random_uuid(), 'POP - Central Hub', '10 Main Street, Central City',
 'Tier1', ST_GeomFromText('POINT(35.172577097998754 0.10398122032007961)', 4326), 'InService', 1, NOW()),

-- Record 2
(gen_random_uuid(), 'POP - East Wing', '55 East Road, Eastville',
 'Tier2', ST_GeomFromText('POINT(35.17500548386931 0.10215978808966497)', 4326), 'InService', 2, NOW()),

-- Record 3
(gen_random_uuid(), 'POP - West Node', '99 West Street, Westborough',
 'Tier3', ST_GeomFromText('POINT(35.18109570186766 0.10133652812864113)', 4326), 'Planned', 3, NOW()),

-- Record 4
(gen_random_uuid(), 'POP - North Unit', '44 North Blvd, Northfield',
 'Tier2', ST_GeomFromText('POINT(35.18251439537727 0.09566521732357103)', 4326), 'Inactive', 1, NOW()),

-- Record 5
(gen_random_uuid(), 'POP - South Access', '123 South Ave, Southend',
 'Tier1', ST_GeomFromText('POINT(35.1705870026571 0.11998073697351685)', 4326), 'InService', 2, NOW());


----------------------------------------------------4. ne_fdp--------------------------------------------------------------

-- Lookup table for FDP types
CREATE TABLE lookup_fdp_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,    -- e.g. 'Indoor', 'Outdoor', 'Wall-Mounted' etc.
	description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

-- FDP: name, port (integer), type, pop_id
CREATE TABLE ne_fdp (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    port INT NOT NULL,

    type_id INT NOT NULL REFERENCES lookup_fdp_types(id),   -- FK to dynamic lookup_fdp_types

	adm1_id INT,
	adm2_id INT,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL,
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

	pop_id INT NULL,
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- 1. Audit Table for fdp

CREATE TABLE audit_ne_fdp (
  id SERIAL PRIMARY KEY,
  ne_fdp_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_fdp_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_fdp(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_fdp()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_fdp (
        ne_fdp_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO audit_id;

    INSERT INTO audit_ne_fdp_geom (
        audit_id,
        geom
    ) VALUES (
        audit_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_fdp
AFTER INSERT OR UPDATE OR DELETE ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION audit_ne_fdp();

-- Insert trigger (ne_fdp) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_custom_id ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bi_row_custom_id
BEFORE INSERT ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_fdp) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_custom_id ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_fdp) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_adm_ids ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bi_row_adm_ids
BEFORE INSERT ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_fdp) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_adm_ids ON ne_fdp;
CREATE TRIGGER trg_ne_fdp_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_fdp
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_fdp_geom ON ne_fdp USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_fdp_public_id ON ne_fdp(public_id);

-------------Insert data--------------------------
INSERT INTO lookup_fdp_types (name, description)
VALUES
('Indoor', 'Installed within buildings or protective enclosures'),
('Outdoor', 'Designed for external environments with weatherproofing'),
('Wall-Mounted', 'Fixed on walls; can be indoor or outdoor'),
('Pole-Mounted', 'Mounted on utility poles; used in aerial deployments'),
('Rack-Mounted', 'Fits into telecom racks in equipment rooms');


INSERT INTO ne_fdp (
    name, port, type_id, geom, status, created_by, pop_id
) VALUES
-- FDP 1
('FDP-001', 16, 1,
 ST_SetSRID(ST_MakePoint(35.1886542640988, 0.11673302113241846), 4326),
 'Planned', 1, 1),

-- FDP 2
('FDP-002', 24, 2,
 ST_SetSRID(ST_MakePoint(35.19188624523329, 0.10963058480555787), 4326),
 'InService', 1, 2),

-- FDP 3
('FDP-003', 8, 3,
 ST_SetSRID(ST_MakePoint(35.18904448073948, 0.10212414211143539), 4326),
 'Planned', 1, 3),

-- FDP 4
('FDP-004', 12, 4,
 ST_SetSRID(ST_MakePoint(35.19046529091864, 0.0978632482916737), 4326),
 'Inactive', 1, 4),

-- FDP 5
('FDP-005', 32, 5,
 ST_SetSRID(ST_MakePoint(35.1957632243317, 0.09460394298565689), 4326),
 'InService', 1, 5);

 ----------------------------------------------------5. ne_fdc--------------------------------------------------------------

CREATE TABLE ne_fdc (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    capacity INT NOT NULL,
    address TEXT,

	adm1_id INT,
	adm2_id INT,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- 1. Audit Table for fdc

CREATE TABLE audit_ne_fdc (
  id SERIAL PRIMARY KEY,
  ne_fdc_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_fdc_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_fdc(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_fdc()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_fdc (
        ne_fdc_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        affected_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO affected_id;

    INSERT INTO audit_ne_fdc_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_fdc
AFTER INSERT OR UPDATE OR DELETE ON ne_fdc
FOR EACH ROW
EXECUTE FUNCTION audit_ne_fdc();

-- Insert trigger (ne_fdc) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdc_bi_row_custom_id ON ne_fdc;
CREATE TRIGGER trg_ne_fdc_bi_row_custom_id
BEFORE INSERT ON ne_fdc
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_fdc) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_fdc_bu_geom_custom_id ON ne_fdc;
CREATE TRIGGER trg_ne_fdc_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_fdc
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_fdc) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdc_bi_row_adm_ids ON ne_fdc;
CREATE TRIGGER trg_ne_fdc_bi_row_adm_ids
BEFORE INSERT ON ne_fdc
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_fdc) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_fdc_bu_geom_adm_ids ON ne_fdc;
CREATE TRIGGER trg_ne_fdc_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_fdc
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_fdc_geom ON ne_fdc USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_fdc_public_id ON ne_fdc(public_id);


-------------Insert data--------------------------
INSERT INTO ne_fdc (
    name, capacity, address, geom, status, created_by
) VALUES
-- FDC 1
('FDC-001', 96, '12 MG Road, Nairobi',
 ST_SetSRID(ST_MakePoint(35.17970116744755, 0.11105292044243242), 4326), 'Planned', 1),

-- FDC 2
('FDC-002', 144, '25 Brigade Road, Nairobi',
 ST_SetSRID(ST_MakePoint(35.17825871110199, 0.10498274995424595), 4326), 'InService', 1),

-- FDC 3
('FDC-003', 72, '101 Indiranagar, Nairobi',
 ST_SetSRID(ST_MakePoint(35.18170943834181, 0.1021428622422178), 4326), 'Planned', 1),

-- FDC 4
('FDC-004', 120, '88 Koramangala, Nairobi',
 ST_SetSRID(ST_MakePoint(35.185987744281846, 0.10131974330923299), 4326), 'Inactive', 1),

-- FDC 5
('FDC-005', 60, '9 Whitefield, Nairobi',
 ST_SetSRID(ST_MakePoint(35.19329857122821, 0.11064597208927296), 4326), 'InService', 1);

----------------------------------------------------6. ne_customer--------------------------------------------------------------

CREATE TABLE ne_customer (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    port INT CHECK (port > 0),
    activation_date DATE,
    customer_type TEXT CHECK (customer_type IN ('Residential', 'Enterprise', 'SMB')) NOT NULL,

	adm1_id INT,
	adm2_id INT,

	geom GEOMETRY(Point, 4326) NOT NULL,

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	fat_id INT REFERENCES ne_fat(id),
	CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- 1. Audit Table for customer

CREATE TABLE audit_ne_customer (
    id SERIAL PRIMARY KEY,
    ne_customer_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_customer_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_customer(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_customer()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_rec_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        audit_rec_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        audit_rec_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_customer (
        ne_customer_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        audit_rec_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_rec_id;

    INSERT INTO audit_ne_customer_geom (
        audit_id,
        geom
    ) VALUES (
        audit_rec_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_customer
AFTER INSERT OR UPDATE OR DELETE ON ne_customer
FOR EACH ROW
EXECUTE FUNCTION audit_ne_customer();

-- Insert trigger (ne_customer) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_customer_bi_row_custom_id ON ne_customer;
CREATE TRIGGER trg_ne_customer_bi_row_custom_id
BEFORE INSERT ON ne_customer
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_customer) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_customer_bu_geom_custom_id ON ne_customer;
CREATE TRIGGER trg_ne_customer_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_customer
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_customer) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_customer_bi_row_adm_ids ON ne_customer;
CREATE TRIGGER trg_ne_customer_bi_row_adm_ids
BEFORE INSERT ON ne_customer
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_customer) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_customer_bu_geom_adm_ids ON ne_customer;
CREATE TRIGGER trg_ne_customer_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_customer
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_customer_geom ON ne_customer USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_customer_public_id ON ne_customer(public_id);
CREATE UNIQUE INDEX idx_ne_customer_custom_id ON ne_customer(custom_id);

-----------------Insert Data-----------------------------

INSERT INTO public.ne_customer (
    name, address, port, activation_date, customer_type,
    geom, status, created_by, fat_id, adm1_id, adm2_id
) VALUES
(
    'Alice Johnson',
    '12 Fiber Street',
    1,
    '2024-12-10',
    'Residential',
    ST_GeomFromText('POINT(35.180111559515524 0.11490019549189867)', 4326),
    'InService',
    1,
    1,
    31,
    93
),
(
    'Beta Solutions Pvt Ltd',
    '88 Tech Park Lane',
    2,
    '2025-01-15',
    'Enterprise',
    ST_GeomFromText('POINT(35.19133730337671 0.1124929888323436)', 4326),
    'InService',
    1,
    1,
    31,
    93
),
(
    'Corner Cafe',
    '25 Market Road',
    3,
    '2025-02-05',
    'SMB',
    ST_GeomFromText('POINT(35.184686466604916 0.0994750334468506)', 4326),
    'Planned',
    1,
    1,
    31,
    93
);

----------------------------------------------------7. ne_building--------------------------------------------------------------

CREATE TABLE ne_building (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    home_passes INT,
    floors INT,
    towers INT,
    survey_status TEXT CHECK (survey_status IN ('Planned', 'Completed', 'Rejected')) DEFAULT 'Planned',
    tenancy TEXT CHECK (tenancy IN ('Owned', 'Rented')) NOT NULL,
    category TEXT CHECK (category IN ('Residential', 'Commercial', 'MixedUse')) NOT NULL,

    adm1_id INT,
    adm2_id INT,

    geom GEOMETRY(Point, 4326) NOT NULL,

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

    CONSTRAINT fk_cables_adm1 FOREIGN KEY (adm1_id) REFERENCES administrative_01(id),
    CONSTRAINT fk_cables_adm2 FOREIGN KEY (adm2_id) REFERENCES administrative_02(id)
);


-- 1. Audit Table for building

CREATE TABLE audit_ne_building (
    id SERIAL PRIMARY KEY,
    ne_building_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_building_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_building(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_building()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_building (
        ne_building_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO affected_audit_id;

    INSERT INTO audit_ne_building_geom (
        audit_id,
        geom
    ) VALUES (
        affected_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_building
AFTER INSERT OR UPDATE OR DELETE ON ne_building
FOR EACH ROW
EXECUTE FUNCTION audit_ne_building();

-- Insert trigger (ne_building) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_building_bi_row_custom_id ON ne_building;
CREATE TRIGGER trg_ne_building_bi_row_custom_id
BEFORE INSERT ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_building) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_building_bu_geom_custom_id ON ne_building;
CREATE TRIGGER trg_ne_building_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_building) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_building_bi_row_adm_ids ON ne_building;
CREATE TRIGGER trg_ne_building_bi_row_adm_ids
BEFORE INSERT ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_building) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_building_bu_geom_adm_ids ON ne_building;
CREATE TRIGGER trg_ne_building_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_building_geom ON ne_building USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_building_public_id ON ne_building(public_id);
CREATE UNIQUE INDEX idx_ne_building_custom_id ON ne_building(custom_id);

-----------------------------Insert Data----------------------------------------------

   INSERT INTO public.ne_building (
    name, address, home_passes, floors, towers,
    survey_status, tenancy, category, geom, status,
    created_by
) VALUES
-- B004
(
    'Vision Heights',
    '101 Optic Park',
    120,
    8,
    2,
    'Completed',
    'Owned',
    'Residential',
    ST_SetSRID(ST_MakePoint(35.18946003253629, 0.10983210161259649), 4326),
    'InService',
    1
),
-- B005
(
    'Mini Mart Complex',
    '202 Micro Zone',
    20,
    1,
    1,
    'Completed',
    'Rented',
    'Commercial',
    ST_SetSRID(ST_MakePoint(35.18966013066333, 0.105570973436798), 4326),
    'Planned',
    1
),
-- B006
(
    'Riverbend Estates',
    '303 Stream Drive',
    75,
    3,
    3,
    'Completed',
    'Owned',
    'Residential',
    ST_SetSRID(ST_MakePoint(35.18599231637177, 0.10314261292906224), 4326),
    'InService',
    1
),
-- B007
(
    'FiberTech Park',
    '404 Data Valley',
    95,
    6,
    2,
    'Completed',
    'Rented',
    'MixedUse',
    ST_SetSRID(ST_MakePoint(35.17728154657976, 0.11551469887237431), 4326),
    'Planned',
    1
),
-- B008 (new record from the 5th coordinate)
(
    'OptiGrid Hub',
    '505 Network Lane',
    60,
    4,
    1,
    'Completed',
    'Owned',
    'Commercial',
    ST_SetSRID(ST_MakePoint(35.17562333726531, 0.1059969524179536), 4326),
    'InService',
    1
);

----------------------------------------------------8. ne_splitter--------------------------------------------------------------
-- Splitter: name, port_ratio, parent_box_id, parent_box_type

CREATE TABLE splitter_specification (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    port_ratio TEXT NOT NULL,    -- Storing 1:8 means 1 input and 8 output ports
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

CREATE TABLE ne_splitter (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,

    specification_id INT REFERENCES splitter_specification(id),

	parent_fat_id INT REFERENCES ne_fat(id),   -- FK to dynamic FAT
    parent_fdc_id INT REFERENCES ne_fdc(id),   -- FK to dynamic FDC

	parent_box_type TEXT CHECK (parent_box_type IN ('FAT', 'FDC')) NOT NULL,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for splitter

CREATE TABLE audit_ne_splitter (
  id SERIAL PRIMARY KEY,
  splitter_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_splitter_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_splitter(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_splitter()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_rec_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_splitter (
        splitter_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_rec_id;

    INSERT INTO audit_ne_splitter_geom (audit_id, geom)
    VALUES (
        audit_rec_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_splitter
AFTER INSERT OR UPDATE OR DELETE ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION audit_ne_splitter();

-- Insert trigger (ne_splitter) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_splitter_bi_row_custom_id ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bi_row_custom_id
BEFORE INSERT ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_splitter) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_splitter_bu_geom_custom_id ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- 5.Index

CREATE INDEX idx_ne_splitter_geom ON ne_splitter USING GIST (geom);
CREATE UNIQUE INDEX idx_ne_splitter_public_id ON ne_splitter(public_id);

-------------Insert data--------------------------

INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:4 Splitter', '1:4', 'Compact size splitter for wall mounting');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:8 Splitter', '1:8', 'Suitable for medium distribution');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:16 Splitter', '1:16', 'High-capacity splitter');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:32 Splitter', '1:32', 'Large capacity splitter for POP usage');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:2 Splitter', '1:2', 'Low-ratio splitter for indoor use');


INSERT INTO ne_splitter (
    name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by
) VALUES
-- Splitter 1 (FAT)
('Splitter-001', 1, 1, null, 'FAT', ST_SetSRID(ST_MakePoint(35.18804510417621, 0.11287607146169876), 4326), 'Inactive', 1),

-- Splitter 2 (FDC)
('Splitter-002', 2, null, 1, 'FDC', ST_SetSRID(ST_MakePoint(35.18011477259262, 0.11247145201639341), 4326), 'Planned', 1),

-- Splitter 3 (FAT)
('Splitter-003', 3, 2, null, 'FAT', ST_SetSRID(ST_MakePoint(35.180501548217705, 0.10619110905665252), 4326), 'Inactive', 1),

-- Splitter 4 (FDC)
('Splitter-004', 4, null, 2, 'FDC', ST_SetSRID(ST_MakePoint(35.185175800267245, 0.10233403481612413), 4326), 'Planned', 1),

-- Splitter 5 (FAT)
('Splitter-005', 5, 3, null, 'FAT', ST_SetSRID(ST_MakePoint(35.19006679229861, 0.11105006426789998), 4326), 'InService', 1);
