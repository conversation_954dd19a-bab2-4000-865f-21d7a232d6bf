CREATE EXTENSION postgis;

-- Execute for gen_random_uuid();
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE TABLE cable_specifications (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,           -- e.g., '12F', '24F', '48F'
    number_of_cores INTEGER NOT NULL,    -- e.g., 12, 24, 48
    description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

INSERT INTO cable_specifications (name, number_of_cores, description)
VALUES
('12F', 12, 'Standard 12 core fiber'),
('24F', 24, 'Standard 24 core fiber'),
('48F', 48, 'High-capacity feeder cable'),
('96F', 96, 'Used for large distribution areas');



--DROP TABLE IF EXISTS layer_cables;

CREATE TABLE layer_cables (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),           -- External identifier
    custom_id TEXT UNIQUE,                                       -- Optional human-readable ID

    name TEXT NOT NULL,

    cable_type TEXT CHECK (cable_type IN ('Feeder', 'Distribution', 'Drop')) NOT NULL,
    mounting_type TEXT CHECK (mounting_type IN ('Underground', 'Overhead')) NOT NULL,
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',

    specification_id INT REFERENCES cable_specifications(id),

    measured_length_m NUMERIC(10,2),
    gis_length_m NUMERIC(10,2),

    installation_date DATE,

    origin_node TEXT,
    origin_node_type TEXT,
    destination_node TEXT,
    destination_node_type TEXT,

    trench_id TEXT,
    duct_id TEXT,

    remarks TEXT,

    geom GEOMETRY(LineString, 4326) NOT NULL,

	created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

CREATE OR REPLACE FUNCTION update_layer_cables_gis_length()
RETURNS TRIGGER AS $$
BEGIN
  NEW.gis_length_m := ST_Length(NEW.geom::geography);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_gis_length
BEFORE INSERT OR UPDATE ON layer_cables
FOR EACH ROW
EXECUTE FUNCTION update_layer_cables_gis_length();

-- Geometry Index
CREATE INDEX idx_layer_cables_geom ON layer_cables USING GIST (geom);

-- Lookup and Join Indexes
CREATE INDEX idx_layer_cables_specification_id ON layer_cables (specification_id);
CREATE INDEX idx_layer_cables_trench_id ON layer_cables (trench_id);
CREATE INDEX idx_layer_cables_duct_id ON layer_cables (duct_id);
CREATE INDEX idx_layer_cables_custom_id ON layer_cables (custom_id);
CREATE INDEX idx_layer_cables_public_id ON layer_cables (public_id);

-- audit
CREATE TABLE audit_layer_cables (
  id SERIAL PRIMARY KEY,
  layer_cable_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

CREATE TABLE audit_layer_cable_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_layer_cables(id) ON DELETE CASCADE,
  geom GEOMETRY(LineString, 4326)
);


-- Trigger Function
CREATE OR REPLACE FUNCTION trg_audit_layer_cables()
RETURNS TRIGGER AS $$
DECLARE
  snapshot_data JSONB;
  inserted_id INT;
  user_id BIGINT;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    snapshot_data := to_jsonb(OLD) - 'geom';
	user_id := COALESCE(OLD.modified_by, OLD.created_by);
    INSERT INTO audit_layer_cables (
      layer_cable_id, operation, operated_by, snapshot
    ) VALUES (
      OLD.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_layer_cable_geom (audit_id, geom)
    VALUES (inserted_id, OLD.geom);

    RETURN OLD;

  ELSIF (TG_OP = 'UPDATE') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := COALESCE(NEW.modified_by, NEW.created_by);
    INSERT INTO audit_layer_cables (
      layer_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_layer_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;

  ELSIF (TG_OP = 'INSERT') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := NEW.created_by;
    INSERT INTO audit_layer_cables (
      layer_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_layer_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;


-- Attach Trigger
DROP TRIGGER IF EXISTS trg_audit_layer_cables ON layer_cables;
CREATE TRIGGER trg_audit_layer_cables
AFTER INSERT OR UPDATE OR DELETE ON layer_cables
FOR EACH ROW
EXECUTE FUNCTION trg_audit_layer_cables();


-- select * from layer_cables;


------------------Insert data----------------------------

INSERT INTO layer_cables
(id, public_id, custom_id, "name", cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, origin_node, origin_node_type, destination_node, destination_node_type, trench_id, duct_id, remarks, geom, created_by, created_on, modified_on, modified_by)
VALUES(1, '6abcde71-6229-484d-94b8-f43090eb92c2'::uuid, 'CABLE-1001', 'Feeder Cable A', 'Feeder', 'Underground', 'Planned', 1, 120.50, 696.23, '2024-04-01', 'POP-01', 'POP', 'FDC-05', 'FDC', 'TRN-12', 'DCT-7', 'Installed along main road', 'SRID=4326;LINESTRING (77.5946 12.9716, 77.6 12.975)'::public.geometry, 101, '2025-05-01 12:28:53.895', NULL, NULL);
INSERT INTO layer_cables
(id, public_id, custom_id, "name", cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, origin_node, origin_node_type, destination_node, destination_node_type, trench_id, duct_id, remarks, geom, created_by, created_on, modified_on, modified_by)
VALUES(2, '3ba9d614-57f2-4ec5-afda-b3445530ef3c'::uuid, 'CABLE-1002', 'Distribution Cable B', 'Distribution', 'Overhead', 'InService', 2, 80.00, 457.19, '2024-07-15', 'FDC-05', 'FDC', 'FAT-11', 'FAT', 'TRN-13', 'DCT-5', 'Spanning across poles', 'SRID=4326;LINESTRING (77.601 12.9755, 77.605 12.9768)'::public.geometry, 102, '2025-05-01 12:38:15.734', NULL, NULL);
INSERT INTO layer_cables
(id, public_id, custom_id, "name", cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, origin_node, origin_node_type, destination_node, destination_node_type, trench_id, duct_id, remarks, geom, created_by, created_on, modified_on, modified_by)
VALUES(3, 'c56c3fb7-947b-4ec4-99b7-294ef7898846'::uuid, 'CABLE-1003', 'Drop Cable C', 'Drop', 'Underground', 'Inactive', 3, 25.00, 210.02, '2023-12-10', 'FAT-11', 'FAT', 'Building-21', 'Building', 'TRN-15', 'DCT-6', 'Old line marked for replacement', 'SRID=4326;LINESTRING (77.605 12.9768, 77.6065 12.978)'::public.geometry, 103, '2025-05-01 12:38:15.821', NULL, NULL);
INSERT INTO layer_cables
(id, public_id, custom_id, "name", cable_type, mounting_type, status, specification_id, measured_length_m, gis_length_m, installation_date, origin_node, origin_node_type, destination_node, destination_node_type, trench_id, duct_id, remarks, geom, created_by, created_on, modified_on, modified_by)
VALUES(4, '8a6085d0-ac1e-45e6-8272-90eabeca68e4'::uuid, 'CABLE-1004', 'Feeder Cable D', 'Feeder', 'Underground', 'Planned', 1, 150.00, 608.90, '2025-01-25', 'POP-02', 'POP', 'FDC-07', 'FDC', 'TRN-20', 'DCT-9', 'Laying along new development route', 'SRID=4326;LINESTRING (77.61 12.98, 77.615 12.9825)'::public.geometry, 101, '2025-05-01 12:38:15.910', NULL, NULL);
