
-- Lookup table for FDP types
CREATE TABLE lookup_fdp_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,    -- e.g. 'Indoor', 'Outdoor', 'Wall-Mounted' etc.
	description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

-- FDP: name, ports (integer), type, pop_id (fk)
CREATE TABLE layer_fdp (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    ports INT NOT NULL,

    type_id INT NOT NULL REFERENCES lookup_fdp_types(id),   -- FK to dynamic lookup_fdp_types
	
    geom GEOMETRY(Point, 4326) NOT NULL,
	
	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	
	pop_id INT NOT NULL REFERENCES layer_pop(id)
);

-- 1. Audit Table for fdp

CREATE TABLE audit_layer_fdp (
  id SERIAL PRIMARY KEY,
  layer_fdp_id INT NOT NULL REFERENCES layer_fdp(id) ON DELETE CASCADE,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_fdp_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_layer_fdp(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_fdp()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_fdp (
        layer_fdp_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO audit_id;

    INSERT INTO audit_layer_fdp_geom (
        audit_id,
        geom
    ) VALUES (
        audit_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_fdp
AFTER INSERT OR UPDATE OR DELETE ON layer_fdp
FOR EACH ROW
EXECUTE FUNCTION audit_layer_fdp();

-- 5.Index

CREATE INDEX idx_layer_fdp_geom ON layer_fdp USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_fdp_public_id ON layer_fdp(public_id);





-------------Insert data--------------------------

INSERT INTO lookup_fdp_types (name, description)
VALUES
('Indoor', 'Installed within buildings or protective enclosures'),
('Outdoor', 'Designed for external environments with weatherproofing'),
('Wall-Mounted', 'Fixed on walls; can be indoor or outdoor'),
('Pole-Mounted', 'Mounted on utility poles; used in aerial deployments'),
('Rack-Mounted', 'Fits into telecom racks in equipment rooms');


INSERT INTO layer_fdp (custom_id, name, ports, type_id, geom, status, created_by, pop_id)
VALUES
('FDP-CUST-001', 'FDP-001', 16, 1, ST_SetSRID(ST_MakePoint(77.5946, 12.9716), 4326), 'Planned', 1, 1),
('FDP-CUST-002', 'FDP-002', 24, 2, ST_SetSRID(ST_MakePoint(77.5950, 12.9720), 4326), 'InService', 1, 2),
('FDP-CUST-003', 'FDP-003', 8, 3, ST_SetSRID(ST_MakePoint(77.5965, 12.9699), 4326), 'Planned', 1, 3),
('FDP-CUST-004', 'FDP-004', 12, 4, ST_SetSRID(ST_MakePoint(77.5972, 12.9705), 4326), 'Inactive', 1, 4),
('FDP-CUST-005', 'FDP-005', 32, 5, ST_SetSRID(ST_MakePoint(77.5988, 12.9730), 4326), 'InService', 1, 5);




