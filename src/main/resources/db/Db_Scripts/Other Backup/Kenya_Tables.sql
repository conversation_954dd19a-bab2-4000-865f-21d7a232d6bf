CREATE TABLE layer_master (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT,
	table_name TEXT,
    status TEXT CHECK (status IN ('Active', 'Inactive')) DEFAULT 'Active'
);

INSERT INTO layer_master (name, display_name, table_name, status) VALUES
('Administrative Boundary Level 0', 'Country', 'administrative_00','Active'),
('Administrative Boundary Level 1', 'County', 'administrative_01', 'Active'),
('Administrative Boundary Level 2', 'District', 'administrative_02', 'Active');



----------------------------------------------------

--UPDATE administrative_02 
--SET geom = ST_SetSRID(geom, 4326);

--UPDATE ken_admbnda_adm2_iebc_20191031  
--SET geom = ST_SetSRID(geom, 4326)
--WHERE ST_SRID(geom) = 0;

--------------------------------Data for country---------------------------------------------------

CREATE TABLE administrative_00 (
    id serial4 NOT NULL,
    adm0_name varchar(50) NULL,
    adm0_code varchar(50) NULL,
    geom geometry(multipolygon, 4326) NULL,
    CONSTRAINT administrative_00_pkey PRIMARY KEY (id)
);

CREATE INDEX administrative_00_geom_idx ON administrative_00 USING gist (geom);

-------------------Insert data from sample file---------------------------
INSERT INTO public.administrative_00 (
    adm0_name,
    adm0_code,
    geom
)
SELECT
    adm0_en AS adm0_name,
    adm0_pcode AS adm0_code,
    ST_Transform(geom, 4326)::geometry(MULTIPOLYGON, 4326)
FROM
    ken_admbnda_adm0_iebc_20191031
WHERE
    geom IS NOT NULL;


--------------------------------Data for county/state---------------------------------------------------

CREATE TABLE administrative_01 (
    id serial4 NOT NULL,
    adm1_name VARCHAR(50),
    adm1_code VARCHAR(50),
    adm0_name VARCHAR(50),       
    adm0_code VARCHAR(50),       
    geom geometry(MultiPolygon, 4326),
	CONSTRAINT administrative_01_pkey PRIMARY KEY (id)
);

CREATE INDEX administrative_01_geom_idx ON administrative_01 USING GIST (geom);

------------Insert data-------------------
INSERT INTO public.administrative_01 (
    adm1_name,
    adm1_code,
    adm0_name,
    adm0_code,
    geom
)
SELECT
    adm1_en AS adm1_name,
    adm1_pcode AS adm1_code,
    adm0_en AS adm0_name,
    adm0_pcode AS adm0_code,
    ST_Transform(geom, 4326)::geometry(MULTIPOLYGON, 4326)
FROM
    ken_admbnda_adm1_iebc_20191031
WHERE
    geom IS NOT NULL;


ALTER TABLE administrative_01
ADD COLUMN adm0_id INT;

ALTER TABLE administrative_01
ADD CONSTRAINT fk_adm0_id
FOREIGN KEY (adm0_id) REFERENCES administrative_00(id);

UPDATE administrative_01 a1
SET adm0_id = a0.id
FROM administrative_00 a0
WHERE a1.adm0_name = a0.adm0_name AND a1.adm0_code = a0.adm0_code;

--------------------------------Data for district---------------------------------------------------

CREATE TABLE administrative_02 (
    id serial4 NOT NULL,
    adm2_name VARCHAR(50),
    adm2_code VARCHAR(50),
    adm0_name VARCHAR(50),       
    adm0_code VARCHAR(50),
    adm1_name VARCHAR(50),           
    adm1_code VARCHAR(50),        
    geom geometry(MultiPolygon, 4326),
    CONSTRAINT administrative_02_pkey PRIMARY KEY (id)
);

CREATE INDEX administrative_02_geom_idx ON administrative_02 USING GIST (geom);

------------Insert data-------------------

INSERT INTO administrative_02 (
    adm2_name,
    adm2_code,
    adm0_name,
    adm0_code,
    adm1_name,
    adm1_code,
    geom
)
SELECT
    adm2_en AS adm2_name,
    adm2_pcode AS adm2_code,
    adm0_en AS adm0_name,
    adm0_pcode AS adm0_code,
    adm1_en AS adm1_name,
    adm1_pcode AS adm1_code,
	ST_Transform(geom, 4326)::geometry(MULTIPOLYGON, 4326)
FROM ken_admbnda_adm2_iebc_20191031
WHERE geom IS NOT NULL;


ALTER TABLE administrative_02
ADD COLUMN adm0_id INT,
ADD COLUMN adm1_id INT;

ALTER TABLE administrative_02
ADD CONSTRAINT fk_adm0_id_02 FOREIGN KEY (adm0_id) REFERENCES administrative_00(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_adm1_id_02 FOREIGN KEY (adm1_id) REFERENCES administrative_01(id) ON DELETE CASCADE;


UPDATE administrative_02 a2
SET adm0_id = a0.id
FROM administrative_00 a0
WHERE a2.adm0_name = a0.adm0_name AND a2.adm0_code = a0.adm0_code;

UPDATE administrative_02 a2
SET adm1_id = a1.id
FROM administrative_01 a1
WHERE a2.adm1_name = a1.adm1_name AND a2.adm1_code = a1.adm1_code;