--Customer: name, address, fat_id(fk), port, activation_date, customer_type
CREATE TABLE layer_customer (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    port INT CHECK (port > 0),
    activation_date DATE,
    customer_type TEXT CHECK (customer_type IN ('Residential', 'Enterprise', 'SMB')) NOT NULL, 

	geom GEOMETRY(Point, 4326) NOT NULL,
	
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	fat_id INT REFERENCES layer_fat(id)
);

-- 1. Audit Table for customer

CREATE TABLE audit_layer_customer (
    id SERIAL PRIMARY KEY,
    layer_customer_id INT NOT NULL REFERENCES layer_customer(id) ON DELETE CASCADE,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_customer_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_layer_customer(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_customer()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_rec_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        audit_rec_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        audit_rec_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_customer (
        layer_customer_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        audit_rec_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_rec_id;

    INSERT INTO audit_layer_customer_geom (
        audit_id,
        geom
    ) VALUES (
        audit_rec_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_customer
AFTER INSERT OR UPDATE OR DELETE ON layer_customer
FOR EACH ROW
EXECUTE FUNCTION audit_layer_customer();

-- 5.Index

CREATE INDEX idx_layer_customer_geom ON layer_customer USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_customer_public_id ON layer_customer(public_id);
CREATE UNIQUE INDEX idx_layer_customer_custom_id ON layer_customer(custom_id);