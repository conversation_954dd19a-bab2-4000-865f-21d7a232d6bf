------------------------- FAT: name, capacity (integer), address ---------------------------------------------
CREATE TABLE layer_fat (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    capacity INT NOT NULL,
    address TEXT,

    geom GEOMETRY(Point, 4326) NOT NULL,
	
	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for fat

CREATE TABLE audit_layer_fat (
    id SERIAL PRIMARY KEY,
    layer_fat_id INT NOT NULL, 
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by <PERSON><PERSON>IN<PERSON>,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_fat_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_layer_fat(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_fat() 
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    new_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_fat (layer_fat_id, operation, operated_by, snapshot)
    VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        snapshot_data
    )
    RETURNING id INTO new_audit_id;

    INSERT INTO audit_layer_fat_geom (audit_id, geom)
    VALUES (
        new_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_fat
AFTER INSERT OR UPDATE OR DELETE ON layer_fat
FOR EACH ROW
EXECUTE FUNCTION audit_layer_fat();


-- 5.Index

CREATE INDEX idx_layer_fat_geom ON layer_fat USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_fat_public_id ON layer_fat(public_id);



---------------Insert data---------------------------

INSERT INTO layer_fat (
    public_id,
    custom_id,
    name,
    capacity,
    address,
    geom,
    status,
    created_by,
    created_on
) VALUES 
-- Record 1
(gen_random_uuid(), 'FAT-001', 'FAT - Alpha Zone', 16, '12 First Avenue, Alpha City',
 ST_GeomFromText('POINT(77.6001 12.9711)', 4326), 'InService', 1, NOW()),

-- Record 2
(gen_random_uuid(), 'FAT-002', 'FAT - Beta Sector', 24, '45 Beta Street, Beta Town',
 ST_GeomFromText('POINT(77.6102 12.9722)', 4326), 'Planned', 2, NOW()),

-- Record 3
(gen_random_uuid(), 'FAT-003', 'FAT - Gamma Node', 32, '78 Gamma Lane, Gamma Area',
 ST_GeomFromText('POINT(77.6203 12.9733)', 4326), 'InService', 3, NOW()),

-- Record 4
(gen_random_uuid(), 'FAT-004', 'FAT - Delta Unit', 16, '22 Delta Road, Delta Colony',
 ST_GeomFromText('POINT(77.6304 12.9744)', 4326), 'Inactive', 1, NOW()),

-- Record 5
(gen_random_uuid(), 'FAT-005', 'FAT - Epsilon Point', 48, '99 Epsilon Blvd, Epsilon District',
 ST_GeomFromText('POINT(77.6405 12.9755)', 4326), 'InService', 2, NOW());
