-- Splitter: name, port_ratio, parent_box_id, parent_box_type

CREATE TABLE splitter_specifications (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,         
    port_ratio TEXT NOT NULL,    -- Storing 1:8 means 1 input and 8 output ports
    description TEXT,             
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

CREATE TABLE splitter (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,

    specification_id INT REFERENCES splitter_specifications(id),

	parent_fat_id INT REFERENCES layer_fat(id),   -- FK to dynamic FAT
    parent_fdc_id INT REFERENCES layer_fdc(id),   -- FK to dynamic FDC

	parent_box_type TEXT CHECK (parent_box_type IN ('FAT', 'FDC')) NOT NULL,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for splitter

CREATE TABLE audit_splitter (
  id SERIAL PRIMARY KEY,
  splitter_id INT NOT NULL REFERENCES splitter(id) ON DELETE CASCADE,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_splitter_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_splitter(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_splitter()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_rec_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_splitter (
        splitter_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_rec_id;

    INSERT INTO audit_splitter_geom (audit_id, geom)
    VALUES (
        audit_rec_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_splitter
AFTER INSERT OR UPDATE OR DELETE ON splitter
FOR EACH ROW
EXECUTE FUNCTION audit_splitter();

-- 5.Index

CREATE INDEX idx_layer_splitter_geom ON splitter USING GIST (geom);
CREATE UNIQUE INDEX idx_splitter_public_id ON splitter(public_id);



-------------Insert data--------------------------

INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:4 Splitter', '1:4', 'Compact size splitter for wall mounting');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:8 Splitter', '1:8', 'Suitable for medium distribution');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:16 Splitter', '1:16', 'High-capacity splitter');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:32 Splitter', '1:32', 'Large capacity splitter for POP usage');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:2 Splitter', '1:2', 'Low-ratio splitter for indoor use');


INSERT INTO splitter (custom_id, name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by) 
VALUES ('SPLIT-CUST-001', 'Splitter-001', 1, 1, null, 'FAT', ST_SetSRID(ST_MakePoint(77.597457, 12.975473), 4326), 'Inactive', 1);
INSERT INTO splitter (custom_id, name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by) VALUES ('SPLIT-CUST-002', 'Splitter-002', 2, null, 1, 'FDC', ST_SetSRID(ST_MakePoint(77.597993, 12.973507), 4326), 'Planned', 1);
INSERT INTO splitter (custom_id, name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by) VALUES ('SPLIT-CUST-003', 'Splitter-003', 3, 2, null, 'FAT', ST_SetSRID(ST_MakePoint(77.591754, 12.968991), 4326), 'Inactive', 1);
INSERT INTO splitter (custom_id, name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by) VALUES ('SPLIT-CUST-004', 'Splitter-004', 4, null, 2, 'FDC', ST_SetSRID(ST_MakePoint(77.589611, 12.967078), 4326), 'Planned', 1);
INSERT INTO splitter (custom_id, name, specification_id, parent_fat_id, parent_fdc_id, parent_box_type, geom, status, created_by) VALUES ('SPLIT-CUST-005', 'Splitter-005', 5, 3, null, 'FAT', ST_SetSRID(ST_MakePoint(77.594064, 12.97349), 4326), 'InService', 1);







