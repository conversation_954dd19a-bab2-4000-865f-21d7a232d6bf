--POP: name, address, category
CREATE TABLE layer_pop (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    category TEXT CHECK (category IN ('Tier1', 'Tier2', 'Tier3')) NOT NULL,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for pop

CREATE TABLE audit_layer_pop (
  id SERIAL PRIMARY KEY,
  layer_pop_id INT NOT NULL REFERENCES layer_pop(id) ON DELETE CASCADE,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGIN<PERSON>,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_pop_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_layer_pop(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_pop()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_pop (
        layer_pop_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_id;

    INSERT INTO audit_layer_pop_geom (audit_id, geom)
    VALUES (
        audit_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_pop
AFTER INSERT OR UPDATE OR DELETE ON layer_pop
FOR EACH ROW
EXECUTE FUNCTION audit_layer_pop();

-- 5.Index

CREATE INDEX idx_layer_pop_geom ON layer_pop USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_pop_public_id ON layer_pop(public_id);
CREATE UNIQUE INDEX idx_layer_pop_custom_id ON layer_pop(custom_id);



----------Insert data-------------------------

INSERT INTO layer_pop (
    public_id,
    custom_id,
    name,
    address,
    category,
    geom,
    status,
    created_by,
    created_on
) VALUES
-- Record 1
(gen_random_uuid(), 'POP-001', 'POP - Central Hub', '10 Main Street, Central City',
 'Tier1', ST_GeomFromText('POINT(77.5801 12.9711)', 4326), 'InService', 1, NOW()),

-- Record 2
(gen_random_uuid(), 'POP-002', 'POP - East Wing', '55 East Road, Eastville',
 'Tier2', ST_GeomFromText('POINT(77.5902 12.9722)', 4326), 'InService', 2, NOW()),

-- Record 3
(gen_random_uuid(), 'POP-003', 'POP - West Node', '99 West Street, Westborough',
 'Tier3', ST_GeomFromText('POINT(77.6003 12.9733)', 4326), 'Planned', 3, NOW()),

-- Record 4
(gen_random_uuid(), 'POP-004', 'POP - North Unit', '44 North Blvd, Northfield',
 'Tier2', ST_GeomFromText('POINT(77.6104 12.9744)', 4326), 'Inactive', 1, NOW()),

-- Record 5
(gen_random_uuid(), 'POP-005', 'POP - South Access', '123 South Ave, Southend',
 'Tier1', ST_GeomFromText('POINT(77.6205 12.9755)', 4326), 'InService', 2, NOW());
