------------------------------ FDC: name, capacity, address ----------------------------------------
CREATE TABLE layer_fdc (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    capacity INT NOT NULL,
    address TEXT,

    geom GEOMETRY(Point, 4326) NOT NULL,

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT
);

-- 1. Audit Table for fdc

CREATE TABLE audit_layer_fdc (
  id SERIAL PRIMARY KEY,
  layer_fdc_id INT NOT NULL REFERENCES layer_fdc(id) ON DELETE CASCADE,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_layer_fdc_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_layer_fdc(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_layer_fdc()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_layer_fdc (
        layer_fdc_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        affected_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO affected_id;

    INSERT INTO audit_layer_fdc_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;



-- 4. Attach Trigger

CREATE TRIGGER trg_audit_layer_fdc
AFTER INSERT OR UPDATE OR DELETE ON layer_fdc
FOR EACH ROW
EXECUTE FUNCTION audit_layer_fdc();

-- 5.Index

CREATE INDEX idx_layer_fdc_geom ON layer_fdc USING GIST (geom);
CREATE UNIQUE INDEX idx_layer_fdc_public_id ON layer_fdc(public_id);


-------------Insert data--------------------------

INSERT INTO layer_fdc (custom_id, name, capacity, address, geom, status, created_by)
VALUES
('FDC-CUST-001', 'FDC-001', 96, '12 MG Road, Bangalore', ST_SetSRID(ST_MakePoint(77.5941, 12.9719), 4326), 'Planned', 1),
('FDC-CUST-002', 'FDC-002', 144, '25 Brigade Road, Bangalore', ST_SetSRID(ST_MakePoint(77.5955, 12.9724), 4326), 'InService', 1),
('FDC-CUST-003', 'FDC-003', 72, '101 Indiranagar, Bangalore', ST_SetSRID(ST_MakePoint(77.5963, 12.9708), 4326), 'Planned', 1),
('FDC-CUST-004', 'FDC-004', 120, '88 Koramangala, Bangalore', ST_SetSRID(ST_MakePoint(77.5977, 12.9697), 4326), 'Inactive', 1),
('FDC-CUST-005', 'FDC-005', 60, '9 Whitefield, Bangalore', ST_SetSRID(ST_MakePoint(77.5990, 12.9731), 4326), 'InService', 1);
