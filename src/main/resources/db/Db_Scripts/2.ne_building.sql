CREATE TABLE ne_building (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    address TEXT,
    home_passes INT,
    floors INT,
    towers INT,
    building_type TEXT NOT NULL CHECK (building_type IN ('SDU', 'MDU', 'CDU')),
    tenancy TEXT CHECK (tenancy IN ('Owned', 'Rented')) NOT NULL,
    category TEXT CHECK (category IN ('Residential', 'Commercial', 'MixedUse')) NOT NULL,

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

    adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

-- 1. Audit Table for building

CREATE TABLE audit_ne_building (
    id SERIAL PRIMARY KEY,
    ne_building_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_building_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_building(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_building()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_audit_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_building (
        ne_building_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    )
    RETURNING id INTO affected_audit_id;

    INSERT INTO audit_ne_building_geom (
        audit_id,
        geom
    ) VALUES (
        affected_audit_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_building
AFTER INSERT OR UPDATE OR DELETE ON ne_building
FOR EACH ROW
EXECUTE FUNCTION audit_ne_building();

-- Insert trigger (ne_building) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_building_bi_row_custom_id ON ne_building;
CREATE TRIGGER trg_ne_building_bi_row_custom_id
BEFORE INSERT ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_building) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_building_bu_geom_custom_id ON ne_building;
CREATE TRIGGER trg_ne_building_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_building) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_building_bi_row_adm_ids ON ne_building;
CREATE TRIGGER trg_ne_building_bi_row_adm_ids
BEFORE INSERT ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_building) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_building_bu_geom_adm_ids ON ne_building;
CREATE TRIGGER trg_ne_building_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_building
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_building_geom ON ne_building USING GIST (geom);
CREATE INDEX idx_ne_building_public_id ON ne_building(public_id);
CREATE INDEX idx_ne_building_custom_id ON ne_building(custom_id);
CREATE INDEX idx_ne_building_building_type ON ne_building(building_type);