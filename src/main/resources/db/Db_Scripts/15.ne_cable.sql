-----------------------------------Cable Specification------------------------------------------

CREATE TABLE cable_specification (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,           -- e.g., '12F', '24F', '48F'
    number_of_cores INTEGER NOT NULL,    -- e.g., 12, 24, 48
    description TEXT,                    -- Optional: for internal notes
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);

INSERT INTO cable_specification (name, number_of_cores, description)
VALUES
('12F', 12, 'Standard 12 core fiber'),
('24F', 24, 'Standard 24 core fiber'),
('48F', 48, 'High-capacity feeder cable'),
('96F', 96, 'Used for large distribution areas');

----------------------------------cable_types-----------------------------------------------------

CREATE TABLE lookup_cable_types (
	id serial4 NOT NULL,
	name text NOT NULL,
	description text NULL,
	is_active bool NULL DEFAULT true,
	created_on timestamp NULL DEFAULT now(),
	modified_on timestamp NULL,
	CONSTRAINT lookup_cable_types_name_key UNIQUE (name),
	CONSTRAINT lookup_cable_types_pkey PRIMARY KEY (id)
);

INSERT INTO lookup_cable_types
(id, "name", description, is_active, created_on, modified_on)
VALUES(1, 'Feeder', 'Feeder', true, '2025-06-02 15:48:28.005', NULL);
INSERT INTO lookup_cable_types
(id, "name", description, is_active, created_on, modified_on)
VALUES(2, 'Distribution', 'Distribution', true, '2025-06-02 15:48:53.851', NULL);
INSERT INTO lookup_cable_types
(id, "name", description, is_active, created_on, modified_on)
VALUES(3, 'Drop', 'Drop', true, '2025-06-02 15:49:06.656', NULL);


----------------------------------ne_cable-----------------------------------------------------

CREATE TABLE ne_cable (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),           -- External identifier
    custom_id TEXT UNIQUE,                                       -- Optional human-readable ID
    name TEXT NOT NULL,
    mounting_type TEXT CHECK (mounting_type IN ('Underground', 'Overhead')) NOT NULL,
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
	cable_type_id INT REFERENCES lookup_cable_types(id),
    specification_id INT REFERENCES cable_specification(id),
    measured_length_m NUMERIC(10,2),
    gis_length_m NUMERIC(10,2),
    installation_date TIMESTAMPTZ,
    origin_node_id INT,
    origin_node_type TEXT,
    destination_node_id INT,
    destination_node_type TEXT,
    	
    remarks TEXT,
	geom GEOMETRY(LineString, 4326) NOT NULL,

	created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

	--It can be Pole, Duct
	parent_ne_id INT,
	parent_ne_type TEXT,

	adm1_id INT REFERENCES administrative_01(id),
	adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

------------------------------------gis_length-------------------------------------------------------------------

CREATE OR REPLACE FUNCTION update_ne_cable_gis_length()
RETURNS TRIGGER AS $$
BEGIN
  NEW.gis_length_m := ST_Length(NEW.geom::geography);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Insert trigger (ne_cable) : On insert row to update gis length
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_gis_length ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_gis_length
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_gis_length();

-- Update trigger (ne_cable) : On update geom to update gis length
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_gis_length ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_gis_length
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_ne_cable_gis_length();

--======================= TRIGGER :
-- Create custom_id automatically

-- Insert trigger (ne_cable) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_custom_id ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_custom_id
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

-- Update trigger (ne_cable) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_custom_id ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

--======================= TRIGGER :
-- Create adm1_id, adm2_id automatically

-- Insert trigger (ne_cable) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_cable_bi_row_adm_ids ON ne_cable;
CREATE TRIGGER trg_ne_cable_bi_row_adm_ids
BEFORE INSERT ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();

-- Update trigger (ne_cable) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_cable_bu_geom_adm_ids ON ne_cable;
CREATE TRIGGER trg_ne_cable_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();

-- Geometry Index
CREATE INDEX idx_ne_cable_geom ON ne_cable USING GIST (geom);

-- Lookup and Join Indexes
CREATE INDEX idx_ne_cable_specification_id ON ne_cable (specification_id);
CREATE INDEX idx_ne_cable_custom_id ON ne_cable (custom_id);
CREATE INDEX idx_ne_cable_public_id ON ne_cable (public_id);
CREATE INDEX idx_ne_cable_cable_type_id ON ne_cable (cable_type_id);

-- audit
CREATE TABLE audit_ne_cable (
  id SERIAL PRIMARY KEY,
  ne_cable_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

CREATE TABLE audit_ne_cable_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_cable(id) ON DELETE CASCADE,
  geom GEOMETRY(LineString, 4326)
);

-- Trigger Function
CREATE OR REPLACE FUNCTION trg_audit_ne_cable()
RETURNS TRIGGER AS $$
DECLARE
  snapshot_data JSONB;
  inserted_id INT;
  user_id BIGINT;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    snapshot_data := to_jsonb(OLD) - 'geom';
	user_id := COALESCE(OLD.modified_by, OLD.created_by);
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      OLD.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, OLD.geom);

    RETURN OLD;

  ELSIF (TG_OP = 'UPDATE') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := COALESCE(NEW.modified_by, NEW.created_by);
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;

  ELSIF (TG_OP = 'INSERT') THEN
    snapshot_data := to_jsonb(NEW) - 'geom';
	user_id := NEW.created_by;
    INSERT INTO audit_ne_cable (
      ne_cable_id, operation, operated_by, snapshot
    ) VALUES (
      NEW.id, TG_OP, user_id, snapshot_data
    )
    RETURNING id INTO inserted_id;

    INSERT INTO audit_ne_cable_geom (audit_id, geom)
    VALUES (inserted_id, NEW.geom);

    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Attach Trigger
DROP TRIGGER IF EXISTS trg_audit_ne_cable ON ne_cable;
CREATE TRIGGER trg_audit_ne_cable
AFTER INSERT OR UPDATE OR DELETE ON ne_cable
FOR EACH ROW
EXECUTE FUNCTION trg_audit_ne_cable();

