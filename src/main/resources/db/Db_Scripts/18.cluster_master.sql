-----------------------------------Cluster Master------------------------------------------
CREATE TABLE cluster_master (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),

    name TEXT NOT NULL UNIQUE,
    description TEXT,

    status TEXT,
    geom GEOMETRY(Polygon, 4326) NOT NULL CHECK (ST_IsValid(geom)),

    is_active BOOLEAN DEFAULT TRUE,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_by <PERSON><PERSON><PERSON><PERSON>,
    modified_on TIMESTAMPTZ,

    adm1_id INT,
    adm2_id INT,
	CONSTRAINT fk_cluster_master_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_cluster_master_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- Trigger
-- Insert trigger (cluster_master) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_cluster_master_bi_row_adm_ids ON cluster_master;
CREATE TRIGGER trg_cluster_master_bi_row_adm_ids
BEFORE INSERT ON cluster_master
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (cluster_master) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_cluster_master_bu_geom_adm_ids ON cluster_master;
CREATE TRIGGER trg_cluster_master_bu_geom_adm_ids
BEFORE UPDATE OF geom ON cluster_master
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Index
CREATE INDEX cluster_master_geom_idx ON cluster_master USING GIST (geom);
CREATE INDEX idx_cluster_master_public_id_idx ON cluster_master(public_id);
