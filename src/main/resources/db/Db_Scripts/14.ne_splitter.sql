-- Splitter: name, port_ratio, parent_box_id, parent_box_type

CREATE TABLE splitter_specification (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    port_ratio TEXT NOT NULL,    -- Storing 1:8 means 1 input and 8 output ports
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT NOW(),
    modified_on TIMESTAMP
);


INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:4 Splitter', '1:4', 'Compact size splitter for wall mounting');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:8 Splitter', '1:8', 'Suitable for medium distribution');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:16 Splitter', '1:16', 'High-capacity splitter');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:32 Splitter', '1:32', 'Large capacity splitter for POP usage');
INSERT INTO splitter_specification (name, port_ratio, description) VALUES ('1:2 Splitter', '1:2', 'Low-ratio splitter for indoor use');

CREATE TABLE ne_splitter (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,

    specification_id INT REFERENCES splitter_specification(id),

	status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
	
	--It can be FAT, FDC
	parent_ne_id INT,
	parent_ne_type TEXT,
	
	adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

-- 1. Audit Table for splitter

CREATE TABLE audit_ne_splitter (
  id SERIAL PRIMARY KEY,
  splitter_id INT NOT NULL,
  operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
  operated_by BIGINT,
  operated_at TIMESTAMPTZ DEFAULT NOW(),
  snapshot JSONB
);

-- 2. Geometry Audit Table

CREATE TABLE audit_ne_splitter_geom (
  id SERIAL PRIMARY KEY,
  audit_id INT REFERENCES audit_ne_splitter(id) ON DELETE CASCADE,
  geom GEOMETRY(Point, 4326)
);

-- 3. Trigger Function

CREATE OR REPLACE FUNCTION audit_ne_splitter()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    audit_rec_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_splitter (
        splitter_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO audit_rec_id;

    INSERT INTO audit_ne_splitter_geom (audit_id, geom)
    VALUES (
        audit_rec_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. Attach Trigger

CREATE TRIGGER trg_audit_ne_splitter
AFTER INSERT OR UPDATE OR DELETE ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION audit_ne_splitter();

-- Insert trigger (ne_splitter) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_splitter_bi_row_custom_id ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bi_row_custom_id
BEFORE INSERT ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Update trigger (ne_splitter) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_splitter_bu_geom_custom_id ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Insert trigger (ne_splitter) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_splitter_bi_row_adm_ids ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bi_row_adm_ids
BEFORE INSERT ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (ne_splitter) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_splitter_bu_geom_adm_ids ON ne_splitter;
CREATE TRIGGER trg_ne_splitter_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_splitter
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- 5.Index

CREATE INDEX idx_ne_splitter_geom ON ne_splitter USING GIST (geom);
CREATE INDEX idx_ne_splitter_public_id ON ne_splitter(public_id);
CREATE INDEX idx_ne_splitter_custom_id ON ne_splitter(custom_id);
