CREATE TABLE ne_duct (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,
    name TEXT,
    material TEXT,
    diameter_mm NUMERIC CHECK (diameter_mm > 0),
    length_m NUMERIC CHECK (length_m > 0),
    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive','Active', 'Abandoned')),
	
    install_date DATE CHECK (install_date <= CURRENT_DATE),
    owner TEXT,
    geom GEOMETRY(LineString, 4326),
    start_node_id BIGINT,
    end_node_id BIGINT,
    num_subducts INTEGER CHECK (num_subducts >= 0),
    used_subducts INTEGER CHECK (used_subducts >= 0),
    available_subducts INTEGER GENERATED ALWAYS AS (num_subducts - used_subducts) STORED,
    network_type TEXT CHECK (network_type IN ('Backbone', 'Access', 'Distribution')),
    remarks TEXT,
	
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

	trench_id INT REFERENCES ne_trench(id),
	adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

CREATE TABLE audit_ne_duct (
    id SERIAL PRIMARY KEY,
    duct_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by TEXT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

CREATE TABLE audit_ne_duct_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_duct(id) ON DELETE CASCADE,
    geom GEOMETRY(LineString, 4326)
);


CREATE OR REPLACE FUNCTION audit_ne_duct()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id TEXT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_duct (duct_id, operation, operated_by, operated_at, snapshot)
    VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
    RETURNING id INTO affected_id;

    INSERT INTO audit_ne_duct_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

--======================= TRIGGER :
CREATE TRIGGER trg_audit_ne_duct
AFTER INSERT OR UPDATE OR DELETE ON ne_duct
FOR EACH ROW
EXECUTE FUNCTION audit_ne_duct();

-- Create custom_id automatically
-- Insert trigger (ne_duct) : On insert row to update custom id
DROP TRIGGER IF EXISTS trg_ne_duct_bi_row_custom_id ON ne_duct;
CREATE TRIGGER trg_ne_duct_bi_row_custom_id
BEFORE INSERT ON ne_duct
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

-- Update trigger (ne_duct) : On update row to update custom id
DROP TRIGGER IF EXISTS trg_ne_duct_bu_geom_custom_id ON ne_duct;
CREATE TRIGGER trg_ne_duct_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_duct
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_linestring();

-- Create adm1_id, adm2_id automatically
-- Insert trigger (ne_duct) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_duct_bi_row_adm_ids ON ne_duct;
CREATE TRIGGER trg_ne_duct_bi_row_adm_ids
BEFORE INSERT ON ne_duct
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();

-- Update trigger (ne_duct) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_ne_duct_bu_geom_adm_ids ON ne_duct;
CREATE TRIGGER trg_ne_duct_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_duct
FOR EACH ROW
EXECUTE FUNCTION update_adm_ids_linestring();


CREATE INDEX idx_ne_duct_geom ON ne_duct USING GIST (geom);
CREATE INDEX idx_ne_duct_public_id ON ne_duct(public_id);
CREATE INDEX idx_ne_duct_custom_id ON ne_duct(custom_id);




