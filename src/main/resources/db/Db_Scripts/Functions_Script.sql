-- ::::::::::::::::::: ::::::::::::::::::: ::::::::::::::::::: Function 1
CREATE OR REPLACE FUNCTION get_geometries_within_polygon(geojson_input TEXT)
RETURNS JSONB
AS $$
DECLARE
    cable_result JSONB;
    fat_result JSONB;
    pop_result JSONB;
    fdp_result JSONB;
    fdc_result JSONB;
    customer_result JSONB;
    building_result JSONB;
    splitter_result JSONB;
BEGIN
	-- Get cables within the polygon with specification and type details
	SELECT jsonb_agg(
	           jsonb_build_object(
	               'name', c.name,
	               'cableTypeId', ct.id,
	               'cableTypeName', ct.name,
	               'cableTypeDescription', ct.description,
	               'cableTypeIsActive', ct.is_active,
	               'mountingType', c.mounting_type,
	               'status', c.status,
	               'specificationId', c.specification_id,
	               'specificationName', cs.name,
	               'numberOfCores', cs.number_of_cores,
	               'specificationDescription', cs.description,
	               'specificationIsActive', cs.is_active,
	               'measuredLengthM', c.measured_length_m,
	               'gisLengthM', c.gis_length_m,
	               'installationDate', c.installation_date,
	               'originNodeId', c.origin_node_id,
	               'originNodeType', c.origin_node_type,
	               'destinationNodeId', c.destination_node_id,
	               'destinationNodeType', c.destination_node_type,
	               'remarks', c.remarks,
	               'geom', ST_AsGeoJSON(c.geom)
	           )
	) INTO cable_result
	FROM ne_cable c
	LEFT JOIN cable_specification cs ON c.specification_id = cs.id
	LEFT JOIN lookup_cable_types ct ON c.cable_type_id = ct.id
	WHERE ST_Within(c.geom, ST_GeomFromGeoJSON(geojson_input));
    -- Note : If Both points are in polygon then it will give linestring for function (ST_Within)
    -- If need to get linestring only with one point in polygon then need to use function (ST_Intersects)

    -- Get FATs within the polygon
    SELECT jsonb_agg(
               jsonb_build_object(
                   'name', name,
                   'capacity', capacity,
                   'address', address,
                   'geom', ST_AsGeoJSON(geom),
                   'status', status,
                   'createdBy', created_by,
                   'createdOn', created_on
               )
    ) INTO fat_result
    FROM ne_fat
    WHERE ST_Within(geom, ST_GeomFromGeoJSON(geojson_input));

    -- Get POPs within the polygon
    SELECT jsonb_agg(
               jsonb_build_object(
                   'name', name,
                   'address', address,
                   'category', category,
                   'geom', ST_AsGeoJSON(geom),
                   'status', status,
                   'createdBy', created_by,
                   'createdOn', created_on
               )
    ) INTO pop_result
    FROM ne_pop
    WHERE ST_Within(geom, ST_GeomFromGeoJSON(geojson_input));

	-- Get FDPs within the polygon with type details
	SELECT jsonb_agg(
	           jsonb_build_object(
	               'name', f.name,
	               'port', f.port,
	               'typeId', f.type_id,
	               'typeName', l.name,
	               'typeDescription', l.description,
	               'typeIsActive', l.is_active,
	               'geom', ST_AsGeoJSON(f.geom),
	               'status', f.status,
	               'createdBy', f.created_by,
	               'createdOn', f.created_on
	           )
	) INTO fdp_result
	FROM ne_fdp f
	LEFT JOIN lookup_fdp_types l ON f.type_id = l.id
	WHERE ST_Within(f.geom, ST_GeomFromGeoJSON(geojson_input));


    -- Get FDCs within the polygon
    SELECT jsonb_agg(
               jsonb_build_object(
                   'name', name,
                   'capacity', capacity,
                   'address', address,
                   'geom', ST_AsGeoJSON(geom),
                   'status', status,
                   'createdBy', created_by,
                   'createdOn', created_on
               )
    ) INTO fdc_result
    FROM ne_fdc
    WHERE ST_Within(geom, ST_GeomFromGeoJSON(geojson_input));

    -- Get customers within the polygon
    SELECT jsonb_agg(
               jsonb_build_object(
                   'name', name,
                   'address', address,
                   'port', port,
                   'activationDate', activation_date,
                   'customerType', customer_type,
                   'geom', ST_AsGeoJSON(geom),
                   'status', status,
                   'createdBy', created_by,
                   'createdOn', created_on
               )
    ) INTO customer_result
    FROM ne_customer
    WHERE ST_Within(geom, ST_GeomFromGeoJSON(geojson_input));

    -- Get buildings within the polygon
    SELECT jsonb_agg(
               jsonb_build_object(
                   'name', name,
                   'address', address,
                   'homePasses', home_passes,
                   'floors', floors,
                   'towers', towers,
                   'surveyStatus', survey_status,
                   'tenancy', tenancy,
                   'category', category,
                   'geom', ST_AsGeoJSON(geom),
                   'status', status,
                   'createdBy', created_by,
                   'createdOn', created_on
               )
    ) INTO building_result
    FROM ne_building
    WHERE ST_Within(geom, ST_GeomFromGeoJSON(geojson_input));

	-- Get splitters within the polygon with specification details
	SELECT jsonb_agg(
	           jsonb_build_object(
	               'name', s.name,
	               'specificationId', s.specification_id,
	               'specificationName', sp.name,
	               'portRatio', sp.port_ratio,
	               'specificationDescription', sp.description,
	               'specificationIsActive', sp.is_active,
	               'parentFatId', s.parent_fat_id,
	               'parentFdcId', s.parent_fdc_id,
	               'parentBoxType', s.parent_box_type,
	               'geom', ST_AsGeoJSON(s.geom),
	               'status', s.status,
	               'createdBy', s.created_by,
	               'createdOn', s.created_on
	           )
	) INTO splitter_result
	FROM ne_splitter s
	LEFT JOIN splitter_specification sp ON s.specification_id = sp.id
	WHERE ST_Within(s.geom, ST_GeomFromGeoJSON(geojson_input));


    -- Return combined result
    RETURN jsonb_build_object(
        'cable', cable_result,
        'fat', fat_result,
        'pop', pop_result,
        'fdp', fdp_result,
        'fdc', fdc_result,
        'customer', customer_result,
        'building', building_result,
        'splitter', splitter_result
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- ::::::::::::::::::: ::::::::::::::::::: ::::::::::::::::::: Function 2

CREATE OR REPLACE FUNCTION find_parent_data_near_child(
    child_layer_id INT,
    geojson_input TEXT,
    search_radius_meters FLOAT DEFAULT 3
)
RETURNS JSONB
AS $$
DECLARE
    result JSONB := '{}';
    temp_result JSONB;
    table_name_var TEXT;
    disp_name TEXT;
    sql TEXT;
    buffer_geom GEOMETRY;
    child_geom_input GEOMETRY(Geometry, 4326);
BEGIN
    -- Convert GeoJSON to geometry
    child_geom_input := ST_SetSRID(ST_GeomFromGeoJSON(geojson_input), 4326);

    -- Create buffer using geography
    buffer_geom := ST_Buffer(child_geom_input::geography, search_radius_meters)::geometry;

    -- Loop through parent layers
    FOR table_name_var, disp_name IN
        SELECT lm.table_name, lm.display_name
        FROM layer_mapping map
        JOIN layer_master lm ON map.parent_layer_id = lm.id
        WHERE map.child_layer_id = find_parent_data_near_child.child_layer_id
    LOOP
        -- Dynamic SQL using table_name_var and injecting layer_name and display_name
        sql := format($f$
            SELECT jsonb_agg(jsonb_build_object(
                'layer_name', %L,
                'public_id', public_id,
                'name', name,
                'status', status,
                'geomType', ST_GeometryType(geom),
                'geom', ST_AsGeoJSON(geom)
            ))
            FROM %I
            WHERE ST_Within(geom, ST_GeomFromText(%L, 4326))
        $f$, disp_name, table_name_var, ST_AsText(buffer_geom));

        EXECUTE sql INTO temp_result;

        IF temp_result IS NOT NULL THEN
            result := jsonb_set(result, ARRAY[disp_name], temp_result, true);
        END IF;
    END LOOP;

    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;

-- ::::::::::::::::::: ::::::::::::::::::: ::::::::::::::::::: Function 3

CREATE OR REPLACE FUNCTION find_near_by_geom(
    geojson_input TEXT,
    radius_length_meters FLOAT DEFAULT 3,
    table_names TEXT[] DEFAULT NULL
)
RETURNS JSONB
AS $$
DECLARE
    result JSONB := '{}';
    temp_result JSONB;
    table_name_var TEXT;
    disp_name TEXT;
    sql TEXT;
    buffer_geom GEOMETRY;
    child_geom_input GEOMETRY(Geometry, 4326);
BEGIN
    -- Return empty JSON if no table names provided
    IF table_names IS NULL OR cardinality(table_names) = 0 THEN
        RETURN result;
    END IF;

    -- Convert GeoJSON to geometry
    child_geom_input := ST_SetSRID(ST_GeomFromGeoJSON(geojson_input), 4326);

    -- Create buffer using geography
    buffer_geom := ST_Buffer(child_geom_input::geography, radius_length_meters)::geometry;

    -- Loop through each table
    FOREACH table_name_var IN ARRAY table_names
    LOOP
        -- Get display_name from layer_master
        SELECT lm.display_name
        INTO disp_name
        FROM layer_master lm
        WHERE lm.table_name = table_name_var
        LIMIT 1;

        -- Fallback if display_name is NULL
        IF disp_name IS NULL THEN
            disp_name := table_name_var;
        END IF;

        -- Build dynamic SQL
        sql := format($f$
            SELECT jsonb_agg(jsonb_build_object(
                'layer_name', %L,
                'public_id', public_id,
                'name', name,
                'status', status,
                'geomType', ST_GeometryType(geom),
                'geom', ST_AsGeoJSON(geom)
            ))
            FROM %I
            WHERE ST_Within(geom, ST_GeomFromText(%L, 4326))
        $f$, disp_name, table_name_var, ST_AsText(buffer_geom));

        EXECUTE sql INTO temp_result;

        IF temp_result IS NOT NULL THEN
            result := jsonb_set(result, ARRAY[disp_name], temp_result, true);
        END IF;
    END LOOP;

    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;
