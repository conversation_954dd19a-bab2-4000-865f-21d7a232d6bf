-- Used to enable postgis extension.
CREATE EXTENSION postgis;

-- Execute for gen_random_uuid();
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Generic function to generate field(custom_id) for all table's geom(Points)
CREATE OR REPLACE FUNCTION update_custom_id_generic()
RETURNS TRIGGER AS $$
DECLARE
    feature_point geometry;
    adm1 TEXT;
    adm2 TEXT;
    layer_code TEXT;
    last_seq INT;
    next_seq TEXT;
    layer_table TEXT := TG_TABLE_NAME;
    sql TEXT;
BEGIN
    -- 1. Extract geometry point (assumes Point geometry)
    feature_point := NEW.geom;

    -- 2. Lookup adm1_code and adm2_code based on the location
    SELECT a.adm1_code, a.adm2_code
    INTO adm1, adm2
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, feature_point)
    LIMIT 1;

    -- 3. Lookup layer code from layer_master
    SELECT UPPER(code)
    INTO layer_code
    FROM layer_master
    WHERE table_name = layer_table
    LIMIT 1;

    -- 4. Build and execute dynamic SQL to get the highest sequence for adm2
    sql := format(
        'SELECT COALESCE(MAX(SPLIT_PART(custom_id, ''-'', 4)::INT), 0) FROM %I WHERE custom_id LIKE ''%%%s%%''',
        layer_table, adm2
    );
    EXECUTE sql INTO last_seq;

    -- 5. Generate next sequence
    next_seq := LPAD((last_seq + 1)::TEXT, 3, '0');

    -- 6. Construct the custom_id
    NEW.custom_id := layer_code || '-' || adm1 || '-' || adm2 || '-' || next_seq;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

----------------------------------------------------
-- Generic function to generate fields(adm1_id, adm2_id) for all table's geom(Points)

CREATE OR REPLACE FUNCTION update_administrative_ids_generic()
RETURNS TRIGGER AS $$
DECLARE
    pointVar geometry;
    adm1_id INTEGER;
    adm2_id INTEGER;
BEGIN
    -- Get geometry of the new row
    pointVar := NEW.geom;

    -- Find administrative areas containing the point
    SELECT a.adm1_id, a.id
    INTO adm1_id, adm2_id
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, pointVar)
    LIMIT 1;

    -- Set the administrative IDs in the new row
    NEW.adm1_id := adm1_id;
    NEW.adm2_id := adm2_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

--======================= Function: custom_id
-- Get First point from LineString and locate point into administrative_02

CREATE OR REPLACE FUNCTION update_custom_id_linestring()
RETURNS TRIGGER AS $$
DECLARE
    start_point geometry;
    adm1 TEXT;
    adm2 TEXT;
    layer_code TEXT;
    last_seq INT;
    next_seq TEXT;
    table_name_var TEXT := TG_TABLE_NAME;  -- Get dynamic table name from trigger context
BEGIN
    -- 1. Get the start point of the LineString
    start_point := ST_StartPoint(NEW.geom);

    -- 2. Get adm1_code and adm2_code from administrative_02 where the point is contained
    SELECT a.adm1_code, a.adm2_code
    INTO adm1, adm2
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    -- 3. Get the layer code from layer_master using dynamic table name
    SELECT UPPER(lm.code)
    INTO layer_code
    FROM layer_master lm
    WHERE lm.table_name = table_name_var
    LIMIT 1;

    -- 4. Get the last sequence used for this adm2 in the current table (dynamic query)
    EXECUTE format('SELECT COALESCE(MAX(SPLIT_PART(custom_id, ''-'', 4)::INT), 0) FROM %I WHERE custom_id LIKE $1', table_name_var)
    INTO last_seq
    USING '%' || adm2 || '%';

    -- 5. Generate the next sequence number
    next_seq := LPAD((last_seq + 1)::TEXT, 3, '0');

    -- 6. Set the final custom_id
    NEW.custom_id := layer_code || '-' || adm1 || '-' || adm2 || '-' || next_seq;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

--======================= Function: adm1_id, adm2_id
-- Get administrative_01(id) and administrative_02(id) based on first point of LineString
CREATE OR REPLACE FUNCTION update_adm_ids_linestring()
RETURNS TRIGGER AS $$
DECLARE
    start_point geometry;
    adm1_id INTEGER;
    adm2_id INTEGER;
    table_name_var TEXT := TG_TABLE_NAME;
BEGIN
    -- 1. Get the start point of the LineString
    start_point := ST_StartPoint(NEW.geom);

    -- 2. Get adm1_id and adm2_id (id from administrative_02)
    SELECT a.adm1_id, a.id
    INTO adm1_id, adm2_id
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    -- 3. Dynamically set the values to NEW.* (works the same regardless of table)
    NEW.adm1_id := adm1_id;
    NEW.adm2_id := adm2_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
