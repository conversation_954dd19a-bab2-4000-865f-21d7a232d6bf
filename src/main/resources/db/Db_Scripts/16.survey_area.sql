-- Lookup table for survey area
CREATE TABLE lookup_survey_status (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
	description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ
);

INSERT INTO lookup_survey_status (name, description, is_active, created_on, modified_on) VALUES ('Planned', 'NA', true, now(), NULL);
INSERT INTO lookup_survey_status (name, description, is_active, created_on, modified_on) VALUES ('Assigned', 'NA', true, now(), NULL);
INSERT INTO lookup_survey_status (name, description, is_active, created_on, modified_on) VALUES ('Completed', 'NA', true, now(), NULL);
INSERT INTO lookup_survey_status (name, description, is_active, created_on, modified_on) VALUES ('Reassign', 'NA', true, now(), NULL);
INSERT INTO lookup_survey_status (name, description, is_active, created_on, modified_on) VALUES ('Inprogress', 'NA', true, now(), NULL);

-----------------------------------Survey Area------------------------------------------
CREATE TABLE survey_area (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),

    name TEXT NOT NULL UNIQUE,
    description TEXT,

    survey_status_id INT REFERENCES lookup_survey_status(id),
    geom GEOMETRY(Polygon, 4326) NOT NULL CHECK (ST_IsValid(geom)),

    is_active BOOLEAN DEFAULT TRUE,

    survey_start_date TIMESTAMPTZ,
    survey_end_date TIMESTAMPTZ,
    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_by BIGINT,
    modified_on TIMESTAMPTZ,

    adm1_id INT,
    adm2_id INT,
	mvno_id INT NOT NULL,
	CONSTRAINT fk_survey_area_adm1 FOREIGN KEY (adm1_id) REFERENCES public.administrative_01(id),
	CONSTRAINT fk_survey_area_adm2 FOREIGN KEY (adm2_id) REFERENCES public.administrative_02(id)
);

-- Trigger
-- Insert trigger (survey_area) : On insert row to update adm ids
DROP TRIGGER IF EXISTS trg_survey_area_bi_row_adm_ids ON survey_area;
CREATE TRIGGER trg_survey_area_bi_row_adm_ids
BEFORE INSERT ON survey_area
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Update trigger (survey_area) : On update row to update adm ids
DROP TRIGGER IF EXISTS trg_survey_area_bu_geom_adm_ids ON survey_area;
CREATE TRIGGER trg_survey_area_bu_geom_adm_ids
BEFORE UPDATE OF geom ON survey_area
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Index
CREATE INDEX survey_area_geom_idx ON survey_area USING GIST (geom);
CREATE INDEX idx_survey_area_public_id_idx ON survey_area(public_id);
