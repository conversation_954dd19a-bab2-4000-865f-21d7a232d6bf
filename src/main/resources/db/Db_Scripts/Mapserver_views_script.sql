CREATE OR REPLACE VIEW vw_map_administrative_00 AS
SELECT 
    id,
    geom
FROM 
    administrative_00;


CREATE OR REPLACE VIEW vw_map_administrative_01 AS
SELECT 
    id,
	adm1_name,
    geom
FROM 
    administrative_01;


CREATE OR REPLACE VIEW vw_map_administrative_02 AS
SELECT 
    id,
    geom
FROM 
    administrative_02;

	
CREATE OR REPLACE VIEW vw_map_ne_building AS
SELECT 
    bld.id,
    lm.icon AS icon_path,
    bld.geom
FROM 
    ne_building bld
JOIN 
    layer_master lm ON lm.table_name = 'ne_building';


CREATE OR REPLACE VIEW public.vw_map_ne_cable AS
SELECT
    cbl.id,
    lct.name AS cable_type,
    lm.icon AS icon_path,
    cbl.geom
FROM
    ne_cable cbl
JOIN
    lookup_cable_types lct ON cbl.cable_type_id = lct.id
JOIN
    layer_master lm ON lm.table_name = 'ne_cable'::text;


CREATE OR REPLACE VIEW vw_map_ne_customer AS
SELECT 
    cst.id,
    lm.icon AS icon_path,
    cst.geom
FROM 
    ne_customer cst
JOIN 
    layer_master lm ON lm.table_name = 'ne_customer';


CREATE OR REPLACE VIEW vw_map_ne_fat AS
SELECT 
    fat.id,
    lm.icon AS icon_path,
    fat.geom
FROM 
    ne_fat fat
JOIN 
    layer_master lm ON lm.table_name = 'ne_fat';


CREATE OR REPLACE VIEW vw_map_ne_fdc AS
SELECT 
    fdc.id,
    lm.icon AS icon_path,
    fdc.geom
FROM 
    ne_fdc fdc
JOIN 
    layer_master lm ON lm.table_name = 'ne_fdc';
	
	
CREATE OR REPLACE VIEW vw_map_ne_fdp AS
SELECT 
    fdp.id,
    lm.icon AS icon_path,
    fdp.geom
FROM 
    ne_fdp fdp
JOIN 
    layer_master lm ON lm.table_name = 'ne_fdp';


CREATE OR REPLACE VIEW vw_map_ne_pop AS
SELECT 
    pop.id,
    lm.icon AS icon_path,
    pop.geom
FROM 
    ne_pop pop
JOIN 
    layer_master lm ON lm.table_name = 'ne_pop';
	
	
CREATE OR REPLACE VIEW vw_map_ne_splitter AS
SELECT 
    splt.id,
    lm.icon AS icon_path,
    splt.geom
FROM 
    ne_splitter splt
JOIN 
    layer_master lm ON lm.table_name = 'ne_splitter';

CREATE OR REPLACE VIEW vw_map_survey_area AS
SELECT
    sa.id,
    lm.icon AS icon_path,
    sa.geom
FROM
    survey_area sa
JOIN
    layer_master lm ON lm.table_name = 'survey_area';
