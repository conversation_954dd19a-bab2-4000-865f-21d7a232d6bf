CREATE TABLE ne_manhole (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    manhole_size TEXT NOT NULL,  -- e.g., '600mm', '1000mm'
    depth_cm INT,
    cover_type TEXT NOT NULL,  -- e.g., 'Cast Iron', 'Composite', 'Concrete'

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

    adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);

CREATE TABLE audit_ne_manhole (
    id SERIAL PRIMARY KEY,
    ne_manhole_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);

CREATE TABLE audit_ne_manhole_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_manhole(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


CREATE OR REPLACE FUNCTION audit_ne_manhole()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_manhole (
        ne_manhole_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        affected_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO affected_id;

    INSERT INTO audit_ne_manhole_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;


-- Main audit trigger
CREATE TRIGGER trg_audit_ne_manhole
AFTER INSERT OR UPDATE OR DELETE ON ne_manhole
FOR EACH ROW
EXECUTE FUNCTION audit_ne_manhole();

-- Custom ID update on insert
DROP TRIGGER IF EXISTS trg_ne_manhole_bi_row_custom_id ON ne_manhole;
CREATE TRIGGER trg_ne_manhole_bi_row_custom_id
BEFORE INSERT ON ne_manhole
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Custom ID update on geom update
DROP TRIGGER IF EXISTS trg_ne_manhole_bu_geom_custom_id ON ne_manhole;
CREATE TRIGGER trg_ne_manhole_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_manhole
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Administrative IDs update on insert
DROP TRIGGER IF EXISTS trg_ne_manhole_bi_row_adm_ids ON ne_manhole;
CREATE TRIGGER trg_ne_manhole_bi_row_adm_ids
BEFORE INSERT ON ne_manhole
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Administrative IDs update on geom update
DROP TRIGGER IF EXISTS trg_ne_manhole_bu_geom_adm_ids ON ne_manhole;
CREATE TRIGGER trg_ne_manhole_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_manhole
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();


CREATE INDEX idx_ne_manhole_geom ON ne_manhole USING GIST (geom);
CREATE INDEX idx_ne_manhole_public_id ON ne_manhole(public_id);
CREATE INDEX idx_ne_manhole_custom_id ON ne_manhole(custom_id);






