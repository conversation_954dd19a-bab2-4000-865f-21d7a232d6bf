CREATE TABLE ne_joint_closure (
    id SERIAL PRIMARY KEY,
    public_id UUID NOT NULL DEFAULT gen_random_uuid(),
    custom_id TEXT UNIQUE,

    name TEXT NOT NULL,
    joint_type TEXT NOT NULL CHECK (joint_type IN ('Splice', 'Splitter', 'Transition')),
    capacity INT,
    mounted_in TEXT NOT NULL CHECK (mounted_in IN ('Handhole', 'Manhole', 'Pole', 'Cabinet')),

    status TEXT CHECK (status IN ('Planned', 'InService', 'Inactive')) DEFAULT 'Planned',
    geom GEOMETRY(Point, 4326) NOT NULL,

    created_by BIGINT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,

	--It can be Handhold, Manhole, Pole
	parent_ne_id INT,
	parent_ne_type TEXT,

    adm1_id INT REFERENCES administrative_01(id),
    adm2_id INT REFERENCES administrative_02(id),
	mvno_id INT NOT NULL,
	survey_area_id INT NOT NULL REFERENCES survey_area(id)
);


CREATE TABLE audit_ne_joint_closure (
    id SERIAL PRIMARY KEY,
    ne_joint_closure_id INT NOT NULL,
    operation TEXT CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
    operated_by BIGINT,
    operated_at TIMESTAMPTZ DEFAULT NOW(),
    snapshot JSONB
);


CREATE TABLE audit_ne_joint_closure_geom (
    id SERIAL PRIMARY KEY,
    audit_id INT REFERENCES audit_ne_joint_closure(id) ON DELETE CASCADE,
    geom GEOMETRY(Point, 4326)
);


CREATE OR REPLACE FUNCTION audit_ne_joint_closure()
RETURNS TRIGGER AS $$
DECLARE
    snapshot_data JSONB;
    affected_id INT;
    user_id BIGINT;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        snapshot_data := to_jsonb(OLD) - 'geom';
        affected_id := OLD.id;
        user_id := COALESCE(OLD.modified_by, OLD.created_by);
    ELSE
        snapshot_data := to_jsonb(NEW) - 'geom';
        affected_id := NEW.id;
        user_id := COALESCE(NEW.modified_by, NEW.created_by);
    END IF;

    INSERT INTO audit_ne_joint_closure (
        ne_joint_closure_id,
        operation,
        operated_by,
        operated_at,
        snapshot
    ) VALUES (
        affected_id,
        TG_OP,
        user_id,
        NOW(),
        snapshot_data
    ) RETURNING id INTO affected_id;

    INSERT INTO audit_ne_joint_closure_geom (audit_id, geom)
    VALUES (
        affected_id,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.geom
            ELSE NEW.geom
        END
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Main audit trigger
CREATE TRIGGER trg_audit_ne_joint_closure
AFTER INSERT OR UPDATE OR DELETE ON ne_joint_closure
FOR EACH ROW
EXECUTE FUNCTION audit_ne_joint_closure();

-- Custom ID update on insert
DROP TRIGGER IF EXISTS trg_ne_joint_closure_bi_row_custom_id ON ne_joint_closure;
CREATE TRIGGER trg_ne_joint_closure_bi_row_custom_id
BEFORE INSERT ON ne_joint_closure
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Custom ID update on geom update
DROP TRIGGER IF EXISTS trg_ne_joint_closure_bu_geom_custom_id ON ne_joint_closure;
CREATE TRIGGER trg_ne_joint_closure_bu_geom_custom_id
BEFORE UPDATE OF geom ON ne_joint_closure
FOR EACH ROW
EXECUTE FUNCTION update_custom_id_generic();

-- Administrative IDs update on insert
DROP TRIGGER IF EXISTS trg_ne_joint_closure_bi_row_adm_ids ON ne_joint_closure;
CREATE TRIGGER trg_ne_joint_closure_bi_row_adm_ids
BEFORE INSERT ON ne_joint_closure
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

-- Administrative IDs update on geom update
DROP TRIGGER IF EXISTS trg_ne_joint_closure_bu_geom_adm_ids ON ne_joint_closure;
CREATE TRIGGER trg_ne_joint_closure_bu_geom_adm_ids
BEFORE UPDATE OF geom ON ne_joint_closure
FOR EACH ROW
EXECUTE FUNCTION update_administrative_ids_generic();

CREATE INDEX idx_ne_joint_closure_geom ON ne_joint_closure USING GIST (geom);
CREATE INDEX idx_ne_joint_closure_public_id ON ne_joint_closure(public_id);
CREATE INDEX idx_ne_joint_closure_custom_id ON ne_joint_closure(custom_id);


