CREATE TABLE bom_master (
	id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    survey_area_id INT NOT NULL REFERENCES survey_area(id),
    status TEXT NOT NULL CHECK (status IN ('Active', 'Inactive')),
	created_by <PERSON><PERSON><PERSON><PERSON>,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by B<PERSON>IN<PERSON>
);


CREATE TABLE bom_version (
    id SERIAL PRIMARY KEY,
    bom_master_id INT NOT NULL REFERENCES bom_master(id),

    version_no INT NOT NULL,
    file_name TEXT,      -- latest/generated Excel file
    file_path TEXT,      -- full path or relative path to file

    created_by B<PERSON>IN<PERSON>,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    modified_on TIMESTAMPTZ,
    modified_by BIGINT,
    CONSTRAINT unique_bom_version UNIQUE (bom_master_id, version_no)
);