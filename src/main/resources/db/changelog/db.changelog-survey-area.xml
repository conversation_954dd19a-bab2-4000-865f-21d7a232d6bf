<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="survey000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="lookup_survey_status"/></not>
                <not><tableExists tableName="survey_area"/></not>
            </or>
        </preConditions>
        <!-- Lookup Table -->
		<createTable tableName="lookup_survey_status">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
        </createTable>

        <!-- Seed Data -->
        <insert tableName="lookup_survey_status">
            <column name="name" value="Planned"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_status">
            <column name="name" value="InActive"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>


        <!-- Survey Area Table -->
		<createTable tableName="survey_area">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="survey_status_id" type="INT"/>
            <column name="geom" type="geometry(Polygon, 4326)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="survey_start_date" type="TIMESTAMPTZ"/>
            <column name="survey_end_date" type="TIMESTAMPTZ"/>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Foreign Keys -->
        <addForeignKeyConstraint baseTableName="survey_area" baseColumnNames="survey_status_id"
                                 referencedTableName="lookup_survey_status" referencedColumnNames="id"
                                 constraintName="fk_survey_area_survey_status"/>
        <addForeignKeyConstraint baseTableName="survey_area" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_survey_area_adm1"/>
        <addForeignKeyConstraint baseTableName="survey_area" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_survey_area_adm2"/>

        <!-- Check Constraints -->
        <sql>
            ALTER TABLE survey_area ADD CONSTRAINT chk_survey_area_geom_valid CHECK (ST_IsValid(geom));
        </sql>

        <!-- Indexes -->
        <sql>CREATE INDEX survey_area_geom_idx ON survey_area USING GIST (geom);</sql>
        <createIndex indexName="idx_survey_area_public_id_idx" tableName="survey_area">
            <column name="public_id"/>
        </createIndex>

        <!-- Triggers -->
        <sql>
            DROP TRIGGER IF EXISTS trg_survey_area_bi_row_adm_ids ON survey_area;
            CREATE TRIGGER trg_survey_area_bi_row_adm_ids
            BEFORE INSERT ON survey_area
            FOR EACH ROW
            EXECUTE FUNCTION update_administrative_ids_generic();

            DROP TRIGGER IF EXISTS trg_survey_area_bu_geom_adm_ids ON survey_area;
            CREATE TRIGGER trg_survey_area_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON survey_area
            FOR EACH ROW
            EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>

    <changeSet id="survey000002" author="initial-setup">
        <!-- TODO At the end move this insert changeset to survey000001 before creating NEW DB -->

        <!-- Rename existing entries instead of deleting -->
        <update tableName="lookup_survey_status">
            <column name="name" value="Approved"/>
            <where>name = 'Planned'</where>
        </update>

        <update tableName="lookup_survey_status">
            <column name="name" value="Initiated"/>
            <where>name = 'InActive'</where>
        </update>

        <!-- Insert new status values -->
        <insert tableName="lookup_survey_status">
            <column name="name" value="In progress"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_status">
            <column name="name" value="Completed"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_status">
            <column name="name" value="Review"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_status">
            <column name="name" value="Rejected"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_status">
            <column name="name" value="Done"/>
            <column name="description" value="NA"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

    </changeSet>
    <changeSet id="survey000003" author="initial-setup">
        <insert tableName="lookup_survey_status">
            <column name="name" value="Assigned"/>
            <column name="description" value="Assigned"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>
    </changeSet>



    <!-- create lookup survey stage table -->
    <changeSet id="survey000004" author="initial-setup">
        <createTable tableName="lookup_survey_stage">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ" />
        </createTable>

        <!-- Seed Data -->
        <insert tableName="lookup_survey_stage">
            <column name="name" value="Survey"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="Digitalization"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="Design"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="BOM"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="Inventory acquisition"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="Build"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

        <insert tableName="lookup_survey_stage">
            <column name="name" value="Quality Check"/>
            <column name="description" value=""/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_on" valueDate="NOW()"/>
        </insert>

    </changeSet>

    <!-- add survey stage fk to survey area -->
    <changeSet id="survey000005" author="initial-setup">
        <!-- Step 1: Add column as nullable -->
        <addColumn tableName="survey_area">
            <column name="survey_stage_id" type="INT"/>
        </addColumn>

        <!-- Step 2: Add foreign key -->
        <addForeignKeyConstraint
                baseTableName="survey_area"
                baseColumnNames="survey_stage_id"
                referencedTableName="lookup_survey_stage"
                referencedColumnNames="id"
                constraintName="fk_survey_area_survey_stage_id"/>

        <!-- Step 3: Set default value for existing rows -->
        <comment>Set default survey_stage_id to 1 for existing rows where it is NULL</comment>
        <sql>
            UPDATE survey_area
            SET survey_stage_id = (
            SELECT id FROM lookup_survey_stage WHERE name = 'Survey'
            )
            WHERE survey_stage_id IS NULL;
        </sql>

        <!-- Step 4: Alter column to NOT NULL -->
        <addNotNullConstraint tableName="survey_area" columnName="survey_stage_id" columnDataType="INT"/>
    </changeSet>

    <changeSet id="survey000006" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="lookup_survey_stage" columnName="mvno_id"/>
            </not>
        </preConditions>

        <addColumn tableName="lookup_survey_stage">
            <column name="mvno_id" type="INT" defaultValueNumeric="2">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="survey000007" author="initial-setup">
        <addColumn tableName="lookup_survey_status">
            <column name="mvno_id" type="INT"  defaultValueNumeric="2">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>


</databaseChangeLog>