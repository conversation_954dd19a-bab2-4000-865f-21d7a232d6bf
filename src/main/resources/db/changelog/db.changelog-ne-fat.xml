<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="fat000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_fat"/></not>
                <not><tableExists tableName="audit_ne_fat"/></not>
                <not><tableExists tableName="audit_ne_fat_geom"/></not>
            </or>
        </preConditions>
        <!-- Main Table -->
		<createTable tableName="ne_fat">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT"/>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT"/>
                <column name="power_levels" type="DECIMAL(10,2)"/>
                <column name="remarks" type="TEXT">
                 <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_fat" columnNames="custom_id" constraintName="uk_ne_fat_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_fat" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_fat_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_fat" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_fat_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_fat" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_fat_survey_area_id"/>


        <!-- Audit Tables -->
		<createTable tableName="audit_ne_fat">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_fat_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_fat ADD CONSTRAINT chk_audit_ne_fat_op CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_fat_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_fat_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_fat" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_fat_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_fat_geom ON ne_fat USING GIST (geom);</sql>
        <createIndex indexName="idx_ne_fat_public_id" tableName="ne_fat">
            <column name="public_id"/>
        </createIndex>
        <createIndex indexName="idx_ne_fat_custom_id" tableName="ne_fat">
            <column name="custom_id"/>
        </createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_fat() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            new_audit_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_fat (ne_fat_id, operation, operated_by, snapshot)
            VALUES (
            COALESCE(NEW.id, OLD.id),
            TG_OP,
            user_id,
            snapshot_data
            )
            RETURNING id INTO new_audit_id;

            INSERT INTO audit_ne_fat_geom (audit_id, geom)
            VALUES (
            new_audit_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <!-- Triggers -->
        <sql>CREATE TRIGGER trg_audit_ne_fat AFTER INSERT OR UPDATE OR DELETE ON ne_fat FOR EACH ROW EXECUTE FUNCTION audit_ne_fat();</sql>
        <sql>DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_custom_id ON ne_fat;</sql>
        <sql>CREATE TRIGGER trg_ne_fat_bi_row_custom_id BEFORE INSERT ON ne_fat FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();</sql>
        <sql>DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_custom_id ON ne_fat;</sql>
        <sql>CREATE TRIGGER trg_ne_fat_bu_geom_custom_id BEFORE UPDATE OF geom ON ne_fat FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();</sql>
        <sql>DROP TRIGGER IF EXISTS trg_ne_fat_bi_row_adm_ids ON ne_fat;</sql>
        <sql>CREATE TRIGGER trg_ne_fat_bi_row_adm_ids BEFORE INSERT ON ne_fat FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();</sql>
        <sql>DROP TRIGGER IF EXISTS trg_ne_fat_bu_geom_adm_ids ON ne_fat;</sql>
        <sql>CREATE TRIGGER trg_ne_fat_bu_geom_adm_ids BEFORE UPDATE OF geom ON ne_fat FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();</sql>

    </changeSet>

    <changeSet id="fat000002" author="arpit">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="ne_fat" columnName="remarks"/>
        </preConditions>

        <dropNotNullConstraint tableName="ne_fat"
                               columnName="remarks"
                               columnDataType="text"/>
    </changeSet>

</databaseChangeLog>