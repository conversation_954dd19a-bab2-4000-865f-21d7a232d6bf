<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="handhole000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_handhole"/></not>
                <not><tableExists tableName="audit_ne_handhole"/></not>
                <not><tableExists tableName="audit_ne_handhole_geom"/></not>
            </or>
        </preConditions>
        <!-- Main Table -->
		<createTable tableName="ne_handhole">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="hole_size" type="TEXT"/>
            <column name="access_type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="material" type="TEXT"/>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_handhole" columnNames="custom_id" constraintName="uk_ne_handhole_custom_id"/>

        <addForeignKeyConstraint baseTableName="ne_handhole" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_handhole_adm1"/>

        <addForeignKeyConstraint baseTableName="ne_handhole" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_handhole_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_handhole" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_handhole_survey_area_id"/>

        <!-- Check constraints -->
            <sql>ALTER TABLE ne_handhole ADD CONSTRAINT chk_ne_handhole_access_type CHECK (access_type IN ('TopOpen', 'SideAccess'));</sql>

        <!-- Audit Tables -->
		<createTable tableName="audit_ne_handhole">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_handhole_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_handhole ADD CONSTRAINT chk_audit_ne_handhole_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_handhole_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_handhole_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_handhole" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_handhole_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_handhole_geom ON ne_handhole USING GIST (geom);</sql>
        <createIndex tableName="ne_handhole" indexName="idx_ne_handhole_public_id">
            <column name="public_id"/>
        </createIndex>
        <createIndex tableName="ne_handhole" indexName="idx_ne_handhole_custom_id">
            <column name="custom_id"/>
        </createIndex>

        <!-- Trigger Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_handhole() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            affected_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            affected_id := OLD.id;
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            affected_id := NEW.id;
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_handhole (ne_handhole_id, operation, operated_by, operated_at, snapshot)
            VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
            RETURNING id INTO affected_id;

            INSERT INTO audit_ne_handhole_geom (audit_id, geom)
            VALUES (affected_id, CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END);

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_handhole
            AFTER INSERT OR UPDATE OR DELETE ON ne_handhole
            FOR EACH ROW EXECUTE FUNCTION audit_ne_handhole();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_handhole_bi_row_custom_id ON ne_handhole;
            CREATE TRIGGER trg_ne_handhole_bi_row_custom_id
            BEFORE INSERT ON ne_handhole
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_handhole_bu_geom_custom_id ON ne_handhole;
            CREATE TRIGGER trg_ne_handhole_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_handhole
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_handhole_bi_row_adm_ids ON ne_handhole;
            CREATE TRIGGER trg_ne_handhole_bi_row_adm_ids
            BEFORE INSERT ON ne_handhole
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_handhole_bu_geom_adm_ids ON ne_handhole;
            CREATE TRIGGER trg_ne_handhole_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_handhole
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>
</databaseChangeLog>