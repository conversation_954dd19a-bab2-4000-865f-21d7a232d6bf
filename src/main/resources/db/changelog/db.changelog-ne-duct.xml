<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="duct000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_duct"/></not>
                <not><tableExists tableName="audit_ne_duct"/></not>
                <not><tableExists tableName="audit_ne_duct_geom"/></not>
            </or>
        </preConditions>
        <!-- Main table -->
		<createTable tableName="ne_duct">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT"/>
            <column name="material" type="TEXT"/>
            <column name="diameter_mm" type="NUMERIC"/>
            <column name="length_m" type="NUMERIC"/>
            <column name="status" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="install_date" type="DATE"/>
            <column name="owner" type="TEXT"/>
            <column name="trench_id" type="INTEGER"/>
            <column name="geom" type="geometry(LineString,4326)"/>
            <column name="start_node_id" type="BIGINT"/>
            <column name="end_node_id" type="BIGINT"/>
            <column name="num_subducts" type="INTEGER"/>
            <column name="used_subducts" type="INTEGER"/>
            <!-- removed invalid defaultValueComputed -->
            <column name="available_subducts" type="INTEGER"/>
            <column name="network_type" type="TEXT"/>
            <column name="remarks" type="TEXT"/>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Calculate available_subducts as a generated column -->
        <sql>
            ALTER TABLE ne_duct DROP COLUMN available_subducts;
        </sql>
        <sql>
            ALTER TABLE ne_duct ADD COLUMN available_subducts INTEGER GENERATED ALWAYS AS (num_subducts - used_subducts) STORED;
        </sql>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_duct" columnNames="custom_id" constraintName="uk_ne_duct_custom_id"/>

        <addForeignKeyConstraint baseTableName="ne_duct" baseColumnNames="trench_id"
                                 referencedTableName="ne_trench" referencedColumnNames="id"
                                 constraintName="fk_ne_duct_trench"/>
        <addForeignKeyConstraint baseTableName="ne_duct" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_duct_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_duct" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_duct_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_duct" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_duct_survey_area_id"/>

        <!-- Check constraints -->
        <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_diameter CHECK (diameter_mm &gt; 0);</sql>
        <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_length CHECK (length_m &gt; 0);</sql>
        <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_num_subducts CHECK (num_subducts &gt;= 0);</sql>
        <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_used_subducts CHECK (used_subducts &gt;= 0);</sql>
          <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_install_date CHECK (install_date &lt;= CURRENT_DATE);</sql>
        <sql>ALTER TABLE ne_duct ADD CONSTRAINT chk_ne_duct_network_type CHECK (network_type IN ('Backbone', 'Access', 'Distribution'));</sql>

        <!-- Audit Tables -->
		<createTable tableName="audit_ne_duct">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="duct_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="TEXT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>
        <sql>ALTER TABLE audit_ne_duct ADD CONSTRAINT chk_audit_ne_duct_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));</sql>

		<createTable tableName="audit_ne_duct_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(LineString,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_duct_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_duct" referencedColumnNames="id"
                                 onDelete="CASCADE"
                                 constraintName="fk_audit_ne_duct_geom_audit_id"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_duct_geom ON ne_duct USING GIST (geom);</sql>
        <createIndex tableName="ne_duct" indexName="idx_ne_duct_public_id"><column name="public_id"/></createIndex>
        <createIndex tableName="ne_duct" indexName="idx_ne_duct_custom_id"><column name="custom_id"/></createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_duct()
            RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            affected_id INT;
            user_id TEXT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            affected_id := OLD.id;
            user_id := COALESCE(OLD.modified_by::TEXT, OLD.created_by::TEXT);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            affected_id := NEW.id;
            user_id := COALESCE(NEW.modified_by::TEXT, NEW.created_by::TEXT);
            END IF;

            INSERT INTO audit_ne_duct (duct_id, operation, operated_by, operated_at, snapshot)
            VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
            RETURNING id INTO affected_id;

            INSERT INTO audit_ne_duct_geom (audit_id, geom)
            VALUES (affected_id, CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END);

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>CREATE TRIGGER trg_audit_ne_duct AFTER INSERT OR UPDATE OR DELETE ON ne_duct FOR EACH ROW EXECUTE FUNCTION audit_ne_duct();</sql>
        <sql>CREATE TRIGGER trg_ne_duct_bi_row_custom_id BEFORE INSERT ON ne_duct FOR EACH ROW EXECUTE FUNCTION update_custom_id_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_duct_bu_geom_custom_id BEFORE UPDATE OF geom ON ne_duct FOR EACH ROW EXECUTE FUNCTION update_custom_id_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_duct_bi_row_adm_ids BEFORE INSERT ON ne_duct FOR EACH ROW EXECUTE FUNCTION update_adm_ids_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_duct_bu_geom_adm_ids BEFORE UPDATE OF geom ON ne_duct FOR EACH ROW EXECUTE FUNCTION update_adm_ids_linestring();</sql>

    </changeSet>

    <changeSet id="duct000002" author="initial-setup">
        <addColumn tableName="ne_duct">
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
        </addColumn>
    </changeSet>

    <changeSet id="remove-trench-id-column" author="initial-setup">
        <dropColumn tableName="ne_duct" columnName="trench_id"/>
    </changeSet>

</databaseChangeLog>
