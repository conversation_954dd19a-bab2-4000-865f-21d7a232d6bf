<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="jointclosure000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_joint_closure"/></not>
                <not><tableExists tableName="audit_ne_joint_closure"/></not>
                <not><tableExists tableName="audit_ne_joint_closure_geom"/></not>
            </or>
        </preConditions>
        <!-- Main Table -->
		<createTable tableName="ne_joint_closure">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="joint_type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="capacity" type="INT"/>
            <column name="mounted_in" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Unique Constraint -->
        <addUniqueConstraint tableName="ne_joint_closure"
                             columnNames="custom_id"
                             constraintName="uk_ne_joint_closure_custom_id"/>

        <!-- Foreign Keys -->
        <addForeignKeyConstraint baseTableName="ne_joint_closure"
                                 baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01"
                                 referencedColumnNames="id"
                                 constraintName="fk_joint_closure_adm1"/>

        <addForeignKeyConstraint baseTableName="ne_joint_closure"
                                 baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02"
                                 referencedColumnNames="id"
                                 constraintName="fk_joint_closure_adm2"/>

        <addForeignKeyConstraint baseTableName="ne_joint_closure" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_joint_closure_survey_area_id"/>

        <!-- Check Constraints -->
        <sql>ALTER TABLE ne_joint_closure ADD CONSTRAINT chk_joint_type CHECK (joint_type IN ('Splice', 'Splitter', 'Transition'));</sql>
        <sql>ALTER TABLE ne_joint_closure ADD CONSTRAINT chk_mounted_in CHECK (mounted_in IN ('Handhole', 'Manhole', 'Pole', 'Cabinet'));</sql>
        <sql>ALTER TABLE ne_joint_closure ADD CONSTRAINT chk_status_joint_closure CHECK (status IN ('Planned', 'InService', 'Inactive'));</sql>

        <!-- Audit Tables -->
		<createTable tableName="audit_ne_joint_closure">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_joint_closure_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_joint_closure ADD CONSTRAINT chk_audit_joint_closure_op
            CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_joint_closure_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_joint_closure_geom"
                                 baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_joint_closure"
                                 referencedColumnNames="id"
                                 onDelete="CASCADE"
                                 constraintName="fk_audit_joint_closure_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_joint_closure_geom ON ne_joint_closure USING GIST (geom);</sql>
        <createIndex indexName="idx_ne_joint_closure_public_id" tableName="ne_joint_closure">
            <column name="public_id"/>
        </createIndex>
        <createIndex indexName="idx_ne_joint_closure_custom_id" tableName="ne_joint_closure">
            <column name="custom_id"/>
        </createIndex>

        <!-- Trigger Function and Triggers -->
        <sql><![CDATA[
            CREATE OR REPLACE FUNCTION audit_ne_joint_closure()
            RETURNS TRIGGER AS
            E'
            DECLARE
                snapshot_data JSONB;
                affected_id INT;
                user_id BIGINT;
            BEGIN
                IF (TG_OP = ''DELETE'') THEN
                    snapshot_data := to_jsonb(OLD) - ''geom'';
                    affected_id := OLD.id;
                    user_id := COALESCE(OLD.modified_by, OLD.created_by);
                ELSE
                    snapshot_data := to_jsonb(NEW) - ''geom'';
                    affected_id := NEW.id;
                    user_id := COALESCE(NEW.modified_by, NEW.created_by);
                END IF;

                INSERT INTO audit_ne_joint_closure (ne_joint_closure_id, operation, operated_by, operated_at, snapshot)
                VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
                RETURNING id INTO affected_id;

                INSERT INTO audit_ne_joint_closure_geom (audit_id, geom)
                VALUES (affected_id, CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END);

                RETURN NULL;
            END;
            ' LANGUAGE plpgsql;

            CREATE TRIGGER trg_audit_ne_joint_closure
            AFTER INSERT OR UPDATE OR DELETE ON ne_joint_closure
            FOR EACH ROW EXECUTE FUNCTION audit_ne_joint_closure();

            DROP TRIGGER IF EXISTS trg_ne_joint_closure_bi_row_custom_id ON ne_joint_closure;
            CREATE TRIGGER trg_ne_joint_closure_bi_row_custom_id
            BEFORE INSERT ON ne_joint_closure
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            DROP TRIGGER IF EXISTS trg_ne_joint_closure_bu_geom_custom_id ON ne_joint_closure;
            CREATE TRIGGER trg_ne_joint_closure_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_joint_closure
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            DROP TRIGGER IF EXISTS trg_ne_joint_closure_bi_row_adm_ids ON ne_joint_closure;
            CREATE TRIGGER trg_ne_joint_closure_bi_row_adm_ids
            BEFORE INSERT ON ne_joint_closure
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();

            DROP TRIGGER IF EXISTS trg_ne_joint_closure_bu_geom_adm_ids ON ne_joint_closure;
            CREATE TRIGGER trg_ne_joint_closure_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_joint_closure
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        ]]></sql>
    </changeSet>

    <changeSet id="jointclosure000002" author="initial-setup">
        <addColumn tableName="ne_joint_closure">
            <column name="port_count" type="INT"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>