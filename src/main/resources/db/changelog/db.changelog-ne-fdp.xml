<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="fdp000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="lookup_fdp_types"/></not>
                <not><tableExists tableName="ne_fdp"/></not>
                <not><tableExists tableName="audit_ne_fdp"/></not>
                <not><tableExists tableName="audit_ne_fdp_geom"/></not>
            </or>
        </preConditions>
        <!-- Lookup table -->
		<createTable tableName="lookup_fdp_types">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_on" type="TIMESTAMP" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMP"/>
        </createTable>

        <insert tableName="lookup_fdp_types">
            <column name="name" value="Indoor"/>
            <column name="description" value="Installed within buildings or protective enclosures"/>
        </insert>
        <insert tableName="lookup_fdp_types">
            <column name="name" value="Outdoor"/>
            <column name="description" value="Designed for external environments with weatherproofing"/>
        </insert>
        <insert tableName="lookup_fdp_types">
            <column name="name" value="Wall-Mounted"/>
            <column name="description" value="Fixed on walls; can be indoor or outdoor"/>
        </insert>
        <insert tableName="lookup_fdp_types">
            <column name="name" value="Pole-Mounted"/>
            <column name="description" value="Mounted on utility poles; used in aerial deployments"/>
        </insert>
        <insert tableName="lookup_fdp_types">
            <column name="name" value="Rack-Mounted"/>
            <column name="description" value="Fits into telecom racks in equipment rooms"/>
        </insert>

        <!-- Main table -->
		<createTable tableName="ne_fdp">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="port" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="fdp_type_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="pop_id" type="INT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="ne_fdp" columnNames="custom_id" constraintName="uk_ne_fdp_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_fdp" baseColumnNames="fdp_type_id"
                                 referencedTableName="lookup_fdp_types" referencedColumnNames="id"
                                 constraintName="fk_ne_fdp_type"/>
        <addForeignKeyConstraint baseTableName="ne_fdp" baseColumnNames="pop_id"
                                 referencedTableName="ne_pop" referencedColumnNames="id"
                                 constraintName="fk_ne_fdp_pop"/>
        <addForeignKeyConstraint baseTableName="ne_fdp" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_fdp_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_fdp" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_fdp_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_fdp" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_fdp_survey_area_id"/>



        <!-- Audit tables -->
		<createTable tableName="audit_ne_fdp">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_fdp_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_fdp ADD CONSTRAINT chk_audit_ne_fdp_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_fdp_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_fdp_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_fdp" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_fdp_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_fdp_geom ON ne_fdp USING GIST (geom);</sql>
        <createIndex tableName="ne_fdp" indexName="idx_ne_fdp_public_id">
            <column name="public_id"/>
        </createIndex>
        <createIndex tableName="ne_fdp" indexName="idx_ne_fdp_custom_id">
            <column name="custom_id"/>
        </createIndex>
        <createIndex tableName="ne_fdp" indexName="idx_ne_fdp_type_id">
            <column name="fdp_type_id"/>
        </createIndex>

        <!-- Trigger Function and Attachments -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_fdp()
            RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            audit_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_fdp (ne_fdp_id, operation, operated_by, operated_at, snapshot)
            VALUES (COALESCE(NEW.id, OLD.id), TG_OP, user_id, NOW(), snapshot_data)
            RETURNING id INTO audit_id;

            INSERT INTO audit_ne_fdp_geom (audit_id, geom)
            VALUES (audit_id, CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END);

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_fdp
            AFTER INSERT OR UPDATE OR DELETE ON ne_fdp
            FOR EACH ROW EXECUTE FUNCTION audit_ne_fdp();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_custom_id ON ne_fdp;
            CREATE TRIGGER trg_ne_fdp_bi_row_custom_id
            BEFORE INSERT ON ne_fdp
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_custom_id ON ne_fdp;
            CREATE TRIGGER trg_ne_fdp_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_fdp
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdp_bi_row_adm_ids ON ne_fdp;
            CREATE TRIGGER trg_ne_fdp_bi_row_adm_ids
            BEFORE INSERT ON ne_fdp
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdp_bu_geom_adm_ids ON ne_fdp;
            CREATE TRIGGER trg_ne_fdp_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_fdp
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>
</databaseChangeLog>