<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="enable-postgis" author="initial-setup" runAlways="true">
        <sql>
            CREATE EXTENSION IF NOT EXISTS postgis;
            -- Execute for gen_random_uuid();
            CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        </sql>
    </changeSet>

</databaseChangeLog>
