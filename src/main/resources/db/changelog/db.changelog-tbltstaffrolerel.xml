<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Staff Role Rel Table  -->
    <changeSet id="TBLSTFRL000000001" author="DarshitPatel">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tbltstaffrolerel"/>
            </not>
        </preConditions>
        <!-- Use SQL block for identity -->
        <sql>
            CREATE TABLE IF NOT EXISTS tbltstaffrolerel (
            staffrolerelid BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
            staffid BIGINT NOT NULL,
            roleid BIGINT NOT NULL,
            CONSTRAINT tbltstaffrolerel_staffrolerelid_unq UNIQUE (staffrolerelid)
            );
        </sql>
    </changeSet>
    <!-- Insert Default Data (staffid=1, roleid=1) -->
    <changeSet id="TBLSTFRL000000002" author="DarshitPatel">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM tbltstaffrolerel WHERE staffid=1 AND roleid=1;
            </sqlCheck>
        </preConditions>
        <insert tableName="tbltstaffrolerel">
            <!-- staffrolerelid will auto-generate -->
            <column name="staffid" value="1"/>
            <column name="roleid" value="1"/>
        </insert>
    </changeSet>
    <!-- Insert Default Data (staffid=2, roleid=1) -->
    <changeSet id="TBLSTFRL000000003" author="DarshitPatel">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM tbltstaffrolerel WHERE staffid=2 AND roleid=1;
            </sqlCheck>
        </preConditions>
        <insert tableName="tbltstaffrolerel">
            <!-- staffrolerelid will auto-generate -->
            <column name="staffid" value="2"/>
            <column name="roleid" value="1"/>
        </insert>
    </changeSet>

    <changeSet id="TBLSTFRL000000004" author="DarshitPatel">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="tbltstaffrolerel" columnName="mvnoid"/>
            </not>
        </preConditions>

        <addColumn tableName="tbltstaffrolerel">
            <column name="mvnoid" type="INTEGER"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="tbltstaffrolerel" columnName="mvnoid"/>
        </rollback>
    </changeSet>



</databaseChangeLog>
