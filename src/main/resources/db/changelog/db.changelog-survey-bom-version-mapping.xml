<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Staff Role Rel Table  -->
    <changeSet id="bom_version_mapping000000001" author="DarshitPatel">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="survey_bom_version_mapping"/>
            </not>
        </preConditions>

        <createTable tableName="survey_bom_version_mapping">
            <column name="id" type="INTEGER" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>

            <column name="survey_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>

            <column name="bom_version_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>

            <column name="layer_inventory_mapping_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>

            <!-- Network Element Columns -->
            <column name="ne_cable_id" type="INTEGER"/>
            <column name="ne_cdu_id" type="INTEGER"/>
            <column name="ne_customer_id" type="INTEGER"/>
            <column name="ne_duct_id" type="INTEGER"/>
            <column name="ne_fat_id" type="INTEGER"/>
            <column name="ne_fdp_id" type="INTEGER"/>
            <column name="ne_fdt_id" type="INTEGER"/>
            <column name="ne_handhole_id" type="INTEGER"/>
            <column name="ne_joint_closure_id" type="INTEGER"/>
            <column name="ne_manhole_id" type="INTEGER"/>
            <column name="ne_mdu_id" type="INTEGER"/>
            <column name="ne_pole_id" type="INTEGER"/>
            <column name="ne_pop_id" type="INTEGER"/>
            <column name="ne_sdu_id" type="INTEGER"/>
            <column name="ne_splitter_id" type="INTEGER"/>
            <column name="ne_street_id" type="INTEGER"/>
            <column name="ne_trench_id" type="INTEGER"/>
            <column name="ne_olt_id" type="INTEGER"/>
            <column name="ne_odf_id" type="INTEGER"/>


            <!-- Audit fields -->
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(100)"/>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
            <column name="status"  type="VARCHAR(50)"/>
        </createTable>

        <!-- Foreign Key Constraints (NO CASCADE) -->
        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="survey_id"
                                 referencedTableName="survey_area"
                                 referencedColumnNames="id"
                                 constraintName="fk_survey_area"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="bom_version_id"
                                 referencedTableName="bom_version"
                                 referencedColumnNames="id"
                                 constraintName="fk_bom_version"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="layer_inventory_mapping_id"
                                 referencedTableName="layer_inventory_mapping"
                                 referencedColumnNames="id"
                                 constraintName="fk_layer_inventory_mapping"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_cable_id"
                                 referencedTableName="ne_cable"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_cable"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_cdu_id"
                                 referencedTableName="ne_cdu"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_cdu"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_customer_id"
                                 referencedTableName="ne_customer"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_customer"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_duct_id"
                                 referencedTableName="ne_duct"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_duct"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_fat_id"
                                 referencedTableName="ne_fat"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_fat"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_fdp_id"
                                 referencedTableName="ne_fdp"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_fdp"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_fdt_id"
                                 referencedTableName="ne_fdt"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_fdt"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_handhole_id"
                                 referencedTableName="ne_handhole"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_handhole"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_joint_closure_id"
                                 referencedTableName="ne_joint_closure"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_joint_closure"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_manhole_id"
                                 referencedTableName="ne_manhole"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_manhole"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_mdu_id"
                                 referencedTableName="ne_mdu"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_mdu"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_pole_id"
                                 referencedTableName="ne_pole"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_pole"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_pop_id"
                                 referencedTableName="ne_pop"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_pop"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_sdu_id"
                                 referencedTableName="ne_sdu"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_sdu"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_splitter_id"
                                 referencedTableName="ne_splitter"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_splitter"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_street_id"
                                 referencedTableName="ne_street"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_street"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_trench_id"
                                 referencedTableName="ne_trench"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_trench"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_olt_id"
                                 referencedTableName="ne_olt"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_olt"/>

        <addForeignKeyConstraint baseTableName="survey_bom_version_mapping"
                                 baseColumnNames="ne_odf_id"
                                 referencedTableName="ne_odf"
                                 referencedColumnNames="id"
                                 constraintName="fk_ne_odf"/>
    </changeSet>

</databaseChangeLog>
