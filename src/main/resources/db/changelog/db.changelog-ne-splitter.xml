<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
            http://www.liquibase.org/xml/ns/dbchangelog
            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="splitter000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="splitter_specification"/></not>
                <not><tableExists tableName="ne_splitter"/></not>
                <not><tableExists tableName="audit_ne_splitter"/></not>
                <not><tableExists tableName="audit_ne_splitter_geom"/></not>
            </or>
        </preConditions>
        <!-- Lookup Table -->
		<createTable tableName="splitter_specification">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="port_ratio" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_on" type="TIMESTAMP" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMP"/>
        </createTable>

        <!-- Initial Data -->
        <sql>
            INSERT INTO splitter_specification (name, port_ratio, description) VALUES
            ('1:4 Splitter', '1:4', 'Compact size splitter for wall mounting'),
            ('1:8 Splitter', '1:8', 'Suitable for medium distribution'),
            ('1:16 Splitter', '1:16', 'High-capacity splitter'),
            ('1:32 Splitter', '1:32', 'Large capacity splitter for POP usage'),
            ('1:2 Splitter', '1:2', 'Low-ratio splitter for indoor use');
        </sql>

        <!-- Main Table -->
		<createTable tableName="ne_splitter">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT" />
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="specification_id" type="INT"/>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_splitter" columnNames="custom_id" constraintName="uk_ne_splitter_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_splitter" baseColumnNames="specification_id"
                                 referencedTableName="splitter_specification" referencedColumnNames="id"
                                 constraintName="fk_ne_splitter_specification"/>
        <addForeignKeyConstraint baseTableName="ne_splitter" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_splitter_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_splitter" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_splitter_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_splitter" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_splitter_survey_area_id"/>


        <!-- Audit Tables -->
		<createTable tableName="audit_ne_splitter">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="splitter_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_splitter ADD CONSTRAINT chk_audit_ne_splitter_op CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_splitter_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_splitter_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_splitter" referencedColumnNames="id"
                                 onDelete="CASCADE"
                                 constraintName="fk_audit_ne_splitter_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_splitter_geom ON ne_splitter USING GIST (geom);</sql>
        <createIndex indexName="idx_ne_splitter_public_id" tableName="ne_splitter">
            <column name="public_id"/>
        </createIndex>
        <createIndex indexName="idx_ne_splitter_custom_id" tableName="ne_splitter">
            <column name="custom_id"/>
        </createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_splitter() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            audit_rec_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_splitter (splitter_id, operation, operated_by, operated_at, snapshot)
            VALUES (
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.id ELSE NEW.id END,
            TG_OP, user_id, NOW(), snapshot_data
            )
            RETURNING id INTO audit_rec_id;

            INSERT INTO audit_ne_splitter_geom (audit_id, geom)
            VALUES (
            audit_rec_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_splitter
            AFTER INSERT OR UPDATE OR DELETE ON ne_splitter
            FOR EACH ROW EXECUTE FUNCTION audit_ne_splitter();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_splitter_bi_row_custom_id ON ne_splitter;
            CREATE TRIGGER trg_ne_splitter_bi_row_custom_id
            BEFORE INSERT ON ne_splitter
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_splitter_bu_geom_custom_id ON ne_splitter;
            CREATE TRIGGER trg_ne_splitter_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_splitter
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_splitter_bi_row_adm_ids ON ne_splitter;
            CREATE TRIGGER trg_ne_splitter_bi_row_adm_ids
            BEFORE INSERT ON ne_splitter
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_splitter_bu_geom_adm_ids ON ne_splitter;
            CREATE TRIGGER trg_ne_splitter_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_splitter
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>

    <changeSet id="splitter000002" author="initial-setup">
        <addColumn tableName="ne_splitter">
            <column name="port_count" type="INT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>