<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <!-- Create table -->
    <changeSet id="layer_master_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="layer_master"/></not>
            </or>
        </preConditions>
		<createTable tableName="layer_master">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="display_name" type="text"/>
            <column name="table_name" type="text"/>
            <column name="status" type="text" defaultValue="Active"/>
            <column name="icon" type="text"/>
            <column name="code" type="varchar"/>
        </createTable>

        <sql>
            ALTER TABLE layer_master
            ADD CONSTRAINT layer_master_status_check
            CHECK (status IN ('Active', 'Inactive'));
        </sql>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="1"/>
            <column name="name" value="Administrative Boundary Level 0"/>
            <column name="display_name" value="Country"/>
            <column name="table_name" value="administrative_00"/>
            <column name="status" value="Active"/>
            <column name="icon" value="countryicon.png"/>
            <column name="code" value="country"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="2"/>
            <column name="name" value="Administrative Boundary Level 1"/>
            <column name="display_name" value="County"/>
            <column name="table_name" value="administrative_01"/>
            <column name="status" value="Active"/>
            <column name="icon" value="countyicon.png"/>
            <column name="code" value="county"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="3"/>
            <column name="name" value="Administrative Boundary Level 2"/>
            <column name="display_name" value="District"/>
            <column name="table_name" value="administrative_02"/>
            <column name="status" value="Active"/>
            <column name="icon" value="districticon.png"/>
            <column name="code" value="district"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="4"/>
            <column name="name" value="Cable"/>
            <column name="display_name" value="Cable"/>
            <column name="table_name" value="ne_cable"/>
            <column name="status" value="Active"/>
            <column name="icon" value="cabelicon.png"/>
            <column name="code" value="cable"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="5"/>
            <column name="name" value="Customer"/>
            <column name="display_name" value="Customer"/>
            <column name="table_name" value="ne_customer"/>
            <column name="status" value="Active"/>
            <column name="icon" value="customericon.png"/>
            <column name="code" value="customer"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="6"/>
            <column name="name" value="Fiber Access Terminal"/>
            <column name="display_name" value="Fat"/>
            <column name="table_name" value="ne_fat"/>
            <column name="status" value="Active"/>
            <column name="icon" value="faticon.png"/>
            <column name="code" value="fat"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="7"/>
            <column name="name" value="Fiber Distribution Point"/>
            <column name="display_name" value="Fdp"/>
            <column name="table_name" value="ne_fdp"/>
            <column name="status" value="Active"/>
            <column name="icon" value="fdpicon.png"/>
            <column name="code" value="fdp"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="8"/>
            <column name="name" value="Point of Presence"/>
            <column name="display_name" value="Pop"/>
            <column name="table_name" value="ne_pop"/>
            <column name="status" value="Active"/>
            <column name="icon" value="popicon.png"/>
            <column name="code" value="pop"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="9"/>
            <column name="name" value="Splitter"/>
            <column name="display_name" value="Splitter"/>
            <column name="table_name" value="ne_splitter"/>
            <column name="status" value="Active"/>
            <column name="icon" value="splittericon.png"/>
            <column name="code" value="splitter"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="10"/>
            <column name="name" value="Handhole"/>
            <column name="display_name" value="Handhole"/>
            <column name="table_name" value="ne_handhole"/>
            <column name="status" value="Active"/>
            <column name="icon" value="handhole.png"/>
            <column name="code" value="handhole"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="11"/>
            <column name="name" value="Manhole"/>
            <column name="display_name" value="Manhole"/>
            <column name="table_name" value="ne_manhole"/>
            <column name="status" value="Active"/>
            <column name="icon" value="manhole.png"/>
            <column name="code" value="manhole"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="12"/>
            <column name="name" value="Pole"/>
            <column name="display_name" value="Pole"/>
            <column name="table_name" value="ne_pole"/>
            <column name="status" value="Active"/>
            <column name="icon" value="pole.png"/>
            <column name="code" value="pole"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="13"/>
            <column name="name" value="Trench"/>
            <column name="display_name" value="Trench"/>
            <column name="table_name" value="ne_trench"/>
            <column name="status" value="Active"/>
            <column name="icon" value="trench.png"/>
            <column name="code" value="trench"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="14"/>
            <column name="name" value="Duct"/>
            <column name="display_name" value="Duct"/>
            <column name="table_name" value="ne_duct"/>
            <column name="status" value="Active"/>
            <column name="icon" value="duct.png"/>
            <column name="code" value="duct"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="15"/>
            <column name="name" value="Joint Closure"/>
            <column name="display_name" value="Joint Closure"/>
            <column name="table_name" value="ne_joint_closure"/>
            <column name="status" value="Active"/>
            <column name="icon" value="jointclosure.png"/>
            <column name="code" value="jointclosure"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="16"/>
            <column name="name" value="Survey Area"/>
            <column name="display_name" value="Survey Area"/>
            <column name="table_name" value="survey_area"/>
            <column name="status" value="Active"/>
            <column name="icon" value="surveyarea.png"/>
            <column name="code" value="surveyarea"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="17"/>
            <column name="name" value="Cdu"/>
            <column name="display_name" value="Cdu"/>
            <column name="table_name" value="ne_cdu"/>
            <column name="status" value="Active"/>
            <column name="icon" value="cdu.png"/>
            <column name="code" value="cdu"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="18"/>
            <column name="name" value="Mdu"/>
            <column name="display_name" value="Mdu"/>
            <column name="table_name" value="ne_mdu"/>
            <column name="status" value="Active"/>
            <column name="icon" value="mdu.png"/>
            <column name="code" value="mdu"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="19"/>
            <column name="name" value="Sdu"/>
            <column name="display_name" value="Sdu"/>
            <column name="table_name" value="ne_sdu"/>
            <column name="status" value="Active"/>
            <column name="icon" value="sdu.png"/>
            <column name="code" value="sdu"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="20"/>
            <column name="name" value="Fdt"/>
            <column name="display_name" value="Fdt"/>
            <column name="table_name" value="ne_fdt"/>
            <column name="status" value="Active"/>
            <column name="icon" value="fdt.png"/>
            <column name="code" value="fdt"/>
        </insert>
    </changeSet>

    <changeSet id="layer_master_000002" author="initial-setup">
        <insert tableName="layer_master">
            <column name="id" valueNumeric="21"/>
            <column name="name" value="Olt"/>
            <column name="display_name" value="Olt"/>
            <column name="table_name" value="ne_olt"/>
            <column name="status" value="Active"/>
            <column name="icon" value="olt.png"/>
            <column name="code" value="olt"/>
        </insert>

        <insert tableName="layer_master">
            <column name="id" valueNumeric="22"/>
            <column name="name" value="Odf"/>
            <column name="display_name" value="Odf"/>
            <column name="table_name" value="ne_odf"/>
            <column name="status" value="Active"/>
            <column name="icon" value="odf.png"/>
            <column name="code" value="odf"/>
        </insert>
    </changeSet>

    <changeSet id="layermapping000001" author="initial-setup">

        <!-- Step 1: Create Table -->
		<createTable tableName="layer_mapping">
            <column name="mapping_id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="parent_layer_id" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="child_layer_id" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="timestamptz" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="timestamptz"/>
        </createTable>

        <!-- Step 2: Constraints -->
        <addForeignKeyConstraint baseTableName="layer_mapping"
                                 baseColumnNames="parent_layer_id"
                                 referencedTableName="layer_master"
                                 referencedColumnNames="id"
                                 constraintName="fk_layer_mapping_parent"/>
        <addForeignKeyConstraint baseTableName="layer_mapping"
                                 baseColumnNames="child_layer_id"
                                 referencedTableName="layer_master"
                                 referencedColumnNames="id"
                                 constraintName="fk_layer_mapping_child"/>
        <addUniqueConstraint tableName="layer_mapping"
                             columnNames="parent_layer_id, child_layer_id"
                             constraintName="unique_parent_child_pair"/>
        <sql>
            ALTER TABLE layer_mapping
            ADD CONSTRAINT no_self_reference CHECK (parent_layer_id != child_layer_id);
        </sql>
    </changeSet>

    <changeSet id="administrative000003" author="initial-setup">
        <!-- Create Table -->
		<createTable tableName="administrative_00">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="adm0_name" type="varchar(50)" />
            <column name="adm0_code" type="varchar(50)" />
            <column name="geom" type="geometry(MultiPolygon,4326)" />
        </createTable>

        <!-- Create GIST Index on geometry column -->
        <sql>
            CREATE INDEX administrative_00_geom_idx
            ON administrative_00
            USING GIST (geom);
        </sql>

        <sqlFile
                path="data/administrative_00.sql"
                relativeToChangelogFile="true"
                splitStatements="false"
                endDelimiter=";"/>

    </changeSet>

    <changeSet id="administrative000004" author="initial-setup">

        <!-- Create Table -->
		<createTable tableName="administrative_01">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="adm1_name" type="varchar(50)"/>
            <column name="adm1_code" type="varchar(50)"/>
            <column name="adm0_name" type="varchar(50)"/>
            <column name="adm0_code" type="varchar(50)"/>
            <column name="geom" type="geometry(MultiPolygon,4326)"/>
        </createTable>

        <!-- Create GIST Index -->
        <sql>
            CREATE INDEX administrative_01_geom_idx
            ON administrative_01
            USING GIST (geom);
        </sql>

        <!-- Add adm0_id column -->
        <addColumn tableName="administrative_01">
            <column name="adm0_id" type="INT"/>
        </addColumn>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint
                baseTableName="administrative_01"
                baseColumnNames="adm0_id"
                constraintName="fk_adm0_id"
                referencedTableName="administrative_00"
                referencedColumnNames="id"/>


    <sqlFile
                path="data/administrative_01.sql"
                relativeToChangelogFile="true"
                splitStatements="false"
                endDelimiter=";"/>



    </changeSet>

    <changeSet id="administrative000005" author="initial-setup">

        <!-- Step 1: Create Table -->
		<createTable tableName="administrative_02">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="adm2_name" type="varchar(50)"/>
            <column name="adm2_code" type="varchar(50)"/>
            <column name="adm0_name" type="varchar(50)"/>
            <column name="adm0_code" type="varchar(50)"/>
            <column name="adm1_name" type="varchar(50)"/>
            <column name="adm1_code" type="varchar(50)"/>
            <column name="geom" type="geometry(MultiPolygon,4326)"/>
        </createTable>

        <!-- Step 2: Create GIST Index on geom -->
        <sql>
            CREATE INDEX administrative_02_geom_idx
            ON administrative_02
            USING GIST (geom);
        </sql>

        <!-- Step 3: Add adm0_id and adm1_id columns -->
        <addColumn tableName="administrative_02">
            <column name="adm0_id" type="INT"/>
            <column name="adm1_id" type="INT"/>
        </addColumn>

        <!-- Step 4: Add foreign key constraints -->
        <addForeignKeyConstraint
                baseTableName="administrative_02"
                baseColumnNames="adm0_id"
                referencedTableName="administrative_00"
                referencedColumnNames="id"
                constraintName="fk_adm0_id_02"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="administrative_02"
                baseColumnNames="adm1_id"
                referencedTableName="administrative_01"
                referencedColumnNames="id"
                constraintName="fk_adm1_id_02"
                onDelete="CASCADE"/>

        <sqlFile
                path="data/administrative_02.sql"
                relativeToChangelogFile="true"
                splitStatements="false"
                endDelimiter=";"/>

        <sql>
            UPDATE administrative_02 a2
            SET adm0_id = a0.id
            FROM administrative_00 a0
            WHERE a2.adm0_name = a0.adm0_name AND a2.adm0_code = a0.adm0_code;

            UPDATE administrative_02 a2
            SET adm1_id = a1.id
            FROM administrative_01 a1
            WHERE a2.adm1_name = a1.adm1_name AND a2.adm1_code = a1.adm1_code;
        </sql>

    </changeSet>

</databaseChangeLog>
