<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="layer_inventory_mapping_001" author="initial-setup">
        <createTable tableName="layer_inventory_mapping">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="layer_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="layer_code" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="parent_param_id" type="INT"/>
            <column name="param_id" type="INT"/>
            <column name="param_name" type="TEXT"/>
            <column name="param_value" type="TEXT"/>
            <column name="is_mandatory" type="BOOLEAN"/>
            <column name="is_accessory" type="BOOLEAN"/>
            <column name="quantity" type="INT" defaultValueNumeric="0"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="CURRENT_TIMESTAMP"/>
        </createTable>
    </changeSet>

    <changeSet id="layer_inventory_mapping_000002" author="initial-setup">
        <addColumn tableName="layer_inventory_mapping">
            <column name="is_configuration" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="layer_inventory_mapping_000003" author="initial-setup">
        <addColumn tableName="layer_inventory_mapping">
            <column name="status" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
    <!--add required indexes in future-->

</databaseChangeLog>