<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="bom000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="bom_master"/></not>
                <not><tableExists tableName="bom_version"/></not>
            </or>
        </preConditions>
        <!-- BOM Master Table -->
		<createTable tableName="bom_master">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
        </createTable>

        <!-- Foreign Key: bom_master.survey_area_id -->
        <addForeignKeyConstraint
                baseTableName="bom_master"
                baseColumnNames="survey_area_id"
                referencedTableName="survey_area"
                referencedColumnNames="id"
                constraintName="fk_bom_master_survey_area"/>

        <!-- Status Check Constraint for bom_master -->
        <sql>
            ALTER TABLE bom_master
            ADD CONSTRAINT chk_bom_master_status
            CHECK (status IN ('Active', 'Inactive'));
        </sql>

        <!-- BOM Version Table -->
		<createTable tableName="bom_version">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="bom_master_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="version_no" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="file_name" type="TEXT"/>
            <column name="file_path" type="TEXT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
        </createTable>

        <!-- Foreign Key: bom_version.bom_master_id -->
        <addForeignKeyConstraint
                baseTableName="bom_version"
                baseColumnNames="bom_master_id"
                referencedTableName="bom_master"
                referencedColumnNames="id"
                constraintName="fk_bom_version_bom_master"/>

        <!-- Unique Constraint for version_no per master -->
        <addUniqueConstraint
                tableName="bom_version"
                columnNames="bom_master_id, version_no"
                constraintName="uk_bom_version_unique_combination"/>

    </changeSet>

    <changeSet id="bom000002" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="bom_version" columnName="is_active"/>
            </not>
        </preConditions>

        <addColumn tableName="bom_version">
            <column name="is_active" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>