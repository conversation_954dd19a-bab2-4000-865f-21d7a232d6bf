<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="street000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_street"/></not>
                <not><tableExists tableName="audit_ne_street"/></not>
                <not><tableExists tableName="audit_ne_street_geom"/></not>
            </or>
        </preConditions>

        <!-- Main Table -->
        <createTable tableName="ne_street">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="length_m" type="NUMERIC"/>
            <column name="code" type="VARCHAR">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR">
                <constraints nullable="false"/>
            </column>
            <column name="one_way" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="surface" type="VARCHAR"/>
            <column name="status" type="VARCHAR"/>
            <column name="geom" type="geometry(LineString,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <addUniqueConstraint tableName="ne_street" columnNames="custom_id" constraintName="uk_ne_street_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_street" baseColumnNames="adm1_id" referencedTableName="administrative_01" referencedColumnNames="id" constraintName="fk_ne_street_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_street" baseColumnNames="adm2_id" referencedTableName="administrative_02" referencedColumnNames="id" constraintName="fk_ne_street_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_street" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_street_survey_area_id"/>


        <sql>ALTER TABLE ne_street ADD CONSTRAINT chk_ne_street_surface CHECK (surface IN ('Asphalt', 'Concrete', 'Gravel', 'Cobblestone', 'Dirt', 'Paved', 'Unpaved'));</sql>
        <sql>ALTER TABLE ne_street ADD CONSTRAINT chk_ne_street_type CHECK (type IN ('Residential', 'Highway' ,'Arterial', 'Collector', 'Alley', 'Pedestrian', 'Service road'));</sql>

        <createTable tableName="audit_ne_street">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_street_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_street ADD CONSTRAINT chk_audit_ne_street_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

        <createTable tableName="audit_ne_street_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(LineString,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_street_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_street" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_street_geom"/>

        <sql>CREATE INDEX idx_ne_street_geom ON ne_street USING GIST (geom);</sql>
        <createIndex indexName="idx_ne_street_public_id" tableName="ne_street">
            <column name="public_id"/>
        </createIndex>
        <createIndex indexName="idx_ne_street_custom_id" tableName="ne_street">
            <column name="custom_id"/>
        </createIndex>

        <!-- Indexes -->


        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_street() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            affected_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            affected_id := OLD.id;
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            affected_id := NEW.id;
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_street (ne_street_id, operation, operated_by, operated_at, snapshot)
            VALUES (affected_id, TG_OP, user_id, NOW(), snapshot_data)
            RETURNING id INTO affected_id;

            INSERT INTO audit_ne_street_geom (audit_id, geom)
            VALUES (affected_id, CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END);

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>
<!--        <sql>CREATE TRIGGER trg_audit_ne_street AFTER INSERT OR UPDATE OR DELETE ON ne_street FOR EACH ROW EXECUTE FUNCTION audit_ne_street();</sql>-->
<!--        <sql>DROP TRIGGER IF EXISTS trg_ne_street_bi_row_custom_id ON ne_street;</sql>-->
<!--        <sql>CREATE TRIGGER trg_ne_street_bi_row_custom_id BEFORE INSERT ON ne_street FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();</sql>-->
<!--        <sql>DROP TRIGGER IF EXISTS trg_ne_street_bu_geom_custom_id ON ne_street;</sql>-->
<!--        <sql>CREATE TRIGGER trg_ne_street_bu_geom_custom_id BEFORE UPDATE OF geom ON ne_street FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();</sql>-->
<!--        <sql>DROP TRIGGER IF EXISTS trg_ne_street_bi_row_adm_ids ON ne_street;</sql>-->
<!--        <sql>CREATE TRIGGER trg_ne_street_bi_row_adm_ids BEFORE INSERT ON ne_street FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();</sql>-->
<!--        <sql>DROP TRIGGER IF EXISTS trg_ne_street_bu_geom_adm_ids ON ne_street;</sql>-->
<!--        <sql>CREATE TRIGGER trg_ne_street_bu_geom_adm_ids BEFORE UPDATE OF geom ON ne_street FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();</sql>-->

        <sql>CREATE TRIGGER trg_audit_ne_street AFTER INSERT OR UPDATE OR DELETE ON ne_street FOR EACH ROW EXECUTE FUNCTION audit_ne_street();</sql>
        <sql>CREATE TRIGGER trg_ne_street_bi_row_custom_id BEFORE INSERT ON ne_street FOR EACH ROW EXECUTE FUNCTION update_custom_id_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_street_bu_geom_custom_id BEFORE UPDATE OF geom ON ne_street FOR EACH ROW EXECUTE FUNCTION update_custom_id_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_street_bi_row_adm_ids BEFORE INSERT ON ne_street FOR EACH ROW EXECUTE FUNCTION update_adm_ids_linestring();</sql>
        <sql>CREATE TRIGGER trg_ne_street_bu_geom_adm_ids BEFORE UPDATE OF geom ON ne_street FOR EACH ROW EXECUTE FUNCTION update_adm_ids_linestring();</sql>

    </changeSet>
</databaseChangeLog>
