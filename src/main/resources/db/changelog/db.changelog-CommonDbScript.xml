<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


    <!-- Generic function to generate field(custom_id) for all table's geom(Points)-->
    <changeSet id="vishal-001" author="initial-setup">
        <sql><![CDATA[
CREATE OR REPLACE FUNCTION update_custom_id_generic()
RETURNS TRIGGER AS '
DECLARE
    feature_point geometry;
    adm1 TEXT;
    adm2 TEXT;
    layer_code TEXT;
    last_seq INT;
    next_seq TEXT;
    layer_table TEXT := TG_TABLE_NAME;
    sql TEXT;
BEGIN
    feature_point := NEW.geom;

    SELECT a.adm1_code, a.adm2_code
    INTO adm1, adm2
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, feature_point)
    LIMIT 1;

    SELECT UPPER(code)
    INTO layer_code
    FROM layer_master
    WHERE table_name = layer_table
    LIMIT 1;

    sql := format(
        ''SELECT COALESCE(MAX(SPLIT_PART(custom_id, ''''-'''', 4)::INT), 0)
         FROM %I WHERE custom_id LIKE ''''%%%s%%%%'''' '',
         layer_table, adm2
    );
    EXECUTE sql INTO last_seq;

    next_seq := LPAD((last_seq + 1)::TEXT, 3, ''0'');
    NEW.custom_id := layer_code || ''-'' || adm1 || ''-'' || adm2 || ''-'' || next_seq;

    RETURN NEW;
END;
' LANGUAGE plpgsql;
]]></sql>
    </changeSet>

    <!-- Generic function to generate fields(adm1_id, adm2_id) for all table's geom(Points) -->
    <changeSet id="vishal-002" author="initial-setup">
        <sql><![CDATA[
CREATE OR REPLACE FUNCTION update_administrative_ids_generic()
RETURNS TRIGGER AS '
DECLARE
    pointVar geometry;
    adm1_id INTEGER;
    adm2_id INTEGER;
BEGIN
    pointVar := NEW.geom;

    SELECT a.adm1_id, a.id
    INTO adm1_id, adm2_id
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, pointVar)
    LIMIT 1;

    NEW.adm1_id := adm1_id;
    NEW.adm2_id := adm2_id;

    RETURN NEW;
END;
' LANGUAGE plpgsql;
]]></sql>
    </changeSet>

    <!-- Function: custom_id
     Get First point from LineString and locate point into administrative_02-->
    <changeSet id="vishal-003" author="initial-setup">
        <sql><![CDATA[
CREATE OR REPLACE FUNCTION update_custom_id_linestring()
RETURNS TRIGGER AS '
DECLARE
    start_point geometry;
    adm1 TEXT;
    adm2 TEXT;
    layer_code TEXT;
    last_seq INT;
    next_seq TEXT;
    table_name_var TEXT := TG_TABLE_NAME;
BEGIN
    start_point := ST_StartPoint(NEW.geom);

    SELECT a.adm1_code, a.adm2_code
    INTO adm1, adm2
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    SELECT UPPER(lm.code)
    INTO layer_code
    FROM layer_master lm
    WHERE lm.table_name = table_name_var
    LIMIT 1;

    EXECUTE format(
        ''SELECT COALESCE(MAX(SPLIT_PART(custom_id, ''''-'''', 4)::INT), 0)
          FROM %I WHERE custom_id LIKE $1'',
        table_name_var
    )
    INTO last_seq
    USING ''%'' || adm2 || ''%'';

    next_seq := LPAD((last_seq + 1)::TEXT, 3, ''0'');
    NEW.custom_id := layer_code || ''-'' || adm1 || ''-'' || adm2 || ''-'' || next_seq;

    RETURN NEW;
END;
' LANGUAGE plpgsql;
]]></sql>
    </changeSet>

    <!-- Function: adm1_id, adm2_id
    Get administrative_01(id) and administrative_02(id) based on first point of LineString -->
    <changeSet id="vishal-004" author="initial-setup">
        <sql><![CDATA[
CREATE OR REPLACE FUNCTION update_adm_ids_linestring()
RETURNS TRIGGER AS '
DECLARE
    start_point geometry;
    adm1_id INTEGER;
    adm2_id INTEGER;
    table_name_var TEXT := TG_TABLE_NAME;
BEGIN
    start_point := ST_StartPoint(NEW.geom);

    SELECT a.adm1_id, a.id
    INTO adm1_id, adm2_id
    FROM administrative_02 a
    WHERE ST_Contains(a.geom, start_point)
    LIMIT 1;

    NEW.adm1_id := adm1_id;
    NEW.adm2_id := adm2_id;

    RETURN NEW;
END;
' LANGUAGE plpgsql;
]]></sql>
    </changeSet>

</databaseChangeLog>

