<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="olt000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_olt"/></not>
                <not><tableExists tableName="audit_ne_olt"/></not>
                <not><tableExists tableName="audit_ne_olt_geom"/></not>
            </or>
        </preConditions>

        <!-- Main table -->
        <createTable tableName="ne_olt">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="optical_level" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="up_link_protection" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="power_backup" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="vendor" type="VARCHAR(255)"/>
            <column name="model" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="slots" type="INT"/>
            <column name="active_ports" type="INT"/>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_olt" columnNames="custom_id" constraintName="uk_ne_olt_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_olt" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id" constraintName="fk_ne_olt_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_olt" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id" constraintName="fk_ne_olt_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_olt" baseColumnNames="survey_area_id"
                                 referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_olt_survey_area_id"/>

        <!-- Audit Tables -->
        <createTable tableName="audit_ne_olt">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_olt_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_olt ADD CONSTRAINT chk_audit_ne_olt_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

        <createTable tableName="audit_ne_olt_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_olt_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_olt" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_olt_geom_audit_id"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_olt_geom ON ne_olt USING GIST (geom);</sql>
        <createIndex tableName="ne_olt" indexName="idx_ne_olt_public_id">
            <column name="public_id"/>
        </createIndex>
        <createIndex tableName="ne_olt" indexName="idx_ne_olt_custom_id">
            <column name="custom_id"/>
        </createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_olt() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            audit_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_olt (
            ne_olt_id, operation, operated_by, operated_at, snapshot
            ) VALUES (
            COALESCE(NEW.id, OLD.id), TG_OP, user_id, NOW(), snapshot_data
            ) RETURNING id INTO audit_id;

            INSERT INTO audit_ne_olt_geom (audit_id, geom)
            VALUES (
            audit_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_olt
            AFTER INSERT OR UPDATE OR DELETE ON ne_olt
            FOR EACH ROW EXECUTE FUNCTION audit_ne_olt();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_olt_bi_row_custom_id ON ne_olt;
            CREATE TRIGGER trg_ne_olt_bi_row_custom_id
            BEFORE INSERT ON ne_olt
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_olt_bu_geom_custom_id ON ne_olt;
            CREATE TRIGGER trg_ne_olt_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_olt
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_olt_bi_row_adm_ids ON ne_olt;
            CREATE TRIGGER trg_ne_olt_bi_row_adm_ids
            BEFORE INSERT ON ne_olt
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_olt_bu_geom_adm_ids ON ne_olt;
            CREATE TRIGGER trg_ne_olt_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_olt
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>
    </changeSet>

    <changeSet id="olt_tray_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="olt_tray"/>
            </not>
        </preConditions>

        <createTable tableName="olt_tray">
            <column name="id" type="INT4" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="olt_id" type="INT4">
                <constraints nullable="false" foreignKeyName="fk_olt_tray_olt" referencedTableName="ne_olt" referencedColumnNames="id"/>
            </column>

            <column name="tray_name" type="VARCHAR(50)"/>

            <column name="tray_status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>

            <column name="port_capacity" type="INT"/>
            <column name="description" type="TEXT"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
        </createTable>

        <sql>
            ALTER TABLE olt_tray ADD CONSTRAINT chk_tray_status CHECK (tray_status IN ('Available', 'Used', 'Reserved', 'Faulty'));
        </sql>
    </changeSet>


</databaseChangeLog>
