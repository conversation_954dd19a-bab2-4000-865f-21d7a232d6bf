<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="fdt000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_fdt"/></not>
                <not><tableExists tableName="audit_ne_fdt"/></not>
                <not><tableExists tableName="audit_ne_fdt_geom"/></not>
            </or>
        </preConditions>
        <!-- Main Table -->
        <createTable tableName="ne_fdt">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT"/>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="core_number" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="card_slot_number" type="INT">
                <constraints nullable="false"/>
            </column>
            <!-- Passive Optical Network port -->
            <column name="pon_port" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_fdt" columnNames="custom_id" constraintName="uk_ne_fdt_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_fdt" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_fdt_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_fdt" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_fdt_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_fdt" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_fdt_survey_area_id"/>



        <!-- Audit Tables -->
        <createTable tableName="audit_ne_fdt">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_fdt_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_fdt ADD CONSTRAINT chk_audit_ne_fdt_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

        <createTable tableName="audit_ne_fdt_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_fdt_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_fdt" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_fdt_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_fdt_geom ON ne_fdt USING GIST (geom);</sql>
        <createIndex indexName="idx_ne_fdt_public_id" tableName="ne_fdt">
            <column name="public_id"/>
        </createIndex>
        <createIndex indexName="idx_ne_fdt_custom_id" tableName="ne_fdt">
            <column name="custom_id"/>
        </createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_fdt() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            affected_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            affected_id := OLD.id;
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            affected_id := NEW.id;
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_fdt (
            ne_fdt_id, operation, operated_by, operated_at, snapshot
            ) VALUES (
            affected_id, TG_OP, user_id, NOW(), snapshot_data
            ) RETURNING id INTO affected_id;

            INSERT INTO audit_ne_fdt_geom (
            audit_id, geom
            ) VALUES (
            affected_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_fdt
            AFTER INSERT OR UPDATE OR DELETE ON ne_fdt
            FOR EACH ROW
            EXECUTE FUNCTION audit_ne_fdt();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdt_bi_row_custom_id ON ne_fdt;
            CREATE TRIGGER trg_ne_fdt_bi_row_custom_id
            BEFORE INSERT ON ne_fdt
            FOR EACH ROW
            EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdt_bu_geom_custom_id ON ne_fdt;
            CREATE TRIGGER trg_ne_fdt_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_fdt
            FOR EACH ROW
            EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdt_bi_row_adm_ids ON ne_fdt;
            CREATE TRIGGER trg_ne_fdt_bi_row_adm_ids
            BEFORE INSERT ON ne_fdt
            FOR EACH ROW
            EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            DROP TRIGGER IF EXISTS trg_ne_fdt_bu_geom_adm_ids ON ne_fdt;
            CREATE TRIGGER trg_ne_fdt_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_fdt
            FOR EACH ROW
            EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>

</databaseChangeLog>