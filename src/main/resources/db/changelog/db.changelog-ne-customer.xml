<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="customer000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_customer"/></not>
                <not><tableExists tableName="audit_ne_customer"/></not>
                <not><tableExists tableName="audit_ne_customer_geom"/></not>
            </or>
        </preConditions>
        <!-- Main Table -->
		<createTable tableName="ne_customer">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT"/>
            <column name="port" type="INT"/>
            <column name="activation_date" type="DATE"/>
            <column name="customer_type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_customer" columnNames="custom_id" constraintName="uk_ne_customer_custom_id"/>
        <addForeignKeyConstraint baseTableName="ne_customer" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_customer_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_customer" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_customer_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_customer" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_customer_survey_area_id"/>
        <sql>
            ALTER TABLE ne_customer ADD CONSTRAINT chk_ne_customer_port CHECK (port > 0);
        </sql>
        <sql>
            ALTER TABLE ne_customer ADD CONSTRAINT chk_ne_customer_type CHECK (customer_type IN ('Residential', 'Enterprise', 'SMB'));
        </sql>


        <!-- Audit Tables -->
		<createTable tableName="audit_ne_customer">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_customer_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_customer ADD CONSTRAINT chk_audit_ne_customer_op CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_customer_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_customer_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_customer" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_customer_geom"/>

        <!-- Indexes -->
        <sql>CREATE INDEX idx_ne_customer_geom ON ne_customer USING GIST (geom);</sql>
        <createIndex tableName="ne_customer" indexName="idx_ne_customer_public_id">
            <column name="public_id"/>
        </createIndex>
        <createIndex tableName="ne_customer" indexName="idx_ne_customer_custom_id">
            <column name="custom_id"/>
        </createIndex>

        <!-- Function and Triggers -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_customer() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            audit_rec_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            audit_rec_id := OLD.id;
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            audit_rec_id := NEW.id;
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_customer (
            ne_customer_id, operation, operated_by, operated_at, snapshot
            ) VALUES (
            audit_rec_id, TG_OP, user_id, NOW(), snapshot_data
            ) RETURNING id INTO audit_rec_id;

            INSERT INTO audit_ne_customer_geom (
            audit_id, geom
            ) VALUES (
            audit_rec_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <!-- Triggers -->
        <sql>
            CREATE TRIGGER trg_audit_ne_customer
            AFTER INSERT OR UPDATE OR DELETE ON ne_customer
            FOR EACH ROW EXECUTE FUNCTION audit_ne_customer();

            DROP TRIGGER IF EXISTS trg_ne_customer_bi_row_custom_id ON ne_customer;
            CREATE TRIGGER trg_ne_customer_bi_row_custom_id
            BEFORE INSERT ON ne_customer
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            DROP TRIGGER IF EXISTS trg_ne_customer_bu_geom_custom_id ON ne_customer;
            CREATE TRIGGER trg_ne_customer_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_customer
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            DROP TRIGGER IF EXISTS trg_ne_customer_bi_row_adm_ids ON ne_customer;
            CREATE TRIGGER trg_ne_customer_bi_row_adm_ids
            BEFORE INSERT ON ne_customer
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();

            DROP TRIGGER IF EXISTS trg_ne_customer_bu_geom_adm_ids ON ne_customer;
            CREATE TRIGGER trg_ne_customer_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_customer
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>
</databaseChangeLog>