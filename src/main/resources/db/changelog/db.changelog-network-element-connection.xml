<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="network_element_connection_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="network_element_connection"/>
            </not>
        </preConditions>
        <createTable tableName="network_element_connection">
            <column name="id" type="SERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="cable_id" type="INT"/>
            <column name="core_number" type="INTEGER"/>
            <column name="from_layer_id" type="INTEGER"/>
            <column name="from_layer_type" type="TEXT"/>
            <column name="from_port_info" type="TEXT"/>
            <column name="to_layer_id" type="INTEGER"/>
            <column name="to_layer_type" type="TEXT"/>
            <column name="to_port_info" type="TEXT"/>
            <column name="closure_id" type="INTEGER"/>
            <column name="closure_type" type="TEXT"/>
            <column name="connection_type" type="TEXT"/>
            <column name="description" type="TEXT"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="network_element_connection"
                baseColumnNames="cable_id"
                referencedTableName="ne_cable"
                referencedColumnNames="id"
                constraintName="fk_network_element_connection_cable"/>

        <sql>
            ALTER TABLE network_element_connection ADD CONSTRAINT chk_connection_type CHECK (connection_type IN ('Splice', 'Through'));
        </sql>

    </changeSet>

    <changeSet id="add-survey-area-and-mvno-columns" author="your-name">
        <addColumn tableName="network_element_connection">
            <column name="survey_area_id" type="INTEGER"/>
            <column name="mvno_id" type="INTEGER"/>
        </addColumn>

    </changeSet>


    <!--add required indexes in future-->

</databaseChangeLog>