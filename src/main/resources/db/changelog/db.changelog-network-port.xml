<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- Create table -->
    <changeSet id="network_port_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="network_port"/>
            </not>
        </preConditions>
        <createTable tableName="network_port">
            <column name="id" type="SERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="layer_id" type="INT4">
                <constraints nullable="false"/>
            </column>
            <column name="layer_type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="port_number" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="port_type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="port_status" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
        </createTable>

        <sql>
            ALTER TABLE network_port ADD CONSTRAINT chk_port_type CHECK (port_type IN ('Input', 'Output'));
            ALTER TABLE network_port ADD CONSTRAINT chk_port_status CHECK (port_status IN ('Available', 'Used', 'Reserved', 'Faulty'));
        </sql>
    </changeSet>

    <!--add required indexes in future-->

</databaseChangeLog>