<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="cable_specification_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="cable_specification"/>
            </not>
        </preConditions>
        <createTable tableName="cable_specification">
            <column name="id" type="serial">
                <constraints primaryKey="true"/>
            </column>
            <column name="name" type="TEXT">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="number_of_tubes" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="number_of_cores" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_on" type="TIMESTAMP" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMP"/>
        </createTable>
    </changeSet>

    <changeSet id="cable_specification_000002" author="initial-setup">
        <insert tableName="cable_specification">
            <column name="name" value="12F"/>
            <column name="number_of_tubes" valueNumeric="1"/>
            <column name="number_of_cores" valueNumeric="12"/>
            <column name="description" value="Standard 12 core fiber"/>
        </insert>
        <insert tableName="cable_specification">
            <column name="name" value="24F"/>
            <column name="number_of_tubes" valueNumeric="2"/>
            <column name="number_of_cores" valueNumeric="24"/>
            <column name="description" value="Standard 24 core fiber"/>
        </insert>
        <insert tableName="cable_specification">
            <column name="name" value="48F"/>
            <column name="number_of_tubes" valueNumeric="4"/>
            <column name="number_of_cores" valueNumeric="48"/>
            <column name="description" value="High-capacity feeder cable"/>
        </insert>
        <insert tableName="cable_specification">
            <column name="name" value="96F"/>
            <column name="number_of_tubes" valueNumeric="8"/>
            <column name="number_of_cores" valueNumeric="96"/>
            <column name="description" value="Used for large distribution areas"/>
        </insert>
    </changeSet>

    <changeSet id="cable_lookup_types_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="lookup_cable_types"/>
            </not>
        </preConditions>
        <createTable tableName="lookup_cable_types">
            <column name="id" type="serial">
                <constraints primaryKey="true"/>
            </column>
            <column name="name" type="TEXT">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_on" type="TIMESTAMP" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMP"/>
        </createTable>
    </changeSet>

    <changeSet id="cable_lookup_types_000002" author="initial-setup">
        <insert tableName="lookup_cable_types">
            <column name="id" valueNumeric="1"/>
            <column name="name" value="Feeder"/>
            <column name="description" value="Feeder"/>
        </insert>
        <insert tableName="lookup_cable_types">
            <column name="id" valueNumeric="2"/>
            <column name="name" value="Distribution"/>
            <column name="description" value="Distribution"/>
        </insert>
        <insert tableName="lookup_cable_types">
            <column name="id" valueNumeric="3"/>
            <column name="name" value="Drop"/>
            <column name="description" value="Drop"/>
        </insert>
    </changeSet>

    <changeSet id="ne_cable_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="ne_cable"/>
            </not>
        </preConditions>
        <createTable tableName="ne_cable">
            <column name="id" type="serial">
                <constraints primaryKey="true"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="mounting_type" type="TEXT"/>
            <column name="status" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="cable_type_id" type="INT"/>
            <column name="specification_id" type="INT"/>
            <column name="measured_length_m" type="NUMERIC(10,2)"/>
            <column name="gis_length_m" type="NUMERIC(10,2)"/>
            <column name="installation_date" type="TIMESTAMPTZ"/>
            <column name="remarks" type="TEXT"/>
            <column name="geom" type="geometry(LineString,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="port_count" type="INT"/>
            <column name="parant_ne_id" type="INT"/>
            <column name="parant_ne_type" type="TEXT"/>
        </createTable>
    </changeSet>

    <!-- ========== ne_cable FKs & Constraints ========== -->
    <changeSet id="ne_cable_000002" author="initial-setup">
        <addForeignKeyConstraint baseTableName="ne_cable" baseColumnNames="cable_type_id"
                                 referencedTableName="lookup_cable_types" referencedColumnNames="id"
                                 constraintName="fk_ne_cable_cable_type"/>
        <addForeignKeyConstraint baseTableName="ne_cable" baseColumnNames="specification_id"
                                 referencedTableName="cable_specification" referencedColumnNames="id"
                                 constraintName="fk_ne_cable_specification"/>
        <addForeignKeyConstraint baseTableName="ne_cable" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_cable_adm1"/>
        <addForeignKeyConstraint baseTableName="ne_cable" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_cable_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_cable" baseColumnNames="survey_area_id"
                                 referencedTableName="survey_area" referencedColumnNames="id"
                                 constraintName="fk_ne_cable_survey_area"/>
        <sql>ALTER TABLE ne_cable ADD CONSTRAINT chk_ne_cable_mounting_type CHECK (mounting_type IN ('Underground', 'Overhead'));</sql>
    </changeSet>

    <!-- ========== Audit Tables ========== -->
    <changeSet id="ne_cable_000003" author="initial-setup">
        <createTable tableName="audit_ne_cable">
            <column name="id" type="serial"><constraints primaryKey="true"/></column>
            <column name="ne_cable_id" type="INT"><constraints nullable="false"/></column>
            <column name="operation" type="TEXT"><constraints nullable="false"/></column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>ALTER TABLE audit_ne_cable ADD CONSTRAINT chk_audit_op CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));</sql>

        <createTable tableName="audit_ne_cable_geom">
            <column name="id" type="serial"><constraints primaryKey="true"/></column>
            <column name="audit_id" type="INT"><constraints nullable="false"/></column>
            <column name="geom" type="geometry(LineString,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_cable_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_cable" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_geom"/>
    </changeSet>

    <!-- ========== Indexes ========== -->
    <changeSet id="ne_cable_000004" author="initial-setup">
        <sql>CREATE INDEX idx_ne_cable_geom ON ne_cable USING GIST (geom);</sql>
        <createIndex tableName="ne_cable" indexName="idx_ne_cable_custom_id">
            <column name="custom_id"/>
        </createIndex>
        <createIndex tableName="ne_cable" indexName="idx_ne_cable_public_id">
            <column name="public_id"/>
        </createIndex>
        <createIndex tableName="ne_cable" indexName="idx_ne_cable_specification_id">
            <column name="specification_id"/>
        </createIndex>
        <createIndex tableName="ne_cable" indexName="idx_ne_cable_cable_type_id">
            <column name="cable_type_id"/>
        </createIndex>
    </changeSet>

    <!-- ========== Functions and Triggers ========== -->
    <changeSet id="ne_cable_000005" author="initial-setup">
        <!-- Functions and Triggers -->
        <sql>
            <![CDATA[
            CREATE OR REPLACE FUNCTION trg_audit_ne_cable() RETURNS TRIGGER AS
            E'
            DECLARE
                snapshot_data JSONB;
                inserted_id INT;
                user_id BIGINT;
            BEGIN
                IF (TG_OP = ''DELETE'') THEN
                    snapshot_data := to_jsonb(OLD) - ''geom'';
                    user_id := COALESCE(OLD.modified_by, OLD.created_by);
                    INSERT INTO audit_ne_cable(ne_cable_id, operation, operated_by, snapshot)
                    VALUES (OLD.id, TG_OP, user_id, snapshot_data)
                    RETURNING id INTO inserted_id;
                    INSERT INTO audit_ne_cable_geom (audit_id, geom) VALUES (inserted_id, OLD.geom);
                    RETURN OLD;
                ELSIF (TG_OP = ''UPDATE'') THEN
                    snapshot_data := to_jsonb(NEW) - ''geom'';
                    user_id := COALESCE(NEW.modified_by, NEW.created_by);
                    INSERT INTO audit_ne_cable(ne_cable_id, operation, operated_by, snapshot)
                    VALUES (NEW.id, TG_OP, user_id, snapshot_data)
                    RETURNING id INTO inserted_id;
                    INSERT INTO audit_ne_cable_geom (audit_id, geom) VALUES (inserted_id, NEW.geom);
                    RETURN NEW;
                ELSIF (TG_OP = ''INSERT'') THEN
                    snapshot_data := to_jsonb(NEW) - ''geom'';
                    user_id := NEW.created_by;
                    INSERT INTO audit_ne_cable(ne_cable_id, operation, operated_by, snapshot)
                    VALUES (NEW.id, TG_OP, user_id, snapshot_data)
                    RETURNING id INTO inserted_id;
                    INSERT INTO audit_ne_cable_geom (audit_id, geom) VALUES (inserted_id, NEW.geom);
                    RETURN NEW;
                END IF;
            END;
            ' LANGUAGE plpgsql;

            CREATE TRIGGER trg_audit_ne_cable
            AFTER INSERT OR UPDATE OR DELETE ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION trg_audit_ne_cable();

            CREATE OR REPLACE FUNCTION update_ne_cable_gis_length()
            RETURNS TRIGGER AS
            E'
            BEGIN
                NEW.gis_length_m := ST_Length(NEW.geom::geography);
                RETURN NEW;
            END;
            ' LANGUAGE plpgsql;

            CREATE TRIGGER trg_ne_cable_bi_row_gis_length
            BEFORE INSERT ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_ne_cable_gis_length();

            CREATE TRIGGER trg_ne_cable_bu_geom_gis_length
            BEFORE UPDATE OF geom ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_ne_cable_gis_length();

            CREATE TRIGGER trg_ne_cable_bi_row_custom_id
            BEFORE INSERT ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_custom_id_linestring();

            CREATE TRIGGER trg_ne_cable_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_custom_id_linestring();

            CREATE TRIGGER trg_ne_cable_bi_row_adm_ids
            BEFORE INSERT ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_adm_ids_linestring();

            CREATE TRIGGER trg_ne_cable_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_cable
            FOR EACH ROW
            EXECUTE FUNCTION update_adm_ids_linestring();
            ]]>
        </sql>
    </changeSet>

    <changeSet id="cable_core_000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="cable_core"/>
            </not>
        </preConditions>

        <createTable tableName="cable_core">
            <column name="id" type="INT4" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="cable_id" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="core_number" type="INT"/>
            <column name="core_color" type="VARCHAR(50)"/>
            <column name="core_status" type="VARCHAR(50)"/>
            <column name="tube_number" type="INT"/>
            <column name="splice_type" type="VARCHAR(50)"/>
            <column name="splice_status" type="VARCHAR(50)"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="cable_core"
                baseColumnNames="cable_id"
                referencedTableName="ne_cable"
                referencedColumnNames="id"
                constraintName="fk_network_element_connection_cable"
                onDelete="CASCADE"/>

        <sql>
            ALTER TABLE cable_core ADD CONSTRAINT chk_cable_core_core_status
            CHECK (core_status IN ('Available', 'Used', 'Reserved', 'Faulty'));
        </sql>
    </changeSet>
</databaseChangeLog>