<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create tblmroles table -->
    <changeSet id="role000001" author="Vishal Shah">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tblmroles"/>
            </not>
        </preConditions>

        <createTable tableName="tblmroles">
            <column name="roleid" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true"
                             primaryKeyName="tblmroles_roleid_pk"
                             nullable="false"
                             unique="true"
                             uniqueConstraintName="tblmroles_roleid_unq"/>
            </column>
            <column name="rolename" type="VARCHAR(250)"/>
            <column name="rstatus" type="VARCHAR(100)"/>
            <column name="is_delete" type="TINYINT(1)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="sysrole" type="TINYINT(1)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="createbyname" type="VARCHAR(100)"/>
            <column name="updatebyname" type="VARCHAR(100)"/>
            <column name="createdbystaffid" type="BIGINT"/>
            <column name="lastmodifiedbystaffid" type="BIGINT"/>
            <column name="createdate" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
            <column name="lastmodifieddate" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
            <column name="MVNOID" type="BIGINT"/>
            <column name="lcoid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="product" type="VARCHAR(100)" defaultValue="Gis">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="tblmroles"
                             columnNames="roleid"
                             constraintName="tblmroles_roleid_unq"/>
    </changeSet>


    <!-- Convert is_delete column to boolean -->
    <changeSet id="role000002" author="Vishal Shah">
        <dropDefaultValue tableName="tblmroles" columnName="is_delete"/>
        <sql>
            ALTER TABLE tblmroles
            ALTER COLUMN is_delete TYPE boolean
            USING (CASE WHEN is_delete = 1 THEN true ELSE false END);
        </sql>
        <addDefaultValue tableName="tblmroles" columnName="is_delete" defaultValueBoolean="false"/>
    </changeSet>

    <!-- Convert sysrole column to boolean -->
    <changeSet id="role000003" author="Vishal Shah">
        <dropDefaultValue tableName="tblmroles" columnName="sysrole"/>
        <sql>
            ALTER TABLE tblmroles
            ALTER COLUMN sysrole TYPE boolean
            USING (CASE WHEN sysrole = 1 THEN true ELSE false END);
        </sql>
        <addDefaultValue tableName="tblmroles" columnName="sysrole" defaultValueBoolean="false"/>
    </changeSet>

    <changeSet id="role000004" author="Arpit Raval">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM tblmroles WHERE roleid = 1;
            </sqlCheck>
        </preConditions>

        <insert tableName="tblmroles">
            <!-- Don't insert roleid since it's auto-generated -->
            <column name="mvnoid" valueNumeric="1"/>
            <column name="rolename" value="Admin"/>
            <column name="rstatus" value="Active"/>
            <column name="is_delete" valueBoolean="false"/>
            <column name="sysrole" valueBoolean="true"/>
            <column name="createbyname" value="admin admin"/>
            <column name="updatebyname" value="admin admin"/>
            <column name="createdbystaffid" valueNumeric="1"/>
            <column name="lastmodifiedbystaffid" valueNumeric="1"/>
            <column name="createdate" valueDate="2021-10-03T18:53:48"/>
            <column name="lastmodifieddate" valueDate="2021-10-03T18:53:48"/>
            <column name="lcoid" valueNumeric="1"/> <!-- REQUIRED by schema -->
            <column name="product" value="Gis"/>   <!-- default, but explicitly added -->
        </insert>
    </changeSet>

</databaseChangeLog>
