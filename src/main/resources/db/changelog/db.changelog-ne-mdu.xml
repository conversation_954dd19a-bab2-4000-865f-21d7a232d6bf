<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="mdu000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_mdu"/></not>
                <not><tableExists tableName="audit_ne_mdu"/></not>
            </or>
        </preConditions>
        <!-- Create ne_mdu main table -->
		<createTable tableName="ne_mdu">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT"/>
            <column name="home_passes" type="INT"/>
            <column name="floors" type="INT"/>
            <column name="towers" type="INT"/>
            <column name="tenancy" type="TEXT"/>
            <column name="category" type="TEXT"/>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="remarks" type="TEXT"/>
            <column name="fat_no" type="TEXT"/>
            <column name="fdt_no" type="TEXT"/>
            <column name="optical_level" type="TEXT"/>
            <column name="photo" type="TEXT"/>
            <column name="street_name" type="TEXT"/>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_mdu" columnNames="custom_id" constraintName="uk_ne_mdu_custom_id"/>

        <addForeignKeyConstraint baseTableName="ne_mdu" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id"
                                 constraintName="fk_ne_mdu_adm1"/>

        <addForeignKeyConstraint baseTableName="ne_mdu" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id"
                                 constraintName="fk_ne_mdu_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_mdu" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_mdu_survey_area_id"/>


        <!-- Create audit_ne_mdu table -->
		<createTable tableName="audit_ne_mdu">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_mdu_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_mdu ADD CONSTRAINT chk_audit_ne_mdu_operation
            CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>
        <!-- Create audit_ne_mdu_geom table -->
		<createTable tableName="audit_ne_mdu_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_mdu_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_mdu" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_mdu_geom_audit_id"/>

        <!-- Indexes -->
        <sql>
            CREATE INDEX idx_ne_mdu_geom ON ne_mdu USING GIST (geom);
        </sql>

        <createIndex tableName="ne_mdu" indexName="idx_ne_mdu_public_id">
            <column name="public_id"/>
        </createIndex>

        <createIndex tableName="ne_mdu" indexName="idx_ne_mdu_custom_id">
            <column name="custom_id"/>
        </createIndex>

        <!-- Triggers and Trigger Functions -->
        <sql splitStatements="false">
            CREATE OR REPLACE FUNCTION audit_ne_mdu()
            RETURNS TRIGGER AS $$
            DECLARE
            snapshot_data JSONB;
            affected_audit_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = 'DELETE') THEN
            snapshot_data := to_jsonb(OLD) - 'geom';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - 'geom';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_mdu (
            ne_mdu_id, operation, operated_by, operated_at, snapshot
            ) VALUES (
            COALESCE(NEW.id, OLD.id), TG_OP, user_id, NOW(), snapshot_data
            ) RETURNING id INTO affected_audit_id;

            INSERT INTO audit_ne_mdu_geom (
            audit_id, geom
            ) VALUES (
            affected_audit_id,
            CASE WHEN TG_OP = 'DELETE' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER trg_audit_ne_mdu
            AFTER INSERT OR UPDATE OR DELETE ON ne_mdu
            FOR EACH ROW EXECUTE FUNCTION audit_ne_mdu();

            CREATE TRIGGER trg_ne_mdu_bi_row_custom_id
            BEFORE INSERT ON ne_mdu
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            CREATE TRIGGER trg_ne_mdu_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_mdu
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();

            CREATE TRIGGER trg_ne_mdu_bi_row_adm_ids
            BEFORE INSERT ON ne_mdu
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();

            CREATE TRIGGER trg_ne_mdu_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_mdu
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>

    <changeSet id="mdu000002" author="initial-setup">
        <addColumn tableName="ne_mdu">
            <column name="riser" type="TEXT"/>
            <column name="olt_name" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
