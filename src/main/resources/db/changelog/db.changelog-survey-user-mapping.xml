<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="surveyusermap000001" author="initial-setup">
        <!-- Main Table -->
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="survey_user_mapping"/>
            </not>
        </preConditions>
		<createTable tableName="survey_user_mapping">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
        </createTable>

        <!-- Foreign Key -->
        <addForeignKeyConstraint
                baseTableName="survey_user_mapping"
                baseColumnNames="survey_area_id"
                referencedTableName="survey_area"
                referencedColumnNames="id"
                constraintName="fk_survey_user_mapping_survey_area"/>

    </changeSet>

    <changeSet id="surveyusermap000002-add-unique" author="initial-setup">
        <addUniqueConstraint
                tableName="survey_user_mapping"
                columnNames="survey_area_id"
                constraintName="uq_survey_unique_assignment"/>
    </changeSet>
</databaseChangeLog>