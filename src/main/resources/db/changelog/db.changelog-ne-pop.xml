<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="pop000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <or>
                <not><tableExists tableName="ne_pop"/></not>
                <not><tableExists tableName="audit_ne_pop"/></not>
                <not><tableExists tableName="audit_ne_pop_geom"/></not>
            </or>
        </preConditions>
        <!-- Main table -->
		<createTable tableName="ne_pop">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="custom_id" type="TEXT"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT"/>
            <column name="category" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TEXT" >
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="parent_ne_id" type="INT"/>
            <column name="parent_ne_type" type="TEXT"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="survey_area_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Constraints -->
        <addUniqueConstraint tableName="ne_pop" columnNames="custom_id" constraintName="uk_ne_pop_custom_id"/>

        <addForeignKeyConstraint baseTableName="ne_pop" baseColumnNames="adm1_id"
                                 referencedTableName="administrative_01" referencedColumnNames="id" constraintName="fk_ne_pop_adm1"/>

        <addForeignKeyConstraint baseTableName="ne_pop" baseColumnNames="adm2_id"
                                 referencedTableName="administrative_02" referencedColumnNames="id" constraintName="fk_ne_pop_adm2"/>
        <addForeignKeyConstraint baseTableName="ne_pop" baseColumnNames="survey_area_id" referencedTableName="survey_area" referencedColumnNames="id" constraintName="fk_ne_pop_survey_area_id"/>

        <!-- Check constraints -->
        <sql>
            ALTER TABLE ne_pop ADD CONSTRAINT chk_ne_pop_category CHECK (category IN ('Tier1', 'Tier2', 'Tier3'));
        </sql>


        <!-- Audit Tables -->
		<createTable tableName="audit_ne_pop">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ne_pop_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="operated_by" type="BIGINT"/>
            <column name="operated_at" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="snapshot" type="JSONB"/>
        </createTable>

        <sql>
            ALTER TABLE audit_ne_pop ADD CONSTRAINT chk_audit_ne_pop_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'));
        </sql>

		<createTable tableName="audit_ne_pop_geom">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="geom" type="geometry(Point,4326)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="audit_ne_pop_geom" baseColumnNames="audit_id"
                                 referencedTableName="audit_ne_pop" referencedColumnNames="id"
                                 onDelete="CASCADE" constraintName="fk_audit_ne_pop_geom_audit_id"/>

        <!-- Indexes -->
        <sql>
            CREATE INDEX idx_ne_pop_geom ON ne_pop USING GIST (geom);
        </sql>

        <createIndex tableName="ne_pop" indexName="idx_ne_pop_public_id">
            <column name="public_id"/>
        </createIndex>

        <createIndex tableName="ne_pop" indexName="idx_ne_pop_custom_id">
            <column name="custom_id"/>
        </createIndex>

        <!-- Triggers & audit function -->
        <sql>
            CREATE OR REPLACE FUNCTION audit_ne_pop() RETURNS TRIGGER AS
            E'
            DECLARE
            snapshot_data JSONB;
            audit_id INT;
            user_id BIGINT;
            BEGIN
            IF (TG_OP = ''DELETE'') THEN
            snapshot_data := to_jsonb(OLD) - ''geom'';
            user_id := COALESCE(OLD.modified_by, OLD.created_by);
            ELSE
            snapshot_data := to_jsonb(NEW) - ''geom'';
            user_id := COALESCE(NEW.modified_by, NEW.created_by);
            END IF;

            INSERT INTO audit_ne_pop (
            ne_pop_id, operation, operated_by, operated_at, snapshot
            ) VALUES (
            COALESCE(NEW.id, OLD.id), TG_OP, user_id, NOW(), snapshot_data
            ) RETURNING id INTO audit_id;

            INSERT INTO audit_ne_pop_geom (audit_id, geom)
            VALUES (
            audit_id,
            CASE WHEN TG_OP = ''DELETE'' THEN OLD.geom ELSE NEW.geom END
            );

            RETURN NULL;
            END;
            ' LANGUAGE plpgsql;
        </sql>

        <sql>
            CREATE TRIGGER trg_audit_ne_pop
            AFTER INSERT OR UPDATE OR DELETE ON ne_pop
            FOR EACH ROW EXECUTE FUNCTION audit_ne_pop();
        </sql>

        <sql>
            CREATE TRIGGER trg_ne_pop_bi_row_custom_id
            BEFORE INSERT ON ne_pop
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            CREATE TRIGGER trg_ne_pop_bu_geom_custom_id
            BEFORE UPDATE OF geom ON ne_pop
            FOR EACH ROW EXECUTE FUNCTION update_custom_id_generic();
        </sql>

        <sql>
            CREATE TRIGGER trg_ne_pop_bi_row_adm_ids
            BEFORE INSERT ON ne_pop
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

        <sql>
            CREATE TRIGGER trg_ne_pop_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON ne_pop
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>
</databaseChangeLog>
