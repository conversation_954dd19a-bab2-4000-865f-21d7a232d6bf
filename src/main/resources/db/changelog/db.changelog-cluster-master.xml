<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="cluster000001" author="initial-setup">

        <!-- Table Definition -->
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="cluster_master"/>
            </not>
        </preConditions>
		<createTable tableName="cluster_master">
            <column name="id" type="serial">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="public_id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="status" type="TEXT"/>
            <column name="geom" type="geometry(Polygon,4326)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMPTZ" defaultValueComputed="NOW()"/>
            <column name="modified_by" type="BIGINT"/>
            <column name="modified_on" type="TIMESTAMPTZ"/>
            <column name="adm1_id" type="INT"/>
            <column name="adm2_id" type="INT"/>
            <column name="mvno_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Foreign Keys -->
        <addForeignKeyConstraint
                baseTableName="cluster_master"
                baseColumnNames="adm1_id"
                referencedTableName="administrative_01"
                referencedColumnNames="id"
                constraintName="fk_cluster_master_adm1"/>

        <addForeignKeyConstraint
                baseTableName="cluster_master"
                baseColumnNames="adm2_id"
                referencedTableName="administrative_02"
                referencedColumnNames="id"
                constraintName="fk_cluster_master_adm2"/>

        <!-- Check Constraint: valid geometry -->
        <sql>
            ALTER TABLE cluster_master ADD CONSTRAINT chk_cluster_master_geom_valid CHECK (ST_IsValid(geom));
        </sql>

        <!-- Indexes -->
        <sql>CREATE INDEX cluster_master_geom_idx ON cluster_master USING GIST (geom);</sql>
        <createIndex indexName="idx_cluster_master_public_id_idx" tableName="cluster_master">
            <column name="public_id"/>
        </createIndex>

        <!-- Triggers -->
        <sql>
            DROP TRIGGER IF EXISTS trg_cluster_master_bi_row_adm_ids ON cluster_master;
            CREATE TRIGGER trg_cluster_master_bi_row_adm_ids
            BEFORE INSERT ON cluster_master
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();

            DROP TRIGGER IF EXISTS trg_cluster_master_bu_geom_adm_ids ON cluster_master;
            CREATE TRIGGER trg_cluster_master_bu_geom_adm_ids
            BEFORE UPDATE OF geom ON cluster_master
            FOR EACH ROW EXECUTE FUNCTION update_administrative_ids_generic();
        </sql>

    </changeSet>
</databaseChangeLog>