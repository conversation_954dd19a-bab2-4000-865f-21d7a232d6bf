<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!--    Create Staff Master Table-->
    <changeSet id="staff0000000001" author="Arpit">
        <preConditions onFail ="MARK_RAN">
            <not>
                <tableExists tableName="tblmstaffuser"></tableExists>
            </not>
        </preConditions>
        <createTable tableName="tblmstaffuser">
            <column name="staffid" type="bigint">
                <constraints nullable="false" primaryKey="true"
                             primaryKeyName="tblmstaffuser_staffid_pk" unique="true"
                             uniqueConstraintName="tblmstaffuser_staffid_unq"/>
            </column>
            <column name="username" type="varchar(250)"></column>
            <column name="password" type="varchar(500)"></column>
            <column name="firstname" type="varchar(250)"></column>
            <column name="lastname" type="varchar(250)"></column>
            <column name="sstatus" type="varchar(100)"></column>
            <column name="last_login_time" type="TIMESTAMP"></column>
            <column name="partnerid" type="bigint">
            </column>
            <column name="is_delete" type="tinyint(1)" defaultValue="0">
            </column>
            <column name="MVNOID" type="bigint"></column>
            <column name="branchid" type="bigint"></column>
            <column name="createbyname" type="varchar(100)"></column>
            <column name="updatebyname" type="varchar(100)"></column>
            <column name="createdbystaffid" type="bigint">
            </column>
            <column name="lastmodifiedbystaffid" type="bigint">
            </column>
            <column name="createdate" type="TIMESTAMP"
                    defaultValueComputed="CURRENT_TIMESTAMP">
            </column>
            <column name="lastmodifieddate" type="TIMESTAMP"
                    defaultValueComputed="CURRENT_TIMESTAMP">
            </column>
            <column name="roleid" type="bigint"></column>
        </createTable>
    </changeSet>
    <changeSet id="staff0000000002" author="Arpit">
        <addColumn tableName="tblmstaffuser">
            <column name="service_area_id" type="bigint"/>
            <column name="parent_staff_id" type="bigint"/>
            <column name="country_code" type="varchar(10)"/>
            <column name="total_collected" type="numeric(20,4)"/>
            <column name="total_transferred" type="numeric(20,4)"/>
            <column name="available_amount" type="numeric(20,4)"/>
            <column name="lcoid" type="bigint"/>
            <column name="hrms_id" type="varchar(50)"/>
            <column name="profile_image" type="bytea"/>
            <column name="department" type="varchar(100)"/>
            <column name="oldpassword1" type="varchar(100)"/>
            <column name="oldpassword2" type="varchar(100)"/>
            <column name="oldpassword3" type="varchar(100)"/>
            <column name="otp" type="varchar(10)"/>
            <column name="otpvalidate" type="timestamp"/>
            <column name="sysstaff" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="businessunitid" type="bigint"/>
            <column name="email" type="varchar(100)"/>
            <column name="phone" type="varchar(20)"/>
            <column name="failcount" type="bigint"/>
            <column name="access_level_group_name" type="varchar(255)"/>
            <column name="mvno_deactivation_flag" type="boolean"/>
            <column name="uuid" type="varchar(255)"/>
            <column name="is_password_expired" type="boolean" defaultValueBoolean="false"/>
            <column name="password_date" type="timestamp"/>
        </addColumn>
    </changeSet>
    <changeSet id="staff0000000003" author="Arpit Raval">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM tblmstaffuser WHERE staffid=1;
            </sqlCheck>
        </preConditions>
        <insert tableName="tblmstaffuser">
            <column name="staffid" value="1"></column>
            <column name="username" value="superadmin"/>
            <column name="password" value="$2a$10$.ICxzN70ViqoPCIgC/POSOkxYJG8UjvcPv13oUnzqWGbKvEEJEL0K"/>
            <column name="firstname" value="superadmin"/>
            <column name="lastname" value="superadmin"/>
            <column name="MVNOID" value="1"/>
            <column name="sstatus" value="ACTIVE"/>
            <column name="last_login_time" value="NULL"/>
            <column name="partnerid" value="1"/>
            <column name="is_delete" value="0"/>
            <column name="createbyname" value="admin admin"/>
            <column name="updatebyname" value="admin admin"/>
            <column name="createdbystaffid" value="1"/>
            <column name="lastmodifiedbystaffid" value="1"/>
            <column name="createdate" value="2021-10-03 18:53:48"/>
            <column name="lastmodifieddate" value="2021-10-03 18:53:48"/>
            <column name="roleid" value="1"/>
        </insert>
    </changeSet>
    <changeSet id="staff0000000004" author="Arpit Raval">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM tblmstaffuser WHERE staffid=2;
            </sqlCheck>
        </preConditions>
        <insert tableName="tblmstaffuser">
            <column name="staffid" value="2"/>
            <column name="username" value="admin"/>
            <column name="password" value="$2a$10$vZnz0KJY7052BllpxqHNPuEyfszEk6ZkZdgntxTgm8FvvMaxHX4oO"/>
            <column name="firstname" value="admin"/>
            <column name="lastname" value="admin"/>
            <column name="sstatus" value="ACTIVE"/>
            <column name="last_login_time" value="NULL"/>
            <column name="partnerid" value="1"/>
            <column name="is_delete" value="0"/>
            <column name="createbyname" value="admin admin"/>
            <column name="updatebyname" value="admin admin"/>
            <column name="createdbystaffid" value="1"/>
            <column name="lastmodifiedbystaffid" value="1"/>
            <column name="createdate" value="2021-10-03 18:53:48"/>
            <column name="MVNOID" value="2"/>
            <column name="lastmodifieddate" value="2021-10-03 18:53:48"/>
            <column name="roleid" value="1"/>
        </insert>
    </changeSet>
    <changeSet id="staff0000000005" author="Vishal Shah">
        <!-- Step 1: Drop default value -->
        <dropDefaultValue tableName="tblmstaffuser" columnName="is_delete"/>
        <!-- Step 2: Alter column type using custom SQL -->
        <sql>
            ALTER TABLE tblmstaffuser
            ALTER COLUMN is_delete TYPE boolean
            USING (CASE WHEN is_delete = 1 THEN true ELSE false END);
        </sql>
        <!-- Step 3: Optionally set new default value -->
        <addDefaultValue tableName="tblmstaffuser" columnName="is_delete" defaultValueBoolean="false"/>
    </changeSet>

</databaseChangeLog>