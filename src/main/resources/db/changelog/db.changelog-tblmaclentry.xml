<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="ACLEY000000000" author="Vishal Shah">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tblmaclentry"/>
            </not>
        </preConditions>

        <createTable tableName="tblmaclentry">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"
                             unique="true" uniqueConstraintName="tblmaclentry_id_unq"
                             primaryKeyName="tblmaclentry_id_pk"/>
            </column>
            <column name="roleid" type="bigint"/>
            <column name="code" type="varchar(100)"/>
            <column name="menuid" type="bigint"/>
            <column name="product" type="varchar(100)" defaultValue="BSS">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="tblmaclentry"
                          columnName="id" columnDataType="bigint"
                          startWith="1" incrementBy="1"/>

        <createIndex tableName="tblmaclentry" indexName="aclentry_role_code_index">
            <column name="roleid"/>
            <column name="code"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
