<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="arpit-001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="liquibase_test"/>
            </not>
        </preConditions>
        <createTable tableName="liquibase_test">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="username" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="layerImageMapping000001" author="initial-setup">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="layer_image_mapping"/>
            </not>
        </preConditions>

        <createTable tableName="layer_image_mapping">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="file_name" type="TEXT"/>
            <column name="layer_id" type="INTEGER"/>
            <column name="layer_code" type="TEXT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
