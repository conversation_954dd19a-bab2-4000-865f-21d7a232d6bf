1. Provide search functionality on map. (location search and self_data search)
2. We have to focus on African countries map on our project.
3. Map should load when click on Map menu after user login page.

4. Import shp file in db.
-> We have to find the way to run all type of geometry data while using the command to upload shp file.
5. Jwt token
-> Generate and validate at the time of login and deactivate at the time of logout.
6. We have to manage icon in layer_master for all layers and it should worked in windows and linux server.
7. Change in SecurityConfig file to apply authentication on all incoming request accept login and register. Uncomment code for prod build and comment development code line.
8. Question : How can we add any (POP, FAT, FDP) to end point or start point (At exact same point at the start/end of cable) for cable From A to B?
Answer : Snapping (Gis concept)