server:
  port: ${PORT:30082}

spring:
  application:
    name: GisCore-Service
  liquibase:
    change-log: classpath:/db/changelog/db.changelog-master.xml

  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: gis-staff-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: com.keyanna.gis.core.dto,com.keyanna.gis.core.Kafka
        spring.json.value.default.type: com.keyanna.gis.core.Kafka.KafkaMessageData
    listener:
      missing-topics-fatal: false

eureka:
  instance:
    instance-id: ${spring.application.name}-${server.port}
    prefer-ip-address: true
  client:
    fetchRegistry: true
    register-with-eureka: true
    serviceUrl:
      defaultZone: http://127.0.0.1:8761/eureka/

